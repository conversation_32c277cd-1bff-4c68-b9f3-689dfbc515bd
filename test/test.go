package test

import (
	"context"
	"net/http"
	"ros-api-go/internal/config"
	"ros-api-go/internal/sms"
	"testing"

	"ros-api-go/internal/wirex"

	"github.com/gavv/httpexpect/v2"
	"github.com/gin-gonic/gin"
)

const (
	baseAPI = "/api/v1"
)

var (
	app *gin.Engine
)

func init() {
	config.MustLoad("./", "local")

	ctx := context.Background()
	injector, _, err := wirex.BuildInjector(ctx)
	if err != nil {
		panic(err)
	}

	app = gin.New()
	err = injector.App.Init(ctx, injector.DB)
	if err != nil {
		panic(err)
	}
	// 初始化短信管理器
	err = sms.InitSMSManager(injector.Redis)
	if err != nil {
		panic(err)
	}
}

func tester(t *testing.T) *httpexpect.Expect {
	return httpexpect.WithConfig(httpexpect.Config{
		Client: &http.Client{
			Transport: httpexpect.NewBinder(app),
			Jar:       httpexpect.NewCookieJar(),
		},
		Reporter: httpexpect.NewAssertReporter(t),
		Printers: []httpexpect.Printer{
			httpexpect.NewDebugPrinter(t, true),
		},
	})
}

# SMS 发送功能测试文档

## 测试用例说明

### 1. 用户密码短信测试 (TestSendUserPasswordSMS)
- 功能：测试发送用户密码短信
- 测试数据：
  - 手机号：13139800070
  - 密码：123456
- 预期结果：短信发送成功，无错误返回

### 2. 忘记密码短信测试 (TestSendForgotPasswordSMS)
- 功能：测试发送忘记密码验证码短信
- 测试数据：
  - 手机号：13139800070
- 预期结果：
  - 短信发送成功
  - 返回有效的批次ID
  - 无错误返回

### 3. 操作密码短信测试 (TestSendOperationPasswordSMS)
- 功能：测试发送操作密码验证码短信
- 测试数据：
  - 手机号：13139800070
- 预期结果：
  - 短信发送成功
  - 返回有效的批次ID
  - 无错误返回

### 4. 客户充值短信测试 (TestSendCustomerRechargeSMS)
- 功能：测试发送客户充值通知短信
- 测试数据：
  - 手机号：13139800070
  - 客户名称：测试
  - 充值金额：100.00
  - 余额：100.00
- 预期结果：短信发送成功，无错误返回

### 5. 客户支付短信测试 (TestSendCustomerPaySMS)
- 功能：测试发送客户支付通知短信
- 测试数据：
  - 手机号：13139800070
  - 客户名称：测试
  - 支付金额：100.00
  - 余额：100.00
- 预期结果：短信发送成功，无错误返回

## 运行测试

```bash
# 运行所有SMS测试
go test -v ./test/sms

# 运行特定测试
go test -v ./test/sms -run TestSendUserPasswordSMS
```

## 注意事项

1. 测试前请确保SMS配置正确
2. 测试使用的手机号码为测试号码
3. 请注意短信发送频率限制
4. 建议在测试环境中运行测试
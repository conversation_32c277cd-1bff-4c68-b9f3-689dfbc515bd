package sms

import (
	"context"
	"ros-api-go/internal/sms"
	_ "ros-api-go/test"
	"testing"
)

// TestSendUserPasswordSMS 测试发送用户密码短信
func TestSendUserPasswordSMS(t *testing.T) {
	tests := []struct {
		name     string
		phone    string
		password string
	}{
		{
			name:     "发送用户密码短信",
			phone:    "13139800070",
			password: "123456",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := sms.SendUserPasswordSMS(tt.phone, tt.password)
			if err != nil {
				t.Errorf("SendUserPasswordSMS() error = %v", err)
			}
		})
	}
}

// TestSendForgotPasswordSMS 测试发送忘记密码短信
func TestSendForgotPasswordSMS(t *testing.T) {
	ctx := context.Background()
	tests := []struct {
		name  string
		phone string
	}{
		{
			name:  "发送忘记密码验证码短信",
			phone: "13139800070",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			batchID, err := sms.SendForgotPasswordSMS(ctx, tt.phone)
			if err != nil {
				t.Errorf("SendForgotPasswordSMS() error = %v", err)
			}
			if batchID == "" {
				t.Error("SendForgotPasswordSMS() batchID is empty")
			}
		})
	}
}

// TestSendOperationPasswordSMS 测试发送操作密码短信
func TestSendOperationPasswordSMS(t *testing.T) {
	ctx := context.Background()
	tests := []struct {
		name  string
		phone string
	}{
		{
			name:  "发送操作密码验证码短信",
			phone: "13139800070",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			batchID, err := sms.SendOperationPasswordSMS(ctx, tt.phone)
			if err != nil {
				t.Errorf("SendOperationPasswordSMS() error = %v", err)
			}
			if batchID == "" {
				t.Error("SendOperationPasswordSMS() batchID is empty")
			}
		})
	}
}

// TestSendCustomerRechargeSMS 测试发送客户充值短信
func TestSendCustomerRechargeSMS(t *testing.T) {
	tests := []struct {
		name         string
		phone        string
		customerName string
		rechargeAmt  string
		balanceAmt   string
	}{
		{
			name:         "发送客户充值通知短信",
			phone:        "13139800070",
			customerName: "测试",
			rechargeAmt:  "100.00",
			balanceAmt:   "100.00",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := sms.SendCustomerRechargeSMS(tt.phone, tt.customerName, tt.rechargeAmt, tt.balanceAmt)
			if err != nil {
				t.Errorf("SendCustomerRechargeSMS() error = %v", err)
			}
		})
	}
}

// TestSendCustomerPaySMS 测试发送客户支付短信
func TestSendCustomerPaySMS(t *testing.T) {
	tests := []struct {
		name         string
		phone        string
		customerName string
		payAmt       string
		balanceAmt   string
	}{
		{
			name:         "发送客户支付通知短信",
			phone:        "13139800070",
			customerName: "测试",
			payAmt:       "100.00",
			balanceAmt:   "100.00",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := sms.SendCustomerPaySMS(tt.phone, tt.customerName, tt.payAmt, tt.balanceAmt)
			if err != nil {
				t.Errorf("SendCustomerPaySMS() error = %v", err)
			}
		})
	}
}

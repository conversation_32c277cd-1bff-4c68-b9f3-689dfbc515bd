package print

import (
	"net"
	"ros-api-go/internal/printer"
	"testing"
	"time"
)

func TestPrint(t *testing.T) {
	printText := printer.NewPrintText(false)

	printText.SetTextType(printer.TEXT_TYPE_TITLE).
		SetAlign(printer.ALIGN_CENTER).
		Text("标题").
		SetTextType(printer.TEXT_TYPE_BODY).
		SetAlign(printer.ALIGN_LEFT)
	printText.Text("换台人 : ", "布拉")
	printText.Text("原台 : ", "名字")
	printText.Text("订单号 : ", "202506011182")
	printText.Text("时间 : ", "2025-06-06 12:00")
	printText.Line()
	printText.Text("目标台 : ", "一号桌")
	printText.Line()
	printText.TextAlign("你好1", "你好2", "你好3", "你好4")
	printText.TextAlign("你好1", "你好2", "你好3", "")
	printText.TextAlign("你好1", "你好2", "", "")

	printText.Feed(3)

	printText2 := printer.NewPrintText(false)
	printText2.SetLang("ug")
	printText2.SetTextType(printer.TEXT_TYPE_TITLE).
		SetAlign(printer.ALIGN_CENTER).
		Text("تاماق").
		SetTextType(printer.TEXT_TYPE_BODY).
		SetAlign(printer.ALIGN_RIGHT)
	printText2.Text("ئالماشتۇرغۇچى : ", "布拉")
	printText2.Text("ئەسلى ئۈستەل : ", "名字")
	printText2.Text("زاكاس نومۇرى : ", "202506011182")
	printText2.Text("ۋاقىت : ", "2025-06-06 12:00")
	printText2.Line()
	printText2.Text("يۆتكەلگەن ئۈستەل : ", "一号桌")
	printText2.Line()
	printText2.TextAlign("ياخشىمۇ1", "ياخشىمۇ2", "ياخشىمۇ3", "ياخشىمۇ4")
	printText2.TextAlign("ياخشىمۇ1", "ياخشىمۇ2", "ياخشىمۇ3", "")
	printText2.TextAlign("ياخشىمۇ1", "ياخشىمۇ2", "", "")

	printText2.Feed(6).Cut()

	SendBytesOverTCP(printText.GetBytes())
	SendBytesOverTCP(printText2.GetBytes())
}

func SendBytesOverTCP(data []byte) error {
	// 定义连接地址和超时时间
	address := "*************:9100"
	timeout := 5 * time.Second

	// 建立TCP连接（带超时）
	conn, err := net.DialTimeout("tcp", address, timeout)
	if err != nil {
		return err
	}
	defer conn.Close() // 确保连接关闭

	// 设置发送超时
	if err := conn.SetWriteDeadline(time.Now().Add(timeout)); err != nil {
		return err
	}

	// 发送数据
	_, err = conn.Write(data)
	if err != nil {
		return err
	}

	return nil
}

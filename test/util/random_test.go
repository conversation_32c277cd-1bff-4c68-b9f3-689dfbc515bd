package util

import (
	"log"
	"ros-api-go/internal/consts"
	"ros-api-go/pkg/util"
	"sync"
	"testing"
)

// 测试SerialNumber函数
func TestSerialNumber(t *testing.T) {
	// 测试生成带前缀的序列号
	prefix := consts.OrderPaymentPrefix
	// 循环生成10000个序列号
	for i := 0; i < 10000; i++ {
		go aaaa(prefix)
	}
}

func aaaa(prefix string) {
	serial := util.SerialNumber(prefix)
	log.Println(serial)
}
func TestSerialNumberUniqueness(t *testing.T) {
	const (
		workers    = 10     // 并发协程数
		iterations = 100000 // 总生成量
	)

	var (
		mu    sync.Mutex
		ids   = make(map[string]struct{})
		wg    sync.WaitGroup
		dupCh = make(chan string, 10)
	)

	// 启动结果收集协程
	go func() {
		for id := range dupCh {
			mu.Lock()
			if _, exists := ids[id]; exists {
				t.<PERSON>rrorf("发现重复序列号: %s", id)
			}
			ids[id] = struct{}{}
			mu.Unlock()
			wg.Done()
		}
	}()

	// 并发生成序列号
	wg.Add(iterations)
	prefix := consts.OrderPaymentPrefix
	for i := 0; i < workers; i++ {
		go func() {
			for n := 0; n < iterations/workers; n++ {
				id := util.SerialNumber(prefix)
				dupCh <- id
			}
		}()
	}

	wg.Wait()
	close(dupCh)

	if len(ids) != iterations {
		t.Fatalf("唯一性验证失败，期望数量: %d, 实际数量: %d", iterations, len(ids))
	}
}

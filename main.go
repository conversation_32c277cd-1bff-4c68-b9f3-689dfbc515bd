package main

import (
	"os"

	"github.com/urfave/cli/v2"
	"ros-api-go/cmd"
)

// Usage: go build -ldflags "-X main.VERSION=x.x.x"
var VERSION = "v1.0.0"

// @title 智慧餐厅云端接口
// @version v1.0.0
// @description 智慧餐厅GO接口
// @securityDefinitions.apikey ApiTokenAuth
// @in header
// @name Authorization
// @securityDefinitions.apikey ServerTokenAuth
// @in header
// @name Authorization
// @securityDefinitions.apikey WechatTokenAuth
// @in header
// @name Authorization
// @schemes http https
// @basePath /
func main() {
	app := cli.NewApp()
	app.Name = "ros-api-go"
	app.Version = VERSION
	app.Usage = "智慧餐厅GO接口"
	app.Commands = []*cli.Command{
		cmd.StartCmd(),
		cmd.StopCmd(),
		cmd.VersionCmd(VERSION),
	}
	err := app.Run(os.Args)
	if err != nil {
		panic(err)
	}
}

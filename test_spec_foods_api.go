package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

// 测试保存规格美食关联关系的API
func main() {
	// 测试数据
	testData := map[string]interface{}{
		"spec_id": 99,
		"foods": []map[string]interface{}{
			{
				"id":        999,
				"price":     20.0,
				"vip_price": 18.0,
			},
		},
	}

	// 转换为JSON
	jsonData, err := json.Marshal(testData)
	if err != nil {
		fmt.Printf("JSON编码错误: %v\n", err)
		return
	}

	fmt.Printf("测试数据:\n%s\n", string(jsonData))
	fmt.Println("\n接口路径: POST /api/v2/foodSpecs/foods")
	fmt.Println("请求头: Content-Type: application/json")
	fmt.Println("需要认证: Authorization: Bearer <token>")
}

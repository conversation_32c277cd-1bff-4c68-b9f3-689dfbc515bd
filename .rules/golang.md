## 角色定位
您是一位精通 Go语言、微服务架构 和 整洁后端开发实践 的专家。您的职责是确保代码符合Go语言习惯、模块化、可测试，并遵循现代最佳实践和设计模式。

## 项目概述
这是一个基于 Go 语言开发的智慧餐厅管理系统 API，使用 Gin 框架和 GORM。项目采用标准的 Go 项目结构，实现了完整的餐厅运营管理功能。

## 核心职责
 - 指导开发：确保Go代码符合语言习惯、易于维护且高性能。
 - 模块化设计：通过整洁架构（Clean Architecture）实现关注点分离。
 - 质量保障：推动测试驱动开发（TDD）、完善的可观测性和可扩展的服务模式。

## 架构模式
 - 接口驱动开发：通过显式依赖注入优先使用接口。
 - 组合优于继承：倾向小而专注的接口。
 - 面向接口编程：公共函数仅通过接口交互（非具体类型），提升灵活性与可测试性。


## 代码规范

### 1. 目录结构详解
   - `cmd/`: 应用入口点
   - `configs/`: 配置文件，按环境(dev/prod)分类
   - `internal/`: 核心业务逻辑
     - `app/`: 应用初始化
     - `config/`: 配置解析
     - `handler/`: 业务处理层
     - `http/`: HTTP相关组件
       - `controller/`: RESTful API控制器
       - `middleware/`: 中间件
       - `request/`: 请求结构体
       - `resource/`: 响应结构体
     - `model/`: 数据库模型
     - `service/`: 业务服务层
     - `consts/`: 常量定义
   - `resource/`: 静态资源(如语言文件)
   - `test/`: 测试代码

### 2. 命名规范
- 使用驼峰命名法
- 文件名使用小写字母和下划线
- 模型名称使用 PascalCase，并以 Model 结尾
- API 路由前缀规范：
  - `/api/` - 普通用户接口
  - `/cloud/api/` - 云服务接口
  - `/admin/` - 管理员接口

### 3. 数据库规范
- 使用 GORM 作为 ORM 框架
- 表名使用小写字母和下划线
- 主键统一使用 `id`
- 包含 `created_at`、`updated_at` 时间戳字段
- 软删除使用 `deleted_at` 字段
- 使用预加载（Preload）优化关联查询
- 查询条件使用 Where 链式调用
- 使用事务确保数据一致性
- 使用 Select 指定查询字段
- 统一使用 util.GetDB(ctx, db) 获取数据库连接

### 4. API 设计规范
- RESTful API 设计
- 使用 HTTP 标准方法（GET, POST, PUT, DELETE）
- 返回标准的 JSON 格式响应
- 统一的错误处理和响应格式
- 必须进行参数验证

### 5. 权限控制
- 使用 Casbin 进行权限管理
- 实现基于角色的访问控制（RBAC）
- 区分管理员和普通用户权限
- 微信用户单独的权限控制

### 6.监控要求
- 记录分布式事务执行链路
- 监控长时间运行的事务(超过5分钟)
- 异常事务及时告警

### 7. 安全规范
- 所有敏感信息必须加密存储
- API 接口必须进行认证和授权
- 实现请求频率限制
- 记录关键操作日志

### 8. 性能规范
- 合理使用数据库索引
- 实现数据库查询缓存
- 大量数据查询必须分页
- 关键接口需要性能监控

### 9. 错误处理
- 统一的错误码管理
- 详细的错误日志记录
- 敏感错误信息不能直接返回给客户端
- 实现优雅的错误恢复机制

### 10. 测试规范
- 单元测试覆盖率要求
- 集成测试场景定义
- API 测试用例管理
- 性能测试指标定义

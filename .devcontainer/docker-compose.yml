version: "3"

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    command: sleep infinity
    networks:
      - db
      - redis
    volumes:
      - ../..:/workspaces:cached
    env_file:
      - .env

  db:
    image: postgres:15.3-alpine
    restart: unless-stopped
    ports:
      - 5432:5432
    networks:
      - db
    volumes:
      - postgres-data:/var/lib/postgresql/data
    env_file:
      - .env

  redis:
    image: redis:latest
    restart: unless-stopped
    ports:
      - 6379:6379
    networks:
      - redis

volumes:
  postgres-data:

networks:
  db:
  redis:
package handler

import (
	"context"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"
	"time"
)

type DebtRepaymentHandler struct {
	Trans                     util.Trans
	MerchantService           *service.MerchantService
	DebtHolderService         *service.DebtHolderService
	PaymentLogService         *service.PaymentLogService
	DebtRepaymentOrderService *service.DebtRepaymentOrderService
	DebtTransactionService    *service.DebtTransactionService
}

// HandleOnlinePaySuccess 线上支付成功
func (handler *DebtRepaymentHandler) HandleOnlinePaySuccess(
	ctx context.Context, holder *model.DebtHolderModel,
	repaymentOrder *model.DebtRepaymentOrderModel, paymentResult *schema.PaymentResult) error {
	// 更新支付信息
	err := handler.Trans.Exec(ctx, func(ctx context.Context) error {
		// 更新支付记录
		if _, err := handler.PaymentLogService.UpdatePaymentStatus(ctx, paymentResult); err != nil {
			return err
		}
		// 更新充值订单状态
		if err := handler.DebtRepaymentOrderService.UpdateStatus(ctx, repaymentOrder.ID, paymentResult.PaymentTypeID, model.DebtRepaymentOrderStatusSuccess); err != nil {
			return err
		}
		// 更新赊账人余额
		return handler.UpdateDebtHolderBalance(ctx, holder, repaymentOrder, paymentResult.PaymentTypeID)
	})
	if err != nil {
		return err
	}
	return nil
}

// UpdateDebtHolderBalance 更新余额
func (handler *DebtRepaymentHandler) UpdateDebtHolderBalance(
	ctx context.Context, holder *model.DebtHolderModel,
	repaymentOrder *model.DebtRepaymentOrderModel, paymentTypeId int64) error {
	// 记录消费日志
	transaction := &model.DebtTransactionModel{
		MerchantNo:    repaymentOrder.MerchantNo,
		HolderID:      repaymentOrder.HolderID,
		Type:          model.DebtTransactionTypeRepayment,
		OrderID:       &repaymentOrder.ID,
		OrderNo:       &repaymentOrder.No,
		Balance:       holder.Balance - repaymentOrder.Amount,
		Amount:        -1 * repaymentOrder.Amount, // 负数表示还款
		CashierID:     repaymentOrder.CashierID,
		PaymentTypeID: paymentTypeId,
	}
	if err := handler.DebtTransactionService.Create(ctx, transaction); err != nil {
		return err
	}
	// 更新余额
	return handler.DebtHolderService.DecreaseBalance(ctx, repaymentOrder.HolderID, repaymentOrder.Amount)
}

// CreateDebtRepaymentOrder 创建还款订单
func (handler *DebtRepaymentHandler) CreateDebtRepaymentOrder(
	ctx context.Context, formItem *request.DebtRepaymentRequest) (*model.DebtRepaymentOrderModel, error) {
	// 创建客户还款订单
	repaymentOrder := &model.DebtRepaymentOrderModel{
		No:            util.SerialNumber(consts.DebtRepaymentPrefix),
		MerchantNo:    formItem.MerchantNo,
		HolderID:      formItem.HolderID,
		Amount:        formItem.Amount,
		PaymentTypeID: formItem.PaymentTypeID,
		Status:        model.DebtRepaymentOrderStatusWaiting,
		CashierID:     formItem.CashierID,
	}

	// 保存还款订单
	err := handler.DebtRepaymentOrderService.Create(ctx, repaymentOrder)

	if err != nil {
		return nil, err
	}

	return repaymentOrder, nil
}

// CreateDebtRepaymentPaymentLog 创建还款订单
func (handler *DebtRepaymentHandler) CreateDebtRepaymentPaymentLog(
	ctx context.Context, repaymentOrder *model.DebtRepaymentOrderModel, tradeType string, clientIP string,
	merchant *model.MerchantModel) (*model.MerchantPaymentLogModel, error) {

	// 构造支付记录
	expiresAt := time.Now().Add(config.C.PaymentConfig.PaymentExpires * time.Second)
	paymentInfo := &model.MerchantPaymentModel{
		MerchantNo:    repaymentOrder.MerchantNo,
		OrderNo:       repaymentOrder.No,
		PaymentTypeID: repaymentOrder.PaymentTypeID,
		CustomerID:    repaymentOrder.HolderID,
		PaymentNo:     repaymentOrder.No,
		Amount:        repaymentOrder.Amount,
		ExpiresAt:     &expiresAt,
	}

	var paymentLog *model.MerchantPaymentLogModel
	var err error

	paymentLog, err = handler.PaymentLogService.CreatePaymentLog(ctx, merchant, paymentInfo, tradeType, consts.ORDER_TYPE_DEBT_REPAYMENT, clientIP)
	if err != nil {
		return nil, err
	}
	return paymentLog, nil
}

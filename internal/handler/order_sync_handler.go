package handler

import (
	"context"
	"go.uber.org/zap"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
)

type OrderSyncHandler struct {
	OrderCloudService *service.OrderCloudService
	OrderService      *service.OrderService
	Trans             *util.Trans
}

// SyncOrder 云订单(cloud_orders)同步到orders表
func (handler *OrderSyncHandler) SyncOrder(ctx context.Context, orderID int64, merchantNo string) {

	cloudOrder, err := handler.OrderCloudService.GetCloudOrderByID(ctx, orderID, merchantNo)
	if err != nil {
		logging.Context(ctx).Error(
			"[订单同步]获取云端订单失败",
			zap.String("merchant_no", merchantNo),
			zap.Int64("order_id", orderID),
			zap.Any("err", err),
		)
		return
	}
	if cloudOrder == nil {
		logging.Context(ctx).Error(
			"[订单同步]云端订单不存在",
			zap.String("merchant_no", merchantNo),
			zap.Int64("order_id", orderID),
		)
		return
	}
	// 如果状态不是3, 4, 5
	if cloudOrder.State != 3 && cloudOrder.State != 4 && cloudOrder.State != 5 {
		logging.Context(ctx).Info(
			"[订单同步]订单状态不是3, 4, 5, 无需同步",
			zap.String("merchant_no", merchantNo),
			zap.String("order_no", cloudOrder.No),
			zap.Int64("order_id", orderID),
			zap.Int64("state", cloudOrder.State),
		)
		return
	}
	if cloudOrder.IsSync {
		logging.Context(ctx).Info(
			"[订单同步]订单已同步, 无需重复同步",
			zap.String("merchant_no", merchantNo),
			zap.String("order_no", cloudOrder.No),
			zap.Int64("order_id", orderID),
		)
		return
	}

	err = handler.Trans.Exec(ctx, func(ctx context.Context) error {
		// 创建订单
		order, err := handler.OrderService.CreateFromCloudOrder(ctx, cloudOrder)
		if err != nil {
			return err
		}

		// 更新订单相关数据中的OrderID
		err = handler.OrderService.UpdateOrderRelatedIds(ctx, order.No, order.MerchantNo, order.ID)
		if err != nil {
			return err
		}

		// 更新订单同步状态
		err = handler.OrderCloudService.OrderSynced(ctx, orderID, merchantNo)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		logging.Context(ctx).Error(
			"[订单同步]订单同步失败",
			zap.String("merchant_no", merchantNo),
			zap.String("order_no", cloudOrder.No),
			zap.Int64("order_id", orderID),
			zap.Any("err", err))
		return
	}
	logging.Context(ctx).Info(
		"[订单同步]订单同步成功",
		zap.String("merchant_no", merchantNo),
		zap.String("order_no", cloudOrder.No),
		zap.Int64("order_id", orderID),
	)
}

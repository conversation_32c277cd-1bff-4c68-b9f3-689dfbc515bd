package handler

import (
	"context"
	"go.uber.org/zap"
	"ros-api-go/internal/broadcast/order_broadcast"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
)

type PaymentHandler struct {
	RpcHandler         *RpcHandler
	LocalServerService *service.LocalServerService
	MerchantService    *service.MerchantService
	OrderCloudService  *service.OrderCloudService
	OrderSyncHandler   *OrderSyncHandler
	PrintService       *service.PrintService
}

// SendPaymentResultToLocalServer 发送支付结果到本地服务
func (handler *PaymentHandler) SendPaymentResultToLocalServer(paymentLog *model.MerchantPaymentLogModel, paymentResult *schema.PaymentResult) {
	ctx := context.Background()
	go func() {
		// 处理panic error
		defer func() {
			if err := recover(); err != nil {
				// 记录日志
				logging.Context(ctx).Error("发送支付结果时发生 panic",
					zap.Any("err", err),
					zap.String("MerchantNo", paymentLog.MerchantNo),
					zap.String("PaymentNo", paymentLog.PaymentNo),
					zap.Any("PaymentResult", paymentResult),
				)
			}
		}()
		// 处理逻辑
		localServer, err := handler.LocalServerService.GetActiveServerByMerchantNo(ctx, paymentLog.MerchantNo)
		if err != nil {
			logging.Context(ctx).Error("发送支付结果失败：获取商户信息失败",
				zap.String("MerchantNo", paymentLog.MerchantNo),
				zap.String("PaymentNo", paymentLog.PaymentNo),
				zap.Any("PaymentResult", paymentResult),
				zap.Error(err),
			)
			return
		}
		if localServer == nil {
			logging.Context(ctx).Error("发送支付结果失败：商户未配置本地服务",
				zap.String("MerchantNo", paymentLog.MerchantNo),
				zap.String("PaymentNo", paymentLog.PaymentNo),
				zap.Any("PaymentResult", paymentResult),
			)
			return
		}
		bodyJson, _ := json.Marshal(paymentResult)

		var body map[string]interface{}

		err = json.Unmarshal(bodyJson, &body)
		if err != nil {
			logging.Context(ctx).Error("发送支付结果失败：解析支付结果失败",
				zap.String("MerchantNo", paymentLog.MerchantNo),
				zap.String("PaymentNo", paymentLog.PaymentNo),
				zap.Any("PaymentResult", paymentResult),
			)
		}

		clientID := util.Int64ToStr(localServer.ID)
		payload := Payload{
			Path:   "/local/api/v2/orders/scan/query/" + paymentLog.PaymentNo,
			Method: "POST",
			Headers: map[string]string{
				"MerchantNo": localServer.MerchantNo,
				"ClientId":   clientID,
			},
			Body: body,
		}

		payloadBody, _ := json.Marshal(payload)

		response, err := handler.RpcHandler.HandRpcWithServerToken(ctx, localServer.MerchantNo, clientID, payloadBody)
		if err != nil {
			logging.Context(ctx).Error("发送支付结果失败：RPC请求失败",
				zap.String("MerchantNo", paymentLog.MerchantNo),
				zap.String("PaymentNo", paymentLog.PaymentNo),
				zap.Any("PaymentResult", paymentResult),
				zap.Any("Response", response),
				zap.Error(err),
			)
			return
		}
	}()
}

// HandleScanOrder 处理扫码点菜通知
func (handler *PaymentHandler) HandleScanOrder(ctx context.Context, paymentLog *model.MerchantPaymentLogModel, paymentResult *schema.PaymentResult) error {

	merchant, err := handler.MerchantService.GetByMerchantNo(ctx, paymentLog.MerchantNo)
	if err != nil || merchant == nil {
		logging.Context(ctx).Error(
			"微信支付通知中获取商户信息失败",
			zap.String("merchantNo", paymentLog.MerchantNo),
			zap.String("paymentNo", paymentLog.PaymentNo),
			zap.Error(err),
		)
		return err
	}

	// 如果是本地模式, 则发送支付结果至本地服务器
	if merchant.Mode == model.MerchantModeLocal {
		// 异步发送支付结果到本地服务器
		handler.SendPaymentResultToLocalServer(paymentLog, paymentResult)
	} else {
		// 如果是在线模式，则试着更新扫码点菜订单状态
		order, err := handler.OrderCloudService.FinishScanOrder(ctx, paymentLog.OrderNo, paymentLog.MerchantNo, *paymentResult.PaidAt)
		if err != nil {
			logging.Context(ctx).Error(
				"微信支付通知中更新扫码点菜失败",
				zap.String("orderNo", paymentLog.OrderNo),
				zap.String("paymentNo", paymentLog.PaymentNo),
				zap.Error(err),
			)
			return err
		}
		// 如果有扫码点菜订单，则推送广播
		if order != nil {
			orderDetails, err := handler.OrderCloudService.GetCloudOrderDetailByOrderNo(ctx, order.No, []int{3}, order.MerchantNo)
			if err != nil {
				logging.Context(ctx).Error(
					"微信支付通知中获取订单详情失败",
					zap.String("orderNo", paymentLog.OrderNo),
					zap.String("paymentNo", paymentLog.PaymentNo),
					zap.Error(err),
				)
				return err
			}
			// 下单成功后打印美食信息
			handler.PrintService.FoodAction(ctx, merchant.No, "加菜", order.TableID, order.ID, "", orderDetails)
			// 推送广播
			order_broadcast.SendOrderBroadcast(order_broadcast.ActionAddFood, order.MerchantNo, order.TableID, order.ID, orderDetails)
			// 同步订单
			handler.OrderSyncHandler.SyncOrder(ctx, order.ID, order.MerchantNo)
		}

	}
	return nil
}

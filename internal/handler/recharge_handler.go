package handler

import (
	"context"
	"go.uber.org/zap"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/internal/sms"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"strconv"
	"time"
)

type RechargeHandler struct {
	Trans                         util.Trans
	MerchantService               *service.MerchantService
	CustomerService               *service.CustomerService
	PaymentLogService             *service.PaymentLogService
	RechargeOrderService          *service.RechargeOrderService
	CustomerConsumptionLogService *service.CustomerConsumptionLogService
}

// CreateRechargeOrder 创建充值订单
func (handler *RechargeHandler) CreateRechargeOrder(
	ctx context.Context, formItem *request.RechargeRequest) (*model.CustomerRechargeOrderModel, error) {
	// 创建客户充值订单
	rechargeOrder := &model.CustomerRechargeOrderModel{
		No:             util.SerialNumber(consts.VIPPaymentPrefix),
		MerchantNo:     formItem.MerchantNo,
		CustomerID:     formItem.CustomerID,
		RechargeAmount: formItem.RechargeAmount,
		PresentAmount:  formItem.PresentAmount,
		PaymentTypeID:  formItem.PaymentTypeID,
		Status:         model.RechargeOrderStatusWaiting,
		Type:           model.RechargeOrderTypeCashier,
		CashierID:      formItem.CashierID,
	}

	if err := handler.RechargeOrderService.Create(ctx, rechargeOrder); err != nil {
		return nil, err
	}

	return rechargeOrder, nil
}

// CreateRechargePaymentLog 创建充值订单
func (handler *RechargeHandler) CreateRechargePaymentLog(
	ctx context.Context, rechargeOrder *model.CustomerRechargeOrderModel, tradeType string, clientIP string,
	merchant *model.MerchantModel) (*model.MerchantPaymentLogModel, error) {

	// 构造支付记录
	expiresAt := time.Now().Add(config.C.PaymentConfig.PaymentExpires * time.Second)
	paymentInfo := &model.MerchantPaymentModel{
		MerchantNo:    rechargeOrder.MerchantNo,
		OrderNo:       rechargeOrder.No,
		PaymentTypeID: rechargeOrder.PaymentTypeID,
		CustomerID:    rechargeOrder.CustomerID,
		PaymentNo:     rechargeOrder.No,
		Amount:        rechargeOrder.RechargeAmount,
		ExpiresAt:     &expiresAt,
	}

	var paymentLog *model.MerchantPaymentLogModel
	var err error

	paymentLog, err = handler.PaymentLogService.CreatePaymentLog(ctx, merchant, paymentInfo, tradeType, consts.ORDER_TYPE_RECHARGE, clientIP)
	if err != nil {
		return nil, err
	}

	return paymentLog, nil
}

// HandleOnlinePaySuccess 线上支付成功
func (handler *RechargeHandler) HandleOnlinePaySuccess(
	ctx context.Context, customer *model.CustomerModel,
	rechargeOrder *model.CustomerRechargeOrderModel, paymentResult *schema.PaymentResult) error {
	// 更新支付信息
	err := handler.Trans.Exec(ctx, func(ctx context.Context) error {
		// 更新支付记录
		if _, err := handler.PaymentLogService.UpdatePaymentStatus(ctx, paymentResult); err != nil {
			return err
		}
		// 更新充值订单状态
		if err := handler.RechargeOrderService.UpdateStatus(ctx, rechargeOrder.ID, paymentResult.PaymentTypeID, model.RechargeOrderStatusSuccess); err != nil {
			return err
		}
		// 更新客户余额
		return handler.UpdateCustomerBalance(ctx, customer, rechargeOrder, paymentResult.PaymentTypeID)
	})
	if err != nil {
		return err
	}
	// 发送短信通知
	go handler.SendSuccessSms(rechargeOrder, customer)
	return nil
}

// UpdateCustomerBalance 更新余额
func (handler *RechargeHandler) UpdateCustomerBalance(
	ctx context.Context, customer *model.CustomerModel,
	rechargeOrder *model.CustomerRechargeOrderModel, paymentTypeId int64) error {
	balance := util.DivideFloat(float64(rechargeOrder.RechargeAmount+rechargeOrder.PresentAmount), 100)
	// 记录消费日志
	consumerLog := &model.CustomerConsumptionLogModel{
		MerchantNo:    rechargeOrder.MerchantNo,
		CustomerID:    rechargeOrder.CustomerID,
		Type:          model.ConsumptionTypeRecharge,
		OrderID:       rechargeOrder.ID,
		Balance:       util.AddFloat(customer.Balance, balance),
		Amount:        util.DivideFloat(float64(rechargeOrder.RechargeAmount), 100),
		PresentAmount: util.DivideFloat(float64(rechargeOrder.PresentAmount), 100),
		CashierID:     rechargeOrder.CashierID,
		PaymentTypeID: paymentTypeId,
	}
	if err := handler.CustomerConsumptionLogService.Create(ctx, consumerLog); err != nil {
		return err
	}
	// 更新余额
	return handler.CustomerService.IncreaseBalance(ctx, rechargeOrder.CustomerID, balance)
}

// SendSuccessSms 发送充值成功短信
func (handler *RechargeHandler) SendSuccessSms(order *model.CustomerRechargeOrderModel, customer *model.CustomerModel) {
	newCtx := context.Background()
	price := float64(order.RechargeAmount+order.PresentAmount) / 100
	leftPrice := customer.Balance
	defer func() {
		if r := recover(); r != nil {
			logging.Context(newCtx).Error("sms_fail 会员充值成功短信发送失败（panic）",
				zap.String("mobile", customer.Mobile),
				zap.Int64("customer_id", customer.ID),
				zap.String("merchant_no", customer.MerchantNo),
				zap.Int64("recharge_id", order.ID),
				zap.String("price", strconv.FormatFloat(price, 'f', 2, 64)),
				zap.String("leftPrice", strconv.FormatFloat(leftPrice, 'f', 2, 64)),
				zap.Any("error", r))
		}
	}()
	merchant, err := handler.MerchantService.GetByMerchantNo(newCtx, order.MerchantNo)
	if err != nil {
		logging.Context(newCtx).Error("充值成功短信发送：商家信息获取失败", zap.String("mobile", customer.Mobile),
			zap.Int64("customer_id", customer.ID),
			zap.String("merchant_no", customer.MerchantNo),
			zap.Int64("recharge_id", order.ID), zap.Error(err))
		return
	}
	if merchant.SMSCount > 0 {
		err := sms.SendCustomerRechargeSMS(
			customer.Mobile,
			merchant.NameZh,
			strconv.FormatFloat(price, 'f', 2, 64),
			strconv.FormatFloat(leftPrice, 'f', 2, 64),
		)
		if err != nil {
			logging.Context(newCtx).Error("sms_fail 会员充值成功短信发送失败",
				zap.String("mobile", customer.Mobile),
				zap.Int64("customer_id", customer.ID),
				zap.String("merchant_no", customer.MerchantNo),
				zap.Int64("recharge_id", order.ID),
				zap.String("price", strconv.FormatFloat(price, 'f', 2, 64)),
				zap.String("leftPrice", strconv.FormatFloat(leftPrice, 'f', 2, 64)),
				zap.Error(err))
			return
		}
		handler.MerchantService.DecreaseSMSCount(newCtx, merchant.No)
	}
}

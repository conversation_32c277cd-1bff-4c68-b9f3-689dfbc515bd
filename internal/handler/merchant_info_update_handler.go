package handler

import (
	"context"
	"go.uber.org/zap"
	"log"
	"reflect"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/logging"
)

type MerchantInfoUpdateHandler struct {
	MerchantInfoUpdateService *service.MerchantInfoUpdateService
}

func (handler *MerchantInfoUpdateHandler) UpdateMerchantInfoUpdates(merchantNo string, mdl interface{}) {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logging.Context(context.Background()).Error("商家信息更新时间更新失败", zap.Any("err", err))
			}
		}()
		ctx := context.Background()
		// 使用GORM进行更新操作
		column := getColumnByModel(mdl)

		log.Println("column: ", column)

		if column != "" {
			err := handler.MerchantInfoUpdateService.UpdateColumn(ctx, merchantNo, column)
			if err != nil {
				logging.Context(ctx).Error("商家信息更新时间更新失败", zap.Error(err))
			}
		}
	}()
}

func getColumnByModel(obj interface{}) string {
	switch reflect.TypeOf(obj).Name() {
	case "UserModel":
		return "user_updated_at"
	case "PrinterModel":
		return "printer_updated_at"
	case "FoodModel":
		return "food_updated_at"
	case "FoodPrinterModel":
		return "food_printer_updated_at"
	default:
		return ""
	}
}

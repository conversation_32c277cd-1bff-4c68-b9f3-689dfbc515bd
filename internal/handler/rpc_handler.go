package handler

import (
	"context"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/mqtt"
)

type RpcHandler struct {
	RpcMqtt            *mqtt.Mqtt
	LocalServerService *service.LocalServerService
}

type RPCResponse struct {
	Code int            `json:"code"`
	Body map[string]any `json:"body"`
}
type Payload struct {
	Path    string            `json:"path"`
	Method  string            `json:"method"`
	Headers map[string]string `json:"headers"`
	Body    map[string]any    `json:"body"`
}

// HandRpcWithServerToken 处理带有服务端token的RPC请求
func (h *RpcHandler) HandRpcWithServerToken(ctx context.Context, merchantNo string, clientId string, body []byte) (*RPCResponse, error) {
	localServer, err := h.LocalServerService.GetActiveServerByMerchantNo(ctx, merchantNo)
	if err != nil {
		return nil, err
	}

	if localServer == nil {
		return nil, errors.BadRequest("", "LocalServerNotRegistered")
	}
	payload := Payload{}
	err = json.Unmarshal(body, &payload)
	if err != nil {
		return nil, err
	}
	payload.Headers["Server-Token"] = localServer.Token[:16]
	body, err = json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	return h.HandleRpc(merchantNo, clientId, body)
}

// HandleRpc 处理RPC请求
func (h *RpcHandler) HandleRpc(merchantNo string, clientId string, body []byte) (*RPCResponse, error) {
	resp, err := h.RpcMqtt.Request(merchantNo, clientId, body)
	if err != nil {
		return nil, err
	}
	var RPCResponse RPCResponse
	err = json.Unmarshal(resp, &RPCResponse)
	if err != nil {
		return nil, err
	}
	return &RPCResponse, nil
}

//go:build wireinject
// +build wireinject

package wirex

// The build tag makes sure the stub is not built in the final build.

import (
	"context"
	"ros-api-go/internal"
	"ros-api-go/pkg/alipayx"
	"ros-api-go/pkg/wechatx"

	"ros-api-go/pkg/util"

	"github.com/google/wire"
)

func BuildInjector(ctx context.Context) (*Injector, func(), error) {
	wire.Build(
		InitCacher, // 初始化缓存
		InitDB,     // 初始化数据库
		InitRedis,  // 初始化redis
		InitAuth,   // 初始化鉴权
		InitMQTT,
		wechatx.NewWechatPayClient,   // 初始化微信支付V2
		wechatx.NewWechatPayClientV3, // 初始化微信支付V3
		alipayx.NewPaymentClient,     // 初始化支付宝支付V3
		alipayx.NewMiniClient,        // 初始化支付宝小程序V3
		wire.NewSet(wire.Struct(new(util.Trans), "*")),
		wire.NewSet(wire.Struct(new(Injector), "*")),
		internal.AppSet,
		internal.BizSet,
	) // end
	return new(Injector), nil, nil
}

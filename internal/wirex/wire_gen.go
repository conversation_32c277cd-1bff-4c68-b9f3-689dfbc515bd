// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wirex

import (
	"context"
	"ros-api-go/internal"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http"
	"ros-api-go/internal/http/controller/v2"
	"ros-api-go/internal/http/controller/v2/foods"
	"ros-api-go/internal/http/controller/v2/local"
	"ros-api-go/internal/http/controller/v2/merchant"
	"ros-api-go/internal/http/controller/v2/order"
	"ros-api-go/internal/http/controller/v2/pay"
	"ros-api-go/internal/http/controller/v2/scan"
	"ros-api-go/internal/http/controller/v2/statistics"
	"ros-api-go/internal/http/controller/v2/sync"
	"ros-api-go/internal/http/middleware"
	"ros-api-go/internal/route"
	"ros-api-go/internal/service"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/internal/service/scan_service"
	"ros-api-go/internal/service/statistic_service"
	"ros-api-go/pkg/alipayx"
	"ros-api-go/pkg/util"
	"ros-api-go/pkg/wechatx"
)

// Injectors from wire.go:

func BuildInjector(ctx context.Context) (*Injector, func(), error) {
	db, cleanup, err := InitDB(ctx)
	if err != nil {
		return nil, nil, err
	}
	client, cleanup2, err := InitRedis(ctx)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	cacher, cleanup3, err := InitCacher(ctx)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	auther, cleanup4, err := InitAuth(ctx)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	areaService := &service.AreaService{
		DB: db,
	}
	areaController := &sync.AreaController{
		AreaService: areaService,
	}
	tableService := &service.TableService{
		DB: db,
	}
	tableController := &sync.TableController{
		TableService: tableService,
	}
	trans := &util.Trans{
		DB: db,
	}
	printerService := &service.PrinterService{
		DB:    db,
		Trans: trans,
	}
	printerController := &sync.PrinterController{
		PrinterService: printerService,
	}
	foodCategoryService := &food_service.FoodCategoryService{
		DB: db,
	}
	foodCategory := &sync.FoodCategory{
		FoodCategoryService: foodCategoryService,
	}
	foodsService := &food_service.FoodsService{
		DB:    db,
		Cache: cacher,
		Trans: trans,
	}
	foodsController := &sync.FoodsController{
		FoodsService: foodsService,
	}
	foodComboService := &food_service.FoodComboService{
		DB: db,
	}
	foodComboController := &sync.FoodComboController{
		FoodComboService: foodComboService,
	}
	userService := &service.UserService{
		Trans: trans,
		Cache: cacher,
		DB:    db,
	}
	userController := &sync.UserController{
		UserService: userService,
	}
	merchantInfoUpdateService := &service.MerchantInfoUpdateService{
		DB: db,
	}
	merchantInfoUpdateController := &sync.MerchantInfoUpdateController{
		MerchantInfoUpdateService: merchantInfoUpdateService,
	}
	merchantConfigService := &service.MerchantConfigService{
		DB: db,
	}
	merchantConfigController := &sync.MerchantConfigController{
		MerchantConfigService: merchantConfigService,
	}
	remarkService := &service.RemarkService{
		DB: db,
	}
	remarkController := &sync.RemarkController{
		RemarkService: remarkService,
	}
	remarkCategoryService := &service.RemarkCategoryService{
		DB: db,
	}
	remarkCategoryController := &sync.RemarkCategoryController{
		RemarkCategoryService: remarkCategoryService,
	}
	foodPrinterService := &food_service.FoodPrinterService{
		DB:    db,
		Trans: trans,
	}
	foodPrinterController := &sync.FoodPrinterController{
		FoodPrinterService: foodPrinterService,
	}
	merchantRoleService := &service.MerchantRoleService{
		DB: db,
	}
	merchantRoleController := &sync.MerchantRoleController{
		MerchantRoleService: merchantRoleService,
	}
	casbinx := &http.Casbinx{
		Cache: cacher,
		DB:    db,
	}
	permissionService := &service.PermissionService{
		Trans:   trans,
		DB:      db,
		Casbinx: casbinx,
	}
	permissionController := &sync.PermissionController{
		PermissionService: permissionService,
	}
	paymentTypeService := &service.PaymentTypeService{
		DB: db,
	}
	merchantService := &service.MerchantService{
		DB: db,
	}
	paymentTypeController := &sync.PaymentTypeController{
		PaymentTypeService: paymentTypeService,
		MerchantService:    merchantService,
	}
	utilTrans := util.Trans{
		DB: db,
	}
	orderSyncService := &service.OrderSyncService{
		Trans: utilTrans,
		DB:    db,
	}
	orderSyncController := &sync.OrderSyncController{
		OrderSyncService: orderSyncService,
	}
	syncRouterGroup := &route.SyncRouterGroup{
		AreaController:               areaController,
		TableController:              tableController,
		PrinterController:            printerController,
		FoodCategoryController:       foodCategory,
		FoodsController:              foodsController,
		FoodComboController:          foodComboController,
		UserController:               userController,
		MerchantInfoUpdateController: merchantInfoUpdateController,
		MerchantConfigController:     merchantConfigController,
		RemarkController:             remarkController,
		RemarkCategoryController:     remarkCategoryController,
		FoodPrinterController:        foodPrinterController,
		MerchantRoleController:       merchantRoleController,
		PermissionController:         permissionController,
		PaymentTypeController:        paymentTypeController,
		OrderSyncController:          orderSyncController,
	}
	merchantEmployeeService := &service.MerchantEmployeeService{
		Db:    db,
		Trans: utilTrans,
	}
	merchantInfoUpdateHandler := &handler.MerchantInfoUpdateHandler{
		MerchantInfoUpdateService: merchantInfoUpdateService,
	}
	roleController := &merchant.RoleController{
		MerchantRoleService:       merchantRoleService,
		MerchantEmployeeService:   merchantEmployeeService,
		MerchantInfoUpdateHandler: merchantInfoUpdateHandler,
		Casbinx:                   casbinx,
	}
	checkUserMerchantProvider := &middleware.CheckUserMerchantProvider{
		MerchantEmployeeService: merchantEmployeeService,
	}
	employeeController := &merchant.EmployeeController{
		MerchantEmployeeService:   merchantEmployeeService,
		MerchantInfoUpdateHandler: merchantInfoUpdateHandler,
		UserService:               userService,
		Casbinx:                   casbinx,
	}
	merchantRouterGroup := &route.MerchantRouterGroup{
		MerchantRoleController:    roleController,
		CheckUserMerchantProvider: checkUserMerchantProvider,
		EmployeeController:        employeeController,
	}
	paymentService := &service.PaymentService{
		DB:    db,
		Trans: utilTrans,
		Cache: cacher,
	}
	wechatClient, err := wechatx.NewWechatPayClient()
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	clientV3, err := wechatx.NewWechatPayClientV3()
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	wechatPayService := &service.WechatPayService{
		DB:              db,
		PaymentClient:   wechatClient,
		PaymentClientV3: clientV3,
	}
	alipayClient, err := alipayx.NewPaymentClient()
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	alipayMiniClient, err := alipayx.NewMiniClient()
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	alipayService := &service.AlipayService{
		PaymentClient: alipayClient,
		MiniClient:    alipayMiniClient,
		DB:            db,
	}
	paymentLogService := &service.PaymentLogService{
		DB: db,
	}
	paymentController := &pay.PaymentController{
		MerchantService:    merchantService,
		PaymentService:     paymentService,
		WechatPayService:   wechatPayService,
		AlipayService:      alipayService,
		PaymentLogService:  paymentLogService,
		PaymentTypeService: paymentTypeService,
	}
	servicePaymentService := service.PaymentService{
		DB:    db,
		Trans: utilTrans,
		Cache: cacher,
	}
	refundLogService := &service.RefundLogService{
		DB: db,
	}
	refundBatchService := &service.RefundBatchService{
		DB: db,
	}
	customerService := &service.CustomerService{
		DB: db,
	}
	debtHolderService := &service.DebtHolderService{
		DB:    db,
		Trans: trans,
	}
	customerConsumptionLogService := &service.CustomerConsumptionLogService{
		DB: db,
	}
	debtTransactionService := &service.DebtTransactionService{
		DB:    db,
		Trans: trans,
	}
	refundHandler := &handler.RefundHandler{
		Trans:                         utilTrans,
		PaymentService:                paymentService,
		WechatPayService:              wechatPayService,
		AlipayService:                 alipayService,
		RefundLogService:              refundLogService,
		RefundBatchService:            refundBatchService,
		CustomerService:               customerService,
		PaymentLogService:             paymentLogService,
		MerchantService:               merchantService,
		DebtHolderService:             debtHolderService,
		CustomerConsumptionLogService: customerConsumptionLogService,
		DebtTransactionService:        debtTransactionService,
	}
	paymentReverseController := &pay.PaymentReverseController{
		Trans:             trans,
		PaymentLogService: paymentLogService,
		PaymentService:    servicePaymentService,
		WechatPayService:  wechatPayService,
		RefundLogService:  refundLogService,
		MerchantService:   merchantService,
		RefundHandler:     refundHandler,
	}
	debtRepaymentOrderService := &service.DebtRepaymentOrderService{
		DB:    db,
		Trans: trans,
	}
	debtRepaymentHandler := &handler.DebtRepaymentHandler{
		Trans:                     utilTrans,
		MerchantService:           merchantService,
		DebtHolderService:         debtHolderService,
		PaymentLogService:         paymentLogService,
		DebtRepaymentOrderService: debtRepaymentOrderService,
		DebtTransactionService:    debtTransactionService,
	}
	scanOrderService := &scan_service.ScanOrderService{
		DB: db,
	}
	orderCloudService := &service.OrderCloudService{
		DB:          db,
		Trans:       trans,
		RedisClient: client,
	}
	rechargeOrderService := &service.RechargeOrderService{
		DB: db,
	}
	mqtt, cleanup5, err := InitMQTT(ctx)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	localServerService := &service.LocalServerService{
		Trans: trans,
		DB:    db,
	}
	rpcHandler := &handler.RpcHandler{
		RpcMqtt:            mqtt,
		LocalServerService: localServerService,
	}
	orderService := &service.OrderService{
		DB: db,
	}
	orderSyncHandler := &handler.OrderSyncHandler{
		OrderCloudService: orderCloudService,
		OrderService:      orderService,
		Trans:             trans,
	}
	printService := &service.PrintService{
		DB:     db,
		Client: client,
	}
	paymentHandler := &handler.PaymentHandler{
		RpcHandler:         rpcHandler,
		LocalServerService: localServerService,
		MerchantService:    merchantService,
		OrderCloudService:  orderCloudService,
		OrderSyncHandler:   orderSyncHandler,
		PrintService:       printService,
	}
	rechargeHandler := &handler.RechargeHandler{
		Trans:                         utilTrans,
		MerchantService:               merchantService,
		CustomerService:               customerService,
		PaymentLogService:             paymentLogService,
		RechargeOrderService:          rechargeOrderService,
		CustomerConsumptionLogService: customerConsumptionLogService,
	}
	wechatPayController := &pay.WechatPayController{
		MerchantService:           merchantService,
		PaymentService:            paymentService,
		PaymentLogService:         paymentLogService,
		RefundLogService:          refundLogService,
		WechatPayService:          wechatPayService,
		DebtRepaymentHandler:      debtRepaymentHandler,
		DebtRepaymentOrderService: debtRepaymentOrderService,
		CustomerService:           customerService,
		DebtHolderService:         debtHolderService,
		ScanOrderService:          scanOrderService,
		OrderCloudService:         orderCloudService,
		RechargeOrderService:      rechargeOrderService,
		PaymentHandler:            paymentHandler,
		RefundHandler:             refundHandler,
		RechargeHandler:           rechargeHandler,
		Cache:                     cacher,
		Trans:                     trans,
	}
	aliPayController := &pay.AliPayController{
		PaymentService:            paymentService,
		MerchantService:           merchantService,
		PaymentLogService:         paymentLogService,
		AlipayService:             alipayService,
		CustomerService:           customerService,
		PaymentHandler:            paymentHandler,
		RefundHandler:             refundHandler,
		RechargeHandler:           rechargeHandler,
		RechargeOrderService:      rechargeOrderService,
		DebtRepaymentHandler:      debtRepaymentHandler,
		DebtRepaymentOrderService: debtRepaymentOrderService,
		DebtHolderService:         debtHolderService,
		Cache:                     cacher,
		Trans:                     trans,
	}
	customerPayController := &pay.CustomerPayController{
		CustomerService: customerService,
		MerchantService: merchantService,
		PaymentService:  paymentService,
	}
	rechargeController := &pay.RechargeController{
		Trans:                trans,
		MerchantService:      merchantService,
		PaymentService:       paymentService,
		PaymentTypeService:   paymentTypeService,
		WechatPayService:     wechatPayService,
		AlipayService:        alipayService,
		PaymentLogService:    paymentLogService,
		CustomerService:      customerService,
		RechargeOrderService: rechargeOrderService,
		RechargeHandler:      rechargeHandler,
	}
	debtPayController := &pay.DebtPayController{
		DebtHolderService: debtHolderService,
		MerchantService:   merchantService,
		PaymentService:    paymentService,
	}
	debtRepaymentController := &pay.DebtRepaymentController{
		Trans:                     trans,
		MerchantService:           merchantService,
		PaymentService:            paymentService,
		PaymentTypeService:        paymentTypeService,
		WechatPayService:          wechatPayService,
		AlipayService:             alipayService,
		PaymentLogService:         paymentLogService,
		DebtHolderService:         debtHolderService,
		DebtRepaymentOrderService: debtRepaymentOrderService,
		DebtRepaymentHandler:      debtRepaymentHandler,
	}
	payPaymentTypeController := &pay.PaymentTypeController{
		PaymentTypeService: paymentTypeService,
		MerchantService:    merchantService,
	}
	payRouterGroup := &route.PayRouterGroup{
		PaymentController:         paymentController,
		PaymentReverseController:  paymentReverseController,
		WechatPayController:       wechatPayController,
		AliPayController:          aliPayController,
		CustomerPayController:     customerPayController,
		RechargeController:        rechargeController,
		DebtPayController:         debtPayController,
		DebtRepaymentController:   debtRepaymentController,
		PaymentTypeController:     payPaymentTypeController,
		CheckUserMerchantProvider: checkUserMerchantProvider,
	}
	localPrinterController := &local.PrinterController{
		Client: client,
	}
	broadcastController := &local.BroadcastController{}
	localRouterGroup := &route.LocalRouterGroup{
		PrinterController:   localPrinterController,
		BroadcastController: broadcastController,
	}
	wechatUserService := service.WechatUserService{
		DB: db,
	}
	wechatAuthController := &scan.WechatAuthController{
		WechatUserService: wechatUserService,
	}
	serviceWechatUserService := &service.WechatUserService{
		DB: db,
	}
	scanOrderController := &scan.ScanOrderController{
		Trans:             trans,
		RpcHandler:        rpcHandler,
		WechatUserService: serviceWechatUserService,
		ScanOrderService:  scanOrderService,
		MerchantService:   merchantService,
		PaymentService:    paymentService,
		PaymentLogService: paymentLogService,
		WechatPayService:  wechatPayService,
		AlipayService:     alipayService,
		OrderCloudService: orderCloudService,
		Cache:             cacher,
	}
	merchantServiceService := &scan_service.MerchantServiceService{
		DB: db,
	}
	serviceController := &scan.ServiceController{
		TableService:           tableService,
		MerchantService:        merchantService,
		MerchantServiceService: merchantServiceService,
		RpcHandler:             rpcHandler,
	}
	scanRouterGroup := &route.ScanRouterGroup{
		WechatAuthController: wechatAuthController,
		ScanOrderController:  scanOrderController,
		ServiceController:    serviceController,
	}
	foodController := &foods.FoodController{
		FoodsService:              foodsService,
		MerchantInfoUpdateHandler: merchantInfoUpdateHandler,
	}
	food_serviceFoodCategoryService := food_service.FoodCategoryService{
		DB: db,
	}
	foodCategoryController := &foods.FoodCategoryController{
		FoodCategoryService:       food_serviceFoodCategoryService,
		MerchantInfoUpdateHandler: merchantInfoUpdateHandler,
	}
	foodSpecService := food_service.FoodSpecService{
		DB:    db,
		Trans: trans,
	}
	foodSpecController := &foods.FoodSpecController{
		FoodSpecService: foodSpecService,
	}
	foodRouterGroup := &route.FoodRouterGroup{
		FoodController:         foodController,
		FoodCategoryController: foodCategoryController,
		FoodSpecController:     foodSpecController,
	}
	methodGroupService := &service.MethodGroupService{
		DB:    db,
		Trans: trans,
	}
	methodGroupController := &v2.MethodGroupController{
		MethodGroupService: methodGroupService,
	}
	methodGroupRouterGroup := &route.MethodGroupRouterGroup{
		MethodGroupController: methodGroupController,
	}
	authService := &service.AuthService{
		DB:    db,
		Cache: cacher,
		Auth:  auther,
	}
	mqttAclService := &service.MqttAclService{
		DB:    db,
		Trans: trans,
	}
	handoverService := &service.HandoverService{
		DB: db,
	}
	authController := &v2.AuthController{
		AuthService:               authService,
		MerchantService:           merchantService,
		LocalServerService:        localServerService,
		UserService:               userService,
		MqttAclService:            mqttAclService,
		HandoverService:           handoverService,
		MerchantInfoUpdateHandler: merchantInfoUpdateHandler,
	}
	operationPasswordController := &v2.OperationPasswordController{
		UserService:               userService,
		MerchantEmployeeService:   merchantEmployeeService,
		MerchantInfoUpdateHandler: merchantInfoUpdateHandler,
	}
	orderDetailService := &service.OrderDetailService{
		DB: db,
	}
	refundOrderLogService := &service.RefundOrderLogService{
		DB: db,
	}
	changeOrderLogService := &service.ChangeOrderLogService{
		DB: db,
	}
	orderRefundController := &order.OrderRefundController{
		Trans:                         trans,
		MerchantService:               merchantService,
		OrderService:                  orderService,
		UserService:                   userService,
		OrderDetailService:            orderDetailService,
		RefundOrderLogService:         refundOrderLogService,
		ChangeOrderLogService:         changeOrderLogService,
		PaymentService:                paymentService,
		PaymentLogService:             paymentLogService,
		RefundBatchService:            refundBatchService,
		RefundLogService:              refundLogService,
		CustomerService:               customerService,
		CustomerConsumptionLogService: customerConsumptionLogService,
		RefundHandler:                 refundHandler,
	}
	billService := &service.BillService{
		DB: db,
	}
	billController := &v2.BillController{
		MerchantService: merchantService,
		BillService:     billService,
	}
	rpcInvokeController := &v2.RPCInvokeController{
		RpcHandler: rpcHandler,
	}
	mqttController := &v2.MqttController{
		MqttAclService:     mqttAclService,
		LocalServerService: localServerService,
	}
	v2PermissionController := &v2.PermissionController{}
	shiftService := &service.ShiftService{
		DB: db,
	}
	handoverController := &v2.HandoverController{
		HandoverService:           handoverService,
		OrderService:              orderService,
		PaymentService:            paymentService,
		PaymentTypeService:        paymentTypeService,
		UserService:               userService,
		EmployeeService:           merchantEmployeeService,
		RechargeOrderService:      rechargeOrderService,
		CustomerService:           customerService,
		ShiftService:              shiftService,
		OrderCloudService:         orderCloudService,
		DebtHolderService:         debtHolderService,
		DebtRepaymentOrderService: debtRepaymentOrderService,
		Trans:                     trans,
	}
	v2UserController := &v2.UserController{
		OrderService: orderService,
	}
	v2PrinterController := &v2.PrinterController{
		PrinterService:            printerService,
		MerchantInfoUpdateHandler: merchantInfoUpdateHandler,
	}
	terminalService := &service.TerminalService{
		DB:    db,
		Trans: trans,
	}
	terminalController := &v2.TerminalController{
		TerminalService: terminalService,
	}
	debtHolderController := &v2.DebtHolderController{
		DebtHolderService: debtHolderService,
		PaymentService:    paymentService,
		MerchantService:   merchantService,
	}
	debtTransactionController := &v2.DebtTransactionController{
		DebtTransactionService: debtTransactionService,
	}
	businessStatisticService := statistic_service.BusinessStatisticService{
		DB: db,
	}
	businessStatisticController := &statistics.BusinessStatisticController{
		Service: businessStatisticService,
	}
	foodsStatisticService := statistic_service.FoodsStatisticService{
		DB: db,
	}
	foodsStatisticController := &statistics.FoodsStatisticController{
		Service: foodsStatisticService,
	}
	handoverStatisticService := statistic_service.HandoverStatisticService{
		DB: db,
	}
	handoverStatisticController := &statistics.HandoverStatisticController{
		Service: handoverStatisticService,
	}
	graphStatisticService := statistic_service.GraphStatisticService{
		DB: db,
	}
	graphStatisticController := &statistics.GraphStatisticController{
		Service: graphStatisticService,
	}
	masterStatisticService := &statistic_service.MasterStatisticService{
		DB: db,
	}
	masterStatisticController := &statistics.MasterStatisticController{
		Service: masterStatisticService,
	}
	apiRouterGroup := &route.ApiRouterGroup{
		SyncRouterGroup:             syncRouterGroup,
		MerchantRouterGroup:         merchantRouterGroup,
		PayRouterGroup:              payRouterGroup,
		LocalRouterGroup:            localRouterGroup,
		ScanRouterGroup:             scanRouterGroup,
		FoodRouterGroup:             foodRouterGroup,
		MethodGroupRouterGroup:      methodGroupRouterGroup,
		AuthController:              authController,
		OperationPasswordController: operationPasswordController,
		OrderController:             orderRefundController,
		BillController:              billController,
		RPCInvokeController:         rpcInvokeController,
		MqttController:              mqttController,
		PermissionController:        v2PermissionController,
		HandoverController:          handoverController,
		UserController:              v2UserController,
		PrinterController:           v2PrinterController,
		TerminalController:          terminalController,
		DebtHolderController:        debtHolderController,
		DebtTransactionController:   debtTransactionController,
		BusinessStatisticController: businessStatisticController,
		FoodsStatisticController:    foodsStatisticController,
		HandoverStatisticController: handoverStatisticController,
		GraphStatisticController:    graphStatisticController,
		MasterStatisticController:   masterStatisticController,
	}
	orderController := &order.OrderController{
		OrderService:       orderService,
		OrderCloudService:  orderCloudService,
		PaymentTypeService: paymentTypeService,
		PaymentService:     paymentService,
		MerchantService:    merchantService,
		PaymentLogService:  paymentLogService,
		WechatPayService:   wechatPayService,
		TableService:       tableService,
		Trans:              trans,
		EmployeeService:    merchantEmployeeService,
		OrderSyncHandler:   orderSyncHandler,
		PrintService:       printService,
	}
	orderPaymentController := &order.OrderPaymentController{
		PaymentService:     paymentService,
		PaymentTypeService: paymentTypeService,
		OrderCloudService:  orderCloudService,
		MerchantService:    merchantService,
		PaymentLogService:  paymentLogService,
		WechatPayService:   wechatPayService,
		AlipayService:      alipayService,
		CustomerService:    customerService,
		DebtHolderService:  debtHolderService,
		Trans:              trans,
	}
	orderPaymentReverseController := &order.PaymentReverseController{
		Trans:              trans,
		PaymentLogService:  paymentLogService,
		PaymentService:     servicePaymentService,
		PaymentTypeService: paymentTypeService,
		WechatPayService:   wechatPayService,
		RefundLogService:   refundLogService,
		MerchantService:    merchantService,
		RefundHandler:      refundHandler,
	}
	cloudRouteGroup := &route.CloudRouteGroup{
		OrderController:           orderController,
		OrderSyncHandler:          orderSyncHandler,
		OrderPaymentController:    orderPaymentController,
		PaymentReverseController:  orderPaymentReverseController,
		FoodCategoryController:    foodCategoryController,
		FoodController:            foodController,
		CheckUserMerchantProvider: checkUserMerchantProvider,
	}
	adminRouterGroup := &route.AdminRouterGroup{
		AuthController: authController,
	}
	routers := &internal.Routers{
		ApiRouterGroup:   apiRouterGroup,
		OrderRouteGroup:  cloudRouteGroup,
		AdminRouterGroup: adminRouterGroup,
	}
	authProvider := &middleware.AuthProvider{
		Auth:        auther,
		Cache:       cacher,
		UserService: userService,
	}
	serviceLocalServerService := service.LocalServerService{
		Trans: trans,
		DB:    db,
	}
	serviceMerchantService := service.MerchantService{
		DB: db,
	}
	serverAuthProvider := &middleware.ServerAuthProvider{
		LocalServerService: serviceLocalServerService,
		MerchantService:    serviceMerchantService,
		Cache:              cacher,
	}
	wechatUserProvider := &middleware.WechatUserProvider{
		WechatUserService: wechatUserService,
		MerchantService:   serviceMerchantService,
	}
	casbinProvider := &middleware.CasbinProvider{
		Casbinx: casbinx,
	}
	app := &internal.App{
		Routers:            routers,
		Casbinx:            casbinx,
		AuthProvider:       authProvider,
		ServerAuthProvider: serverAuthProvider,
		WechatUserProvider: wechatUserProvider,
		CasbinProvider:     casbinProvider,
	}
	injector := &Injector{
		DB:    db,
		Redis: client,
		Cache: cacher,
		Auth:  auther,
		App:   app,
	}
	return injector, func() {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}

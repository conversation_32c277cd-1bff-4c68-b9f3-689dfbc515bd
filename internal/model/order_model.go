package model

import (
	"time"
)

// OrderModel 订单模型
type OrderModel struct {
	ID              int64      `gorm:"column:id;size:20;primaryKey;"`   // ID
	MerchantNo      string     `gorm:"column:merchant_no"`              // 商户编号
	No              string     `gorm:"column:no"`                       // 订单流水号
	CustomerID      int64      `gorm:"column:customer_id"`              // VIP客户 ID
	TableID         int64      `gorm:"column:table_id"`                 // 餐桌编号
	OpenID          *string    `gorm:"column:open_id"`                  // 扫码点餐用户openId
	TerminalID      int64      `gorm:"column:terminal_id"`              // 终端ID
	CustomersCount  int        `gorm:"column:customers_count"`          // 人数
	FoodsCount      float64    `gorm:"column:foods_count"`              // 美食数量
	CostPrice       float64    `gorm:"column:cost_price"`               // 成本价
	OriginalPrice   float64    `gorm:"column:original_price"`           // 原价
	VipPrice        float64    `gorm:"column:vip_price"`                // 会员价
	Price           float64    `gorm:"column:price"`                    // 出售价
	IgnorePrice     float64    `gorm:"column:ignore_price"`             // 抹零价
	RefundPrice     float64    `gorm:"column:refund_price"`             // 退款金额
	CollectedAmount *float64   `gorm:"column:collected_amount"`         // 从客户手收的钱
	UserID          int64      `gorm:"column:user_id"`                  // 下单人员编号
	CashierID       *int64     `gorm:"column:cashier_id"`               // 结账人员编号
	GiveChange      *float64   `gorm:"column:give_change"`              // 找零
	State           int64      `gorm:"column:state"`                    // 订单状态
	Remarks         *string    `gorm:"column:remarks"`                  // 备注
	PaidAt          *time.Time `gorm:"column:paid_at"`                  // 结账时间
	TaxTicket       bool       `gorm:"column:tax_ticket"`               // 是否开过税票
	IsPrint         bool       `gorm:"column:is_print"`                 // 打印状态
	WechatUserID    *int64     `gorm:"column:wechat_user_id"`           // 微信用户ID
	IsScanOrder     bool       `gorm:"column:is_scan_order"`            // 是否是扫码点餐
	IsSync          bool       `gorm:"column:is_sync"`                  // 是否已同步
	CreatedAt       *time.Time `gorm:"column:created_at"`               // 创建时间
	UpdatedAt       *time.Time `gorm:"column:updated_at"`               // 更新时间
	DeletedAt       *time.Time `gorm:"column:deleted_at"`               // 删除时间
	CanceledAmount  float64    `gorm:"<-:false;column:canceled_amount"` // 取消金额

	// 关联关系
	Merchant      *MerchantModel          `gorm:"foreignKey:MerchantNo;references:No"`                          // 商户
	Details       []*OrderDetailModel     `gorm:"foreignKey:OrderID;references:ID"`                             // 订单详情
	Payments      []*MerchantPaymentModel `gorm:"foreignKey:OrderNo;references:No"`                             // 订单支付记录
	RefundBatches []*RefundBatchModel     `gorm:"foreignKey:OrderID;references:ID"`                             // 退款批次
	RefundLogs    []*RefundOrderLogModel  `gorm:"foreignKey:OrderID;references:ID"`                             // 退款记录
	Creator       *MerchantEmployeeModel  `gorm:"foreignKey:UserID,MerchantNo;references:UserID,MerchantNo"`    // 收银员
	Cashier       *MerchantEmployeeModel  `gorm:"foreignKey:CashierID,MerchantNo;references:UserID,MerchantNo"` // 收银员
	Table         *TableModel             `gorm:"foreignKey:TableID;references:ID"`                             // 餐桌
}

func (model *OrderModel) TableName() string {
	return "orders"
}

package model

import "time"

const (
	DebtRepaymentOrderStatusWaiting = 0 // 待充值
	DebtRepaymentOrderStatusSuccess = 1 // 充值成功
	DebtRepaymentOrderStatusFailed  = 2 // 充值失败
)

type DebtRepaymentOrderModel struct {
	ID            int64     `gorm:"column:id;primary_key;autoIncrement"` // 订单ID
	No            string    `gorm:"column:no"`                           // 订单号
	MerchantNo    string    `gorm:"column:merchant_no"`                  // 商户号
	HolderID      int64     `gorm:"column:holder_id"`                    // 赊账人ID
	Amount        int64     `gorm:"column:amount"`                       // 结账金额
	PaymentTypeID int64     `gorm:"column:payment_type_id"`              // 支付方式ID
	Status        int64     `gorm:"column:status"`                       // 订单状态： 0 ：待充值 1： 已充值、 2: 失败
	CashierID     int64     `gorm:"column:cashier_id"`                   // 收银员ID
	CreatedAt     time.Time `gorm:"column:created_at"`                   // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at"`                   // 更新时间

	PaymentType *PaymentTypeModel      `gorm:"foreignkey:PaymentTypeID"`                                     // 关联支付方式
	Holder      *DebtHolderModel       `gorm:"foreignkey:HolderID"`                                          // 关联用户
	Cashier     *MerchantEmployeeModel `gorm:"foreignKey:CashierID,MerchantNo;references:UserID,MerchantNo"` // 收银员
}

func (m *DebtRepaymentOrderModel) TableName() string {
	return "debt_repayment_orders"
}

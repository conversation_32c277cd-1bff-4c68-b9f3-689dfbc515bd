package model

import (
	"time"
)

// DebtTransaction types
const (
	DebtTransactionTypeDebt      = 1 // 赊账
	DebtTransactionTypeRepayment = 2 // 结账
	DebtTransactionTypeRefund    = 3 // 退款
)

// DebtTransactionModel 赊账交易记录模型
type DebtTransactionModel struct {
	ID            int64      `gorm:"column:id;primaryKey;auto_increment" `
	MerchantNo    string     `gorm:"column:merchant_no;size:20;not null" `                  // 商家编号
	OrderID       *int64     `gorm:"column:order_id" `                                      // 订单ID
	OrderNo       *string    `gorm:"column:order_no;size:32" `                              // 订单编号
	HolderID      int64      `gorm:"column:holder_id;not null" `                            // 赊账人ID
	Amount        int64      `gorm:"column:amount;not null" `                               // 金额
	PaymentTypeID int64      `gorm:"column:payment_type_id" `                               // 支付方式ID
	Balance       int64      `gorm:"column:balance;not null" `                              // 余额 (大于0 为赊账，小于0为结账/退款)
	Type          uint8      `gorm:"column:type;default:1;not null" `                       // 交易类型 1 赊账 2 结账
	CashierID     int64      `gorm:"column:cashier_id;not null" `                           // 创建人
	CreatedAt     time.Time  `gorm:"column:created_at;default:CURRENT_TIMESTAMP;not null" ` // 创建时间
	UpdatedAt     *time.Time `gorm:"column:updated_at" `                                    // 更新时间

	// 关联
	Holder  *DebtHolderModel       `gorm:"foreignKey:HolderID"`                                          // 赊账人
	Cashier *MerchantEmployeeModel `gorm:"foreignKey:CashierID,MerchantNo;references:UserID,MerchantNo"` // 收银员
}

// TableName 表名
func (d *DebtTransactionModel) TableName() string {
	return "debt_transactions"
}

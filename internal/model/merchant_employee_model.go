package model

import (
	"time"
)

const (
	EmployeeStateActive      = 1     // 正常状态
	EmployeeStateInactive    = 0     // 禁用状态
	EmployeeStateDeleted     = -1    // 删除状态
	MerchantEmployeeOwner    = true  // 店主
	MerchantEmployeeNotOwner = false // 非店主

)

// MerchantEmployeeModel 商家用户角色
type MerchantEmployeeModel struct {
	ID                int64      `gorm:"column:id"`          // ID
	MerchantNo        string     `gorm:"column:merchant_no"` // 商户号
	No                string     `gorm:"column:no"`          // 员工号
	UserID            int64      `gorm:"column:user_id"`     // 用户ID
	NameUg            string     `gorm:"column:name_ug"`     // 用户名（英文）
	NameZh            string     `gorm:"column:name_zh"`     // 用户名（中文）
	IsOwner           bool       `gorm:"column:is_owner"`    // 是否是店主
	OperationPassword string     `gorm:"column:operation_password"`
	State             int8       `gorm:"column:state"`      // 状态
	CreatedAt         time.Time  `gorm:"column:created_at"` // Create time
	UpdatedAt         time.Time  `gorm:"column:updated_at"` // Update time
	DeletedAt         *time.Time `gorm:"column:deleted_at"` // Delete time
	//MerchantRoles []MerchantRoleModel `gorm:"many2many:user_roles;foreignKey:UserID,MerchantNo;joinForeignKey:UserID,MerchantNo;references:ID;joinReferences:RoleID"`
	Roles []MerchantRoleModel `gorm:"many2many:merchant_employee_roles;foreignKey:ID;joinForeignKey:EmployeeID;references:ID;joinReferences:RoleID"`
	User  *UserModel          `gorm:"foreignKey:UserID"`
}

func (a *MerchantEmployeeModel) TableName() string {

	return "merchant_employees"
}

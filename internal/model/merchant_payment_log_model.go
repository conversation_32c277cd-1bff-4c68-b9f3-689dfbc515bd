package model

import (
	"ros-api-go/internal/consts"
	"time"
)

// MerchantPaymentLogModel 商户支付记录
type MerchantPaymentLogModel struct {
	ID            int64      `gorm:"primaryKey;autoIncrement" json:"id"` // 主键，自增
	MerchantNo    string     `json:"merchant_no"`                        // 商户编号
	OrderNo       string     `json:"order_no"`                           // 订单编号
	OrderType     int64      `json:"order_type"`                         // 订单类型
	PaymentTypeID int64      `json:"payment_type_id"`                    // 平台支付方式ID
	PaymentNo     string     `json:"payment_no"`                         // 支付编号
	ClientIP      string     `json:"client_ip"`                          // 客户端IP
	Amount        int64      `json:"amount"`                             // 支付金额
	OutTradeNo    string     `json:"out_trade_no"`                       // 商户支付编号
	MchID         string     `json:"mch_id"`                             // 主商户号
	SubMchID      string     `json:"sub_mch_id"`                         // 特约商户号
	OpenID        string     `json:"open_id"`                            // 微信openid
	PayType       string     `json:"pay_type"`                           // 支付方式：wechatx/alipay
	TradeType     string     `json:"trade_type"`                         // 交易类型：NATIVE/MICROPAY等
	TradeState    string     `json:"trade_state"`                        // 交易状态
	TradeDesc     string     `json:"trade_desc"`                         // 交易状态说明
	TransactionID string     `json:"transaction_id"`                     // 交易单号
	Subject       string     `json:"subject"`                            // 备注信息
	Remark        *string    `json:"remark"`                             // 附加信息
	Status        int64      `json:"status;default:0"`                   // 状态：0 未支付 1已支付 2 已退款
	ExpiresAt     *time.Time `json:"expires_at"`                         // 过期时间
	PaidAt        *time.Time `json:"paid_at"`                            // 付款时间
	CreatedAt     time.Time  `json:"created_at"`                         // 创建时间
	UpdatedAt     time.Time  `json:"updated_at"`                         // 更新时间
}

func (model *MerchantPaymentLogModel) TableName() string {
	return "merchant_payment_logs"
}

// IsExpired 判断是否过期
func (model *MerchantPaymentLogModel) IsExpired() bool {
	return model.ExpiresAt.Before(time.Now())
}

// IsPaid 判断是否过期
func (model *MerchantPaymentLogModel) IsPaid() bool {
	return model.Status == consts.PAY_STATUS_PAID
}

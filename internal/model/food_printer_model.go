package model

import (
	"time"
)

// FoodPrinterModel 美食-打印机关联Model
type FoodPrinterModel struct {
	ID         uint64     `gorm:"column:id,primary_key;autoIncrement"` // 主键ID
	MerchantNo string     `gorm:"column:merchant_no"`                  // 商户号
	FoodID     int64      `gorm:"column:food_id"`                      // 美食ID
	PrinterID  string     `gorm:"column:printer_id"`                   // 打印机ID
	CreatedAt  *time.Time `gorm:"column:created_at"`                   // 创建时间
	UpdatedAt  *time.Time `gorm:"column:updated_at"`                   // 更新时间
	DeletedAt  *time.Time `gorm:"column:deleted_at"`                   // 删除时间
}

func (a *FoodPrinterModel) TableName() string {
	return "food_printers"
}

package model

import "time"

const (
	CustomerStateActive   = 1  // 正常状态
	CustomerStateInactive = 0  // 禁用状态
	CustomerStateDeleted  = -1 // 删除状态
)

// CustomerModel 客户会员表
type CustomerModel struct {
	ID               int64      `json:"id" gorm:"primaryKey;autoIncrement"` // Unique ID
	MerchantNo       string     `gorm:"column:merchant_no"`                 // 商家编号
	Mobile           string     `gorm:"column:mobile"`                      // 手机号
	CardNumber       []byte     `gorm:"column:card_number"`                 // vip卡号
	Password         string     `gorm:"column:password"`                    // 会员密码
	Name             string     `gorm:"column:name"`                        // 姓名
	Sex              int64      `gorm:"column:sex"`                         // 性别（0男，1女）
	BirthDay         *time.Time `gorm:"column:birth_day" `                  // 出生日期
	Level            int64      `gorm:"column:level"`                       // 会员等级
	ConsumptionTimes int64      `gorm:"column:consumption_times"`           // 消费次数
	Integration      int64      `gorm:"column:integration"`                 // 会员积分
	Balance          float64    `gorm:"column:balance"`                     // 会员余额
	CashierID        int64      `gorm:"column:cashier_id" `                 // 开卡人（员工）编号
	OutTradeNo       string     `gorm:"column:out_trade_no"`                // 会员充值时候生成，以便于扫码付款后，查询查询结果
	State            int64      `gorm:"column:state"`                       // 状态
	Remark           string     `gorm:"column:remark"`                      // 备注
	IsSync           int64      `gorm:"column:is_sync"`                     // 是否已同步
	CreatedAt        *time.Time `gorm:"column:created_at" `                 // 创建时间
	UpdatedAt        *time.Time `gorm:"column:updated_at" `                 // 更新时间
	DeletedAt        *time.Time `gorm:"column:deleted_at" `                 // 删除时间
}

// TableName sets the insert table name for this struct type
func (c *CustomerModel) TableName() string {
	return "customers"
}

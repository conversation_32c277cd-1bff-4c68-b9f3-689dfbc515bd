package model

import (
	"time"
)

// HandoverLogModel 交班记录
type HandoverLogModel struct {
	ID               int64                  `gorm:"column:id"`                                                 // 主键ID
	MerchantNo       string                 `gorm:"column:merchant_no"`                                        // 商户号
	UserID           int64                  `gorm:"column:user_id"`                                            // 用户ID
	StartAt          time.Time              `gorm:"column:start_at"`                                           // 交班开始时间
	LeaveAt          *time.Time             `gorm:"column:leave_at"`                                           // 交班结束时间
	Shift            int64                  `gorm:"column:shift"`                                              // 班次
	OrderCount       int64                  `gorm:"column:order_count"`                                        // 订单数量
	ReceivableAmount float64                `gorm:"column:receivable_amount"`                                  // 应收金额
	PaidAmount       float64                `gorm:"column:paid_amount"`                                        // 实收金额
	RefundAmount     float64                `gorm:"column:refund_amount"`                                      // 退款金额
	WorkingBalance   float64                `gorm:"column:working_balance"`                                    // 工作余额
	AlternateAmount  float64                `gorm:"column:alternate_amount"`                                   // 代付金额
	CreatedAt        time.Time              `gorm:"column:created_at"`                                         // 创建时间
	UpdatedAt        time.Time              `gorm:"column:updated_at"`                                         // 更新时间
	HandoverDetails  []HandoverDetailModel  `gorm:"foreignKey:HandoverLogID;references:ID"`                    // 交班明细
	Cashier          *MerchantEmployeeModel `gorm:"foreignKey:UserID,MerchantNo;references:UserID,MerchantNo"` // 收银员
	ShiftModel       *ShiftModel            `gorm:"foreignKey:Shift"`                                          // 班次
	SubmittedAmount  float64                `gorm:"column:submitted_amount;<-:false"`                          // 提交金额 (只读字段, 联合查询获取)
}

func (a *HandoverLogModel) TableName() string {
	return "handover_logs"
}

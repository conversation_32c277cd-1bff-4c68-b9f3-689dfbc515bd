package model

import (
	"time"
)

// MerchantRefundLogModel 商户退款记录
type MerchantRefundLogModel struct {
	ID            int64      `gorm:"primaryKey;autoIncrement" json:"id"` // 主键，自增
	MerchantNo    string     `json:"merchant_no"`                        // 商户编号
	BatchID       int64      `json:"batch_id"`                           // 批次号
	OrderID       int64      `json:"order_id"`                           // 订单ID
	OrderNo       string     `json:"order_no"`                           // 订单编号
	OrderType     int64      `json:"order_type"`                         // 订单类型 1 普通订单 2 会员充值
	RefundType    int64      `json:"refund_type"`                        // 退款类型: 1: 支付退款（撤销付款）, 2: 已结账订单退菜
	PaymentTypeID int64      `json:"payment_type_id"`                    // 平台支付方式ID
	PaymentLogID  int64      `json:"payment_log_id"`                     // 支付记录ID
	PaymentNo     string     `json:"payment_no"`                         // 支付编号
	PayAmount     int64      `json:"pay_amount"`                         // 支付金额
	Amount        int64      `json:"amount"`                             // 退款金额
	OutTradeNo    string     `json:"out_trade_no"`                       // 商户支付编号
	OutRefundNo   string     `json:"out_refund_no"`                      // 商户退款编号
	MchID         string     `json:"mch_id"`                             // 主商户号
	SubMchID      string     `json:"sub_mch_id"`                         // 特约商户号
	TradeState    string     `json:"trade_state"`                        // 退款状态(退款接口状态)
	TradeDesc     string     `json:"trade_desc"`                         // 退款状态说明
	TradeType     string     `json:"trade_type"`                         // 交易类型
	RefundID      string     `json:"refund_id"`                          // 微信退款单号
	Reason        string     `json:"reason"`                             // 备注信息
	Remark        *string    `json:"remark"`                             // 附加信息
	RefundStatus  int64      `json:"refund_status;default:0"`            // 状态：0 未退款 1： 已退款 2：退款失败
	RefundAt      *time.Time `json:"refund_at"`                          // 退款时间
	CreatedAt     time.Time  `json:"created_at"`                         // 创建时间
	UpdatedAt     time.Time  `json:"updated_at"`                         // 更新时间

	PaymentType *PaymentTypeModel `gorm:"foreignKey:PaymentTypeID;references:ID" json:"payment_type"` // 支付方式
}

func (model *MerchantRefundLogModel) TableName() string {
	return "merchant_refund_logs"
}

package model

import (
	"time"
)

const (
	ShiftStateOpen   = 1 // 班次开启
	ShiftStateClosed = 2 // 班次关闭
)

// ShiftModel 班次模型
type ShiftModel struct {
	ID         int64     `gorm:"column:id;primaryKey;"` // Unique ID
	MerchantNo string    `gorm:"column:merchant_no"`    // 商家编号
	NameUg     string    `gorm:"column:name_ug"`        // 名称(维语)
	NameZh     string    `gorm:"column:name_zh"`        // 名称(中文)
	Sort       int64     `gorm:"column:sort"`           // 排序
	State      int64     `gorm:"column:state"`          // 状态
	CreatedAt  time.Time `gorm:"column:created_at"`     // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at"`     // 更新时间
}

func (a *ShiftModel) TableName() string {
	return "shifts"
}

package model

// MqttAcl mqtt授权表
type MqttAcl struct {
	ID         uint   `gorm:"primaryKey;autoIncrement"`
	MerchantNo string `gorm:"size:10"`
	Username   string `gorm:"size:100;not null"`
	Permission string `gorm:"size:5;not null"`
	Action     string `gorm:"size:20;not null;collation:utf8mb4_0900_ai_ci"`
	Topic      string `gorm:"size:100;not null"`
	Qos        uint8  `gorm:"size:1"`
	Retain     *uint8 `gorm:"size:1"`
}

// TableName 返回MqttAcl结构对应的数据库表名。
// 该方法属于MqttAcl类型，用于指定与之关联的数据库表名。
// 返回值为字符串"mqtt_acl"，表示数据库中的表名。
func (MqttAcl) TableName() string {
	return "mqtt_acl"
}

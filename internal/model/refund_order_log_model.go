package model

import (
	"time"
)

type RefundOrderLogModel struct {
	ID                int64                  `gorm:"column:id;primaryKey;autoIncrement"`                           // ID
	MerchantNo        string                 `gorm:"column:merchant_no"`                                           // 商户号
	BatchID           int64                  `gorm:"column:batch_id"`                                              // 批次ID
	OrderID           int64                  `gorm:"column:order_id"`                                              // 订单ID
	OrderDetailID     int64                  `gorm:"column:order_detail_id"`                                       // 订单详情ID
	FoodID            int64                  `gorm:"column:food_id"`                                               // 菜品ID
	RefundCount       float64                `gorm:"column:refund_count"`                                          // 退款数量
	RefundAmount      float64                `gorm:"column:refund_amount"`                                         // 退款金额
	RefundPaymentType int64                  `gorm:"column:refund_payment_type"`                                   // 退款方式
	CashierID         int64                  `gorm:"column:cashier_id"`                                            // 收银员ID
	Remarks           string                 `gorm:"column:remarks"`                                               // 备注
	State             int64                  `gorm:"column:state"`                                                 // 状态 1-成功 2-失败
	CreatedAt         time.Time              `gorm:"column:created_at"`                                            // 创建时间
	UpdatedAt         time.Time              `gorm:"column:updated_at"`                                            // 更新时间
	DeletedAt         *time.Time             `gorm:"column:deleted_at"`                                            // 删除时间
	Food              *FoodModel             `gorm:"foreignKey:FoodID;references:ID"`                              // 菜品
	Cashier           *MerchantEmployeeModel `gorm:"foreignKey:CashierID,MerchantNo;references:UserID,MerchantNo"` // 收银员
}

func (RefundOrderLogModel) TableName() string {
	return "refund_order_logs"
}

package model

import (
	"time"
)

const (
	PaymentTypeStateNormal   = 1
	PaymentTypeStateDisabled = 0
)

// PaymentTypeModel 支付方式Model
type PaymentTypeModel struct {
	ID              int64                     `gorm:"column:id;primaryKey;"`                   // Unique ID
	Tag             string                    `gorm:"column:tag;"`                             // 标签
	NameUg          string                    `gorm:"column:name_ug;"`                         // 名称(维语)
	NameZh          string                    `gorm:"column:name_zh;"`                         // 名称(中文)
	Icon            string                    `gorm:"column:icon"`                             // 图标
	Type            int64                     `gorm:"column:type"`                             // 类型
	PayMethod       int64                     `gorm:"column:pay_method"`                       // 支付方式
	Online          int64                     `gorm:"column:online"`                           // 在线支付
	Sort            int64                     `gorm:"column:sort"`                             // 排序
	State           int64                     `gorm:"column:state"`                            // 状态
	CreatedAt       time.Time                 `gorm:"column:created_at"`                       // 创建时间
	UpdatedAt       time.Time                 `gorm:"column:updated_at"`                       // 更新时间
	DeletedAt       *time.Time                `gorm:"column:deleted_at"`                       // 删除时间
	MerchantPayment *MerchantPaymentTypeModel `gorm:"foreignKey:PaymentTypeID;references:ID;"` // 商户支付方式(需要根据商家编号查询)
}

func (a *PaymentTypeModel) TableName() string {
	return "payment_types"
}

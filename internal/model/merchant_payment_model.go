package model

import (
	"time"
)

// MerchantPaymentModel 商户支付记录
type MerchantPaymentModel struct {
	ID            int64                     `gorm:"column:id;primaryKey;autoIncrement"`                           // 主键，自增
	MerchantNo    string                    `gorm:"column:merchant_no"`                                           // 商户编号
	OrderID       int64                     `gorm:"column:order_id"`                                              // 订单ID
	OrderNo       string                    `gorm:"column:order_no"`                                              // 订单编号
	PaymentTypeID int64                     `gorm:"column:payment_type_id"`                                       // 付款类型ID
	CustomerID    int64                     `gorm:"column:customer_id"`                                           // 客户ID
	PaymentNo     string                    `gorm:"column:payment_no"`                                            // 付款编号
	Amount        int64                     `gorm:"column:amount"`                                                // 付款金额
	PayType       string                    `gorm:"column:pay_type"`                                              // 付款类型
	Remark        *string                   `gorm:"column:remark"`                                                // 备注
	Status        int64                     `gorm:"column:status"`                                                // 付款状态
	ExpiresAt     *time.Time                `gorm:"column:expires_at"`                                            // 过期时间
	PaidAt        *time.Time                `gorm:"column:paid_at"`                                               // 付款时间
	CreatedAt     time.Time                 `gorm:"column:created_at"`                                            // 创建时间
	UpdatedAt     time.Time                 `gorm:"column:updated_at"`                                            // 更新时间
	CashierID     int64                     `gorm:"column:cashier_id"`                                            // 收银员ID
	PaymentType   *PaymentTypeModel         `gorm:"foreignKey:PaymentTypeID"`                                     // 付款类型
	PaymentLog    *MerchantPaymentLogModel  `gorm:"foreignKey:PaymentNo"`                                         // 付款日志
	RefundLogs    []*MerchantRefundLogModel `gorm:"foreignKey:PaymentNo;references:PaymentNo"`                    // 退款订单日志
	Cashier       *MerchantEmployeeModel    `gorm:"foreignKey:CashierID,MerchantNo;references:UserID,MerchantNo"` // 收银员
	RefundAmount  int64                     `gorm:"<-:false;column:refund_amount"`                                // 退款金额(联合查询)
}

func (a *MerchantPaymentModel) TableName() string {
	return "merchant_payments"
}

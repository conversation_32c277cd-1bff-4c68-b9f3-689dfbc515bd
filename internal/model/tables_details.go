package model

import (
	"ros-api-go/pkg/util"
	"time"
)

type TablesDetails struct {
	ID              int      `json:"id" gorm:"primaryKey"`
	CustomersCount  int      `json:"customers_count"`
	AreaId          int      `json:"area_id"`
	NameUg          string   `json:"name_ug"`
	NameZh          string   `json:"name_zh"`
	No              string   `json:"no"`
	SeatingCapacity int      `json:"seating_capacity"`
	Orders          *[]Order `json:"orders" gorm:"foreignKey:TableId;references:ID"`
}

type Order struct {
	ID              int64          `json:"id"`
	No              string         `json:"order_no" gorm:"no"`
	TableID         int64          `json:"table_id"`
	CustomersCount  int            `json:"customers_count"`
	FoodsCount      float64        `json:"foods_count"`
	OriginalPrice   float64        `json:"original_price"`
	VipPrice        float64        `json:"vip_price"`
	Price           float64        `json:"price"`
	IgnorePrice     float64        `json:"ignore_price"`
	User            string         `json:"user"`
	UserId          int            `json:"user_id"`
	Remarks         string         `json:"remarks"`
	CreatedAt       string         `json:"created_at"`
	CashierId       int            `json:"cashier_id"`       //结账人id
	WechatUserId    int            `json:"wechat_user_id"`   //微信用户id
	CashierName     string         `json:"cashier_name"`     //结账人
	GiveChange      float64        `json:"give_change"`      //找零金额
	CollectedAmount float64        `json:"collected_amount"` //收款金额
	OrderDetails    []*OrderDetail `json:"order_details" gorm:"foreignKey:OrderNo;references:No"`
	CanceledFoods   []*OrderDetail `json:"canceled_foods" gorm:"foreignKey:OrderNo;references:No"`
}

type OrderDetail struct {
	Id             int                     `json:"id"`
	OrderDetailsId int                     `json:"order_details_id"` //退菜订单的原始订单id
	MerchantNo     string                  `json:"merchant_no"`
	OrderId        int                     `json:"order_id"`
	OrderNo        int                     `json:"order_no"`
	FoodId         int                     `json:"food_id"`
	FoodsCount     float64                 `json:"foods_count"`
	CostPrice      float64                 `json:"cost_price"`
	OriginalPrice  float64                 `json:"original_price"`
	VipPrice       float64                 `json:"vip_price"`
	Price          float64                 `json:"price"`
	TotalPrice     float64                 `json:"total_price"`
	IsPrint        bool                    `json:"is_print"`
	User           string                  `json:"user"`
	UserId         int                     `json:"user_id"`
	State          int                     `json:"state"` //订单状态 - 1 新订单（人数确定，未点菜）、2 未支付订单（已点菜，订单已提交）、3 已结账订单（已结账）、4 退菜、5 取消退菜 、6 被并单
	Remarks        string                  `json:"remarks"`
	IsCombo        bool                    `json:"is_combo"`
	ComboInfo      *util.TArray[ComboInfo] `json:"combo_info" gorm:"type:text"`
	IsSync         bool                    `json:"is_sync"`
	CreatedAt      time.Time               `json:"created_at"`
	UpdatedAt      time.Time               `json:"updated_at"`

	FoodNameUg   string `json:"food_name_ug"`
	FoodNameZh   string `json:"food_name_zh"`
	FoodFormatId int    `json:"food_format_id"`

	PrinterId int `json:"printer_id"`
}

func (model *TablesDetails) TableName() string {
	return "t_tables"
}
func (model *Order) TableName() string {
	return "cloud_orders"
}
func (model *OrderDetail) TableName() string {
	return "order_details"
}

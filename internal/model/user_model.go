package model

import (
	"time"
)

const (
	UserStatusActivated = 1
	UserStatusFreezed   = 0
)

// UserModel 用户
type UserModel struct {
	ID                int64                    `json:"id" gorm:"size:20;primaryKey;"`      // Unique ID
	Avatar            string                   `json:"avatar" gorm:"size:255"`             // 头像
	JobNo             string                   `json:"job_no" gorm:"size:9"`               // 员工编号
	Name              string                   `json:"name" gorm:"size:32"`                // 姓名
	Phone             string                   `json:"phone" gorm:"size:12;"`              // 手机号
	Password          string                   `json:"password" gorm:"size:64;"`           // 密码
	RoleID            int64                    `json:"role_id" gorm:"size:128;"`           // 角色名称
	ApiToken          string                   `json:"api_token" gorm:"size:255;"`         // 登录token
	State             int64                    `json:"state" gorm:"size:20"`               // 状态 - 1 开启 0 关闭
	LeavedAt          time.Time                `json:"leaved_at"`                          // 离职时间
	LastLogin         time.Time                `json:"last_login"`                         // 最后登录
	CreatedAt         time.Time                `json:"created_at"`                         // Create time
	UpdatedAt         time.Time                `json:"updated_at"`                         // Update time
	DeletedAt         *time.Time               `json:"deleted_at"`                         // Delete time
	Employees         []*MerchantEmployeeModel `gorm:"foreignKey:UserID"`                  // Employees of user
	Employee          *MerchantEmployeeModel   `gorm:"foreignKey:UserID"`                  // Employee of user
	MerchantNo        string                   `gorm:"<-:false;column:merchant_no"`        // 商户号
	NameUg            string                   `gorm:"<-:false;column:name_ug"`            // 员工姓名(英文)
	NameZh            string                   `gorm:"<-:false;column:name_zh"`            // 员工姓名(中文)
	IsOwner           bool                     `gorm:"<-:false;column:is_owner"`           // 是否为店主
	OperationPassword string                   `gorm:"<-:false;column:operation_password"` // 操作密码
	// Roles     UserRoles `json:"roles" gorm:"-"`                // Roles of user
}

func (a *UserModel) TableName() string {
	return "users"
}

// Defining the slice of `UserModel` struct.
type Users []*UserModel

func (a Users) ToIDs() []int64 {
	var ids []int64
	for _, item := range a {
		ids = append(ids, item.ID)
	}
	return ids
}

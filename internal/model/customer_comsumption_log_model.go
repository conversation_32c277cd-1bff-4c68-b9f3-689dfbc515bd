package model

import "time"

const (
	ConsumptionTypeConsume  = 1 // 消费
	ConsumptionTypeRecharge = 2 // 充值
	ConsumptionTypeRefund   = 3 // 已结账订单退款充值
)

type CustomerConsumptionLogModel struct {
	ID            int64      `json:"id" gorm:"primaryKey;autoIncrement"`  // Unique ID
	MerchantNo    string     `gorm:"column:merchant_no"`                  // 商家编号
	CustomerID    int64      `gorm:"column:customer_id"`                  // 客户编号
	Type          int64      `gorm:"column:type"`                         // 类型,1:消费，2：充值 3: 退款
	OrderID       int64      `gorm:"column:order_id;autoIncrement:false"` // orders表的订单编号、type字段为2是为0
	Amount        float64    `gorm:"column:amount"`                       // type:1 消费金额，type:2 充值金额 type：3 退款金额
	PresentAmount float64    `gorm:"column:present_amount"`               // 赠送金额
	Balance       float64    `gorm:"column:balance"`                      // 会员当前余额
	PaymentTypeID int64      `gorm:"column:payment_type_id"`              // 付款方式编号
	CashierID     int64      `gorm:"column:cashier_id"`                   // 经办用户编号
	IsSync        int64      `gorm:"column:is_sync"`                      // 是否已同步
	CreatedAt     *time.Time `gorm:"column:created_at"`                   // 创建时间
	UpdatedAt     *time.Time `gorm:"column:updated_at"`                   // 更新时间
}

// TableName sets the insert table name for this struct type
func (c *CustomerConsumptionLogModel) TableName() string {
	return "customer_consumption_logs"
}

package model

import (
	"time"
)

// HandoverDetailModel 交班记录明细表模型
type HandoverDetailModel struct {
	ID                    int64     `gorm:"column:id;primaryKey;"`          // Unique ID
	MerchantNo            string    `gorm:"column:merchant_no"`             // 商家编号
	UserID                int64     `gorm:"column:user_id"`                 // 交班用户编号
	HandoverLogID         int64     `gorm:"column:handover_log_id"`         // 交班记录编号
	PaymentTypeID         int64     `gorm:"column:payment_type_id"`         // 支付类型编号
	OrderCount            int       `gorm:"column:order_count"`             // 订单数量
	OrderAmount           float64   `gorm:"column:order_amount"`            // 订单金额
	RefundAmount          float64   `gorm:"column:refund_amount"`           // 退款金额
	VipCount              int       `gorm:"column:vip_count"`               // 新增会员数量
	VipRechargeAmount     float64   `gorm:"column:vip_recharge_amount"`     // 会员充值金额
	PresentRechargeAmount float64   `gorm:"column:present_recharge_amount"` // 会员赠送金额
	DebtRepaymentAmount   float64   `gorm:"column:debt_repayment_amount"`   // 赊账还清金额
	CreatedAt             time.Time `gorm:"column:created_at"`              // 创建时间
	UpdatedAt             time.Time `gorm:"column:updated_at"`              // 更新时间
	// 以下字段仅用于查询，不入库
	OrderCashAmount         float64 `gorm:"<-:false;column:order_cash_amount"`          // 订单现金金额
	VipCashRechargeAmount   float64 `gorm:"<-:false;column:vip_cash_recharge_amount"`   // 会员现金充值金额
	RefundCashAmount        float64 `gorm:"<-:false;column:refund_cash_amount"`         // 退款金额
	DebtRepaymentCashAmount float64 `gorm:"<-:false;column:debt_repayment_cash_amount"` // 退款金额

	PaymentType *PaymentTypeModel `gorm:"foreignkey:PaymentTypeID"` // 支付类型
	HandoverLog *HandoverLogModel `gorm:"foreignkey:HandoverLogID"` // 交班记录
}

// TableName 设置表名
func (model *HandoverDetailModel) TableName() string {
	return "handover_details"
}

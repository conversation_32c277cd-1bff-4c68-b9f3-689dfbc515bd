package model

import (
	"gorm.io/gorm"
	"time"
)

// FoodSpecModel 规格管理
type FoodSpecModel struct {
	ID         int64          `json:"id" gorm:"size:20;primaryKey;"` // Unique ID
	MerchantNo string         `json:"merchant_no" gorm:"size:20;"`   // 商家编号
	NameZh     string         `json:"name_zh" gorm:"size:128;"`      // 规格名称(中文)
	NameUg     string         `json:"name_ug" gorm:"size:128;"`      // 规格名称(维语)
	Sort       int64          `json:"sort" gorm:"default:0"`         // 排序
	CreatedAt  time.Time      `gorm:"column:created_at"`             // 创建时间
	UpdatedAt  time.Time      `gorm:"column:updated_at"`             // 更新时间
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at"`             // 删除时间

	// 关联字段
	FoodsCount int64 `gorm:"<-:false;column:foods_count"` // 关联美食数量
}

func (a *FoodSpecModel) TableName() string {
	return "food_specs"
}

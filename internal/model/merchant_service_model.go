package model

import "time"

type MerchantServiceModel struct {
	ID            int        `gorm:"column:id;primary_key;auto_increment"`
	MerchantNo    string     `gorm:"column:merchant_no"`
	ServiceNameUg string     `gorm:"column:service_name_ug"`
	ServiceNameZh string     `gorm:"column:service_name_zh"`
	Image         string     `gorm:"column:image"`
	Sort          int        `gorm:"column:sort;default:0"`
	State         int        `gorm:"column:state;default:1"`
	CreatedAt     *time.Time `gorm:"column:created_at"`
	UpdatedAt     *time.Time `gorm:"column:updated_at"`
}

func (MerchantServiceModel) TableName() string {
	return "merchant_services"
}

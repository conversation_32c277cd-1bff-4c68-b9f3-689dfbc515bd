package model

import (
	"time"
)

// RemarkCategoryModel 菜品分类
type RemarkCategoryModel struct {
	ID        int64     `json:"id" gorm:"size:20;primaryKey;"` // Unique ID
	Tag       string    `json:"tag" gorm:"size:20;"`           // 标签
	NameUg    string    `json:"name_ug" gorm:"size:50;"`       // 名称(维语)
	NameZh    string    `json:"name_zh" gorm:"size:50;"`       // 名称(中文)
	CreatedAt time.Time `json:"created_at"`                    // Create time
	UpdatedAt time.Time `json:"updated_at"`                    // Update time
}

func (a *RemarkCategoryModel) TableName() string {
	return "remark_categories"
}

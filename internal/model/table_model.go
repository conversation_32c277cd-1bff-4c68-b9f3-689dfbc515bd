package model

import (
	"time"
)

// TableModel 餐厅餐桌信息
type TableModel struct {
	ID              int64      `json:"id" gorm:"size:20;primaryKey;"` // Unique ID
	MerchantNo      string     `json:"merchant_no" gorm:"size:20;"`   // 商家编号
	AreaID          int64      `json:"area_id" gorm:"size:20;"`       // 区域ID
	No              string     `json:"no" gorm:"size:20;"`            // 编号
	NameUg          string     `json:"name_ug" gorm:"size:50;"`       // 名称(维语)
	NameZh          string     `json:"name_zh" gorm:"size:50;"`       // 名称(中文)
	SeatingCapacity int        `json:"seating_capacity"`              // 座位数
	Sort            int64      `json:"sort"`                          // 排序
	State           int64      `json:"state" gorm:"size:10;"`         // 状态
	CreatedAt       time.Time  `gorm:"column:created_at"`             // 创建时间
	UpdatedAt       time.Time  `gorm:"column:updated_at"`             // 更新时间
	DeletedAt       *time.Time `gorm:"column:deleted_at"`             // 删除时间

	CloudOrders []*CloudOrderModel `json:"-" gorm:"foreignKey:TableID;references:ID"`
	Orders      []*OrderModel      `json:"-" gorm:"foreignKey:TableID;references:ID"`
}

type TableDataModel struct {
	ID              int64  `json:"id" gorm:"size:20;primaryKey;"` // Unique ID
	AreaId          int64  `json:"area_id" gorm:"size:20;"`       // 区域ID
	No              string `json:"no" gorm:"size:20;"`            // 编号
	NameUg          string `json:"name_ug" gorm:"size:50;"`       // 名称(维语)
	NameZh          string `json:"name_zh" gorm:"size:50;"`       // 名称(中文)
	Sort            int64  `json:"sort"`
	State           int64  `json:"state" gorm:"size:10;"` // 状态
	SeatingCapacity int64  `json:"seating_capacity"`
	LastWechatTime  string `json:"last_wechat_time"`

	Orders []*CloudOrderModel `json:"-" gorm:"foreignKey:TableID;references:ID"`

	OrdersCount       int `json:"orders_count" gorm:"-"`
	OrderDetailsCount int `json:"order_details_count" gorm:"-"`
	CustomersCount    int `json:"customers_count" gorm:"-"`
	TableStatus       int `json:"table_status" gorm:"-"`
}

func (a *TableModel) TableName() string {
	return "t_tables"
}

func (a *TableDataModel) TableName() string {
	return "t_tables"
}

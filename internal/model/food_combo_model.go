package model

import (
	"time"
)

// FoodComboModel 套餐
type FoodComboModel struct {
	ID          int64      `json:"id" gorm:"size:20;primaryKey;"` // Unique ID
	MerchantNo  string     `json:"merchant_no" gorm:"size:20;"`   // 商家编号
	ComboID     int64      `json:"combo_id" gorm:"size:20;"`      // 套餐 ID
	Type        string     `json:"type" gorm:"size:20;"`          // 类型
	ParentID    int64      `json:"parent_id" gorm:"size:20;"`     // 父级 ID
	NameUg      string     `json:"name_ug" gorm:"size:50;"`       // 名称(维语)
	NameZh      string     `json:"name_zh" gorm:"size:50;"`       // 名称(中文)
	FoodID      int64      `json:"food_id" gorm:"size:20;"`       // 菜品 ID
	OriginPrice float64    `json:"origin_price"`                  // 原价
	ComboPrice  float64    `json:"combo_price"`                   // 套餐价
	Count       int        `json:"count"`                         // 数量
	CreatedAt   time.Time  `gorm:"column:created_at"`             // 创建时间
	UpdatedAt   time.Time  `gorm:"column:updated_at"`             // 更新时间
	DeletedAt   *time.Time `gorm:"column:deleted_at"`             // 删除时间

	Childs []FoodComboModel `gorm:"foreignkey:ParentID;references:ID"` // 子套餐
}

func (a *FoodComboModel) TableName() string {
	return "food_combo"
}

package model

import "time"

type ServiceHistoryModel struct {
	ID            int       `gorm:"column:id;primary_key;auto_increment"`
	MerchantNo    string    `gorm:"column:merchant_no;not null"`      // 商户号
	TableID       int64     `gorm:"column:table_id;not null"`         // 餐桌号
	ServiceNameUg string    `gorm:"column:service_name_ug"`           // 服务名称
	ServiceNameZh string    `gorm:"column:service_name_zh"`           // 服务名称（中文）
	Status        int       `gorm:"column:status;default:0;not null"` // 状态
	UpdatedAt     time.Time `gorm:"column:updated_at"`                // 更新时间
	CreatedAt     time.Time `gorm:"column:created_at"`                // 创建时间
}

func (ServiceHistoryModel) TableName() string {
	return "service_history"
}

package model

import (
	"time"
)

const (
	ServerActivated = 1
	ServerInactive  = 0
)

// LocalServerModel 本地服务
type LocalServerModel struct {
	ID         int64     `json:"id" gorm:"primaryKey;autoIncrement"` // Unique ID
	MerchantNo string    `json:"merchant_no" gorm:"size:16;"`        // 商家唯一编号
	UUID       string    `json:"uuid" gorm:"size:32;unique;"`        // 本地服务识别码
	UserID     int64     `json:"user_id" gorm:"size:32;"`            // 主机登录用户ID
	Ipv4       string    `json:"ipv4" gorm:"size:128;"`              // ipv4
	Ipv6       string    `json:"ipv6" gorm:"size:128;"`              // ipv6
	Token      string    `json:"token" gorm:"size:32;unique;"`       // 授权码
	Status     int64     `json:"status" gorm:"size:1;"`              // 状态 0-未启用 1-启用 2-禁用
	CreatedAt  time.Time `json:"created_at"`                         // Create time
	UpdatedAt  time.Time `json:"updated_at"`                         // Update time
}

func (a *LocalServerModel) TableName() string {
	return "local_servers"
}

package model

import (
	"time"
)

// MerchantUserRoleModel 用户角色关系表
type MerchantUserRoleModel struct {
	ID         int64     `json:"id" gorm:"size:20;primaryKey"`     // ID
	MerchantNo string    `json:"merchant_no" gorm:"size:20;index"` // 商家编号
	UserID     int64     `json:"user_id" gorm:"size:20;index"`     // 用户ID
	RoleID     int64     `json:"role_id" gorm:"size:20;index"`     // 角色ID
	CreatedAt  time.Time `json:"created_at"`                       // Create time
	UpdatedAt  time.Time `json:"updated_at"`                       // Update time
}

func (a *MerchantUserRoleModel) TableName() string {
	return "user_roles"
}

type MerchantUserRoles []*MerchantUserRoleModel

// ToUserIDMap 提取用户ID和角色列表的映射
func (a MerchantUserRoles) ToUserIDMap() map[int64]MerchantUserRoles {
	m := make(map[int64]MerchantUserRoles)
	for _, userRole := range a {
		m[userRole.UserID] = append(m[userRole.UserID], userRole)
	}
	return m
}

// ToRoleIDs 提取角色ID列表
func (a MerchantUserRoles) ToRoleIDs() []int64 {
	var ids []int64
	for _, item := range a {
		ids = append(ids, item.RoleID)
	}
	return ids
}

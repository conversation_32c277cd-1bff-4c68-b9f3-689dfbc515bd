package model

import (
	"time"
)

const (
	PrinterStateEnabled  = 1
	PrinterStateDisabled = 0
	PrinterStateDeleted  = -1
)

// PrinterModel 打印机模型

type PrinterModel struct {
	ID             string     `gorm:"column:id;size:20;primaryKey;"` // Unique ID
	MerchantNo     string     `gorm:"column:merchant_no"`            // 商户号
	NameZh         string     `gorm:"column:name_zh"`                // 中文名称
	NameUg         string     `gorm:"column:name_ug"`                // 维语名称
	ConnectionType string     `gorm:"column:connection_type"`        // 连接方式 network/usb/inner(内置打印机)/cloud
	IpAddress      string     `gorm:"column:ip_address"`             // IP地址(网络打印机使用)
	UsbPort        string     `gorm:"column:usb_port"`               // USB端口号(USB打印机使用)
	Cloud          string     `gorm:"column:cloud"`                  // 云打印机号
	PaperWidth     int        `gorm:"column:paper_width"`            // 纸张宽度(58mm或80mm)
	PrintMode      string     `gorm:"column:print_mode"`             // 打印方式 text/image
	Status         int        `gorm:"column:status"`                 // 打印机状态 0:禁用 1:启用 -1:删除
	Buzzer         bool       `gorm:"column:buzzer"`                 // 蜂鸣器	false:不提醒 true:提醒
	DeletedAt      *time.Time `gorm:"column:deleted_at;index"`       // 删除时间
	CreatedAt      *time.Time `gorm:"column:created_at"`             // 创建时间
	UpdatedAt      *time.Time `gorm:"column:updated_at"`             // 更新时间
	CashierConfig             // 收银打印机配置
	KitchenConfig             // 后厨打印机配置
}

func (p *PrinterModel) TableName() string {
	return "printers_local"
}

type CashierConfig struct {
	PreTicket       bool `json:"pre_ticket"`       // 预结账
	StatementTicket bool `json:"statement_ticket"` // 结账单
	RechargeTicket  bool `json:"recharge_ticket"`  // 会员充值记录
	BusinessTicket  bool `json:"business_ticket"`  // 日营业额
	DeliveryTicket  bool `json:"delivery_ticket"`  // 换班
}

type KitchenConfig struct {
	SplitTicket       bool `json:"split_ticket"`        // 一单一个
	BackTicket        bool `json:"back_ticket"`         // 退菜
	TableChangeTicket bool `json:"table_change_ticket"` // 转台
	OrderMergeTicket  bool `json:"order_merge_ticket"`  // 合并订单
}

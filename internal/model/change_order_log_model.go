package model

import "time"

type ChangeOrderLogModel struct {
	ID            int64      `json:"id" gorm:"primaryKey;autoIncrement"` // Unique ID
	MerchantNo    string     `gorm:"column:merchant_no"`                 // 商户号
	OrderID       int64      `gorm:"column:order_id"`                    // 订单ID
	OrderDetailID int64      `gorm:"column:order_detail_id"`             // 订单详情ID
	FoodID        int64      `gorm:"column:food_id"`                     // 菜品ID
	Count         float64    `gorm:"column:count"`                       // 数量
	RealPrice     float64    `gorm:"column:real_price"`                  // 实际价格
	TotalPrice    float64    `gorm:"column:total_price"`                 // 总价
	Type          int        `gorm:"column:type"`                        // 类型
	Discount      float64    `gorm:"column:discount"`                    // 折扣
	Remarks       string     `gorm:"column:remarks"`                     // 备注
	UserID        int64      `gorm:"column:user_id"`                     // 用户ID
	State         int        `gorm:"column:state"`                       // 状态
	CreatedAt     time.Time  `gorm:"column:created_at"`                  // 创建时间
	UpdatedAt     time.Time  `gorm:"column:updated_at"`                  // 更新时间
	DeletedAt     *time.Time `gorm:"column:deleted_at"`                  // 删除时间
}

func (c *ChangeOrderLogModel) TableName() string {
	return "change_order_logs"
}

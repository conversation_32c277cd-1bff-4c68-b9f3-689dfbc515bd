package model

import "time"

const (
	RechargeOrderStatusWaiting = 0 // 待充值
	RechargeOrderStatusSuccess = 1 // 充值成功
	RechargeOrderStatusFailed  = 2 // 充值失败

	RechargeOrderTypeCashier = 1 // 收银员充值
	RechargeOrderTypeMini    = 2 // 小程序充值
)

type CustomerRechargeOrderModel struct {
	ID             int64     `gorm:"column:id;primary_key;autoIncrement"` // 订单ID
	No             string    `gorm:"column:no"`                           // 订单号
	MerchantNo     string    `gorm:"column:merchant_no"`                  // 商户号
	CustomerID     int64     `gorm:"column:customer_id"`                  // 用户ID
	RechargeAmount int64     `gorm:"column:recharge_amount"`              // 充值金额
	PresentAmount  int64     `gorm:"column:present_amount"`               // 赠送金额
	PaymentTypeID  int64     `gorm:"column:payment_type_id"`              // 支付方式ID
	Status         int64     `gorm:"column:status"`                       // 订单状态： 0 ：待充值 1： 已充值、 2: 失败
	Type           int64     `gorm:"column:type"`                         // 充值类型： 1 收银员充值、2 小程序充值
	CashierID      int64     `gorm:"column:cashier_id"`                   // 收银员ID
	CreatedAt      time.Time `gorm:"column:created_at"`                   // 创建时间
	UpdatedAt      time.Time `gorm:"column:updated_at"`                   // 更新时间

	PaymentType *PaymentTypeModel      `gorm:"foreignkey:PaymentTypeID"`                                     // 关联支付方式
	Customer    *CustomerModel         `gorm:"foreignkey:CustomerID"`                                        // 关联用户
	Cashier     *MerchantEmployeeModel `gorm:"foreignKey:CashierID,MerchantNo;references:UserID,MerchantNo"` // 收银员
}

func (m *CustomerRechargeOrderModel) TableName() string {
	return "customer_recharge_orders"
}

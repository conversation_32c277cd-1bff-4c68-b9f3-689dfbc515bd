package model

import "time"

type CloudOrderModel struct {
	ID              int64      `json:"id"`
	MerchantNo      string     `json:"merchant_no"`      //商户号
	No              string     `json:"no"`               //订单号
	CustomerID      int64      `json:"customer_id"`      //客户id
	TableID         int64      `json:"table_id"`         //桌台id
	OpenID          string     `json:"open_id"`          //openid
	TerminalID      int64      `json:"terminal_id"`      //终端id
	CustomersCount  int        `json:"customers_count"`  //桌台人数
	FoodsCount      float64    `json:"foods_count"`      //菜品数量
	CostPrice       float64    `json:"cost_price"`       //订单成本价
	OriginalPrice   float64    `json:"original_price"`   //订单原价
	VipPrice        float64    `json:"vip_price"`        //订单会员价
	Price           float64    `json:"price"`            //实际付款
	IgnorePrice     float64    `json:"ignore_price"`     //忽略价格
	CollectedAmount float64    `json:"collected_amount"` //收款金额
	User            string     `json:"user"`             //订单操作人
	UserID          int64      `json:"user_id"`          //订单操作人id
	CashierID       int64      `json:"cashier_id"`       //结账人id
	CashierName     string     `json:"cashier_name"`     //结账人
	WechatUserID    int64      `json:"wechat_user_id"`   //微信用户id
	GiveChange      float64    `json:"give_change"`      //找零金额
	State           int64      `json:"state"`            //订单状态 - 1 新订单（人数确定，未点菜）、2 未支付订单（已点菜，订单已提交）、3 已结账订单（已结账）、4 取消订单（退菜）、5被拼单
	Remarks         string     `json:"remarks"`          //备注
	PaidAt          *time.Time `json:"paid_at"`          //付款时间
	TaxTicket       bool       `json:"tax_ticket"`       //是否开具发票
	IsPrint         bool       `json:"is_print"`         //是否打印
	IsScanOrder     bool       `json:"is_scan_order"`    //是否扫码点单
	IsSync          bool       `json:"is_sync"`          //是否同步
	CreatedAt       *time.Time `json:"created_at"`       //创建时间
	UpdatedAt       *time.Time `json:"updated_at"`       //更新时间

	// 关联关系
	Merchant *MerchantModel          `gorm:"foreignKey:MerchantNo;references:No"`                       // 商户
	Details  []*OrderDetailModel     `gorm:"foreignKey:OrderNo;references:No"`                          // 订单详情
	Payments []*MerchantPaymentModel `gorm:"foreignKey:OrderNo;references:No"`                          // 订单支付记录
	Creator  *MerchantEmployeeModel  `gorm:"foreignKey:UserID,MerchantNo;references:UserID,MerchantNo"` // 收银员
	Table    *TableModel             `gorm:"foreignKey:TableID;references:ID"`                          // 餐桌

}

func (CloudOrderModel) TableName() string {
	return "cloud_orders"
}

package model

import (
	"github.com/gin-gonic/gin"
	"time"
)

type ClientType string

var (
	ClientTypeManager    ClientType = "manager"
	ClientTypeCashier    ClientType = "cashier"
	ClientTypeWebCashier ClientType = "web-cashier"
	ClientTypeOrdering   ClientType = "ordering"
	ClientTypeOthers     ClientType = "others"
)

// 互斥策略配置 (登录时清空策略中的字段)
var mutexMap = map[ClientType][]ClientType{
	ClientTypeManager: nil, // 管理端登录时，不进行清空
	ClientTypeOthers:  nil,
}

type UserTokenModel struct {
	ID         int64      `gorm:"column:id;primary_key"` // 主键ID
	UserID     int64      `gorm:"column:user_id"`        // 用户ID
	ClientType ClientType `gorm:"column:client_type"`    // 客户端类型 (manager, cashier, ordering)
	UserToken  string     `gorm:"column:user_token"`     // 用户的token
	ExpiresAt  *time.Time `gorm:"column:expires_at"`     // 过期时间
	CreatedAt  *time.Time `gorm:"column:created_at"`     // 创建时间
	UpdatedAt  *time.Time `gorm:"column:updated_at"`     // 更新时间

	Employee *MerchantEmployeeModel `gorm:"foreignkey:UserID;references:UserID"` // 外键关联 EmployeeModel
}

func (a *UserTokenModel) TableName() string {
	return "user_tokens"
}

func GetUserTokenMutex(clientType ClientType) []ClientType {
	return mutexMap[clientType]
}

// GetClientType 获取客户端类型
func GetClientType(c *gin.Context) (ClientType, error) {
	terminal := c.GetHeader("terminal")
	switch terminal {
	//case "mobile":
	//	return ClientTypeOrdering, nil
	//case "order-pro":
	//	return ClientTypeOrdering, nil
	//case "ros-ordering":
	//	return ClientTypeOrdering, nil
	//case "web-cashier":
	//	return ClientTypeWebCashier, nil
	//case "android-cashier":
	//	return ClientTypeCashier, nil
	case "ros-manager":
		return ClientTypeManager, nil
	}
	return ClientTypeOthers, nil
}

package model

import "time"

type WechatUserModel struct {
	ID             int64      `gorm:"column:id;primary_key"`
	OpenID         string     `gorm:"column:open_id"`
	Mobile         string     `gorm:"column:mobile"`
	Points         int        `gorm:"column:points"`
	AreaID         int        `gorm:"column:area_id"`
	APIToken       string     `gorm:"column:api_token"`
	LastLogin      *time.Time `gorm:"column:last_login"`
	YinlianOpenID  string     `gorm:"column:yinlian_open_id"`
	YinlianUnionID string     `gorm:"column:yinlian_union_id"`
	AlipayOpenID   string     `gorm:"column:alipay_open_id"`
	CreatedAt      time.Time  `gorm:"column:created_at"`
	UpdatedAt      time.Time  `gorm:"column:updated_at"`
	DeletedAt      *time.Time `gorm:"column:deleted_at"`
}

func (WechatUserModel) TableName() string {
	return "wechat_users"
}

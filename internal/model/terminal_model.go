package model

import (
	"time"
)

// TerminalModel represents the terminals table in the database
type TerminalModel struct {
	ID                  uint       `gorm:"column:id;primaryKey;auto_increment"`
	TerminalType        uint8      `gorm:"column:terminal_type;not null"`                   // 终端类型（1 收银端、 2 点菜端）
	Name                string     `gorm:"column:name;size:32;not null"`                    // 终端名称
	NameUg              string     `gorm:"column:name_ug;size:32;not null"`                 // 终端名称
	NameZh              string     `gorm:"column:name_zh;size:32;not null"`                 // 终端名称
	OsType              uint8      `gorm:"column:os_type;not null"`                         // 操作系统类型(1表示Android、2表示iOS、3表示windows)
	Icon                string     `gorm:"column:icon;size:512;not null"`                   // 终端图标
	Version             string     `gorm:"column:version;size:32;not null"`                 // 终端版本
	VersionCode         uint       `gorm:"column:version_code;not null"`                    // 终端版本号
	PackageName         string     `gorm:"column:package_name;size:512;not null"`           // 终端包名
	URL                 string     `gorm:"column:url;size:512;not null"`                    // 终端下载地址
	Sha512              *string    `gorm:"column:sha512;size:255"`                          // 客户端sha512值
	Size                *int       `gorm:"column:size"`                                     // 大小
	HtmlURL             string     `gorm:"column:html_url;size:512;not null"`               // 终端下载地址
	Description         string     `gorm:"column:description;size:1024;not null"`           // 终端说明
	ForceUpdate         uint8      `gorm:"column:force_update;not null;default:0"`          // 是否强制更新
	MinSupportedVersion int        `gorm:"column:min_supported_version;not null;default:1"` // 最低支持版本号
	State               uint8      `gorm:"column:state;not null"`                           // 状态（0关闭，1开通）
	CreatedAt           *time.Time `gorm:"column:created_at"`
	UpdatedAt           *time.Time `gorm:"column:updated_at"`
}

// TableName returns the table name for the TerminalModel
func (t *TerminalModel) TableName() string {
	return "terminals"
}

// Terminal types
const (
	TerminalTypeCashier = 1 // 收银端
	TerminalTypeOrder   = 2 // 点菜端
)

// OS types
const (
	OsTypeAndroid = 1 // Android
	OsTypeIOS     = 2 // iOS
	OsTypeWindows = 3 // Windows
)

// Terminal states
const (
	TerminalStateClosed    = 0 // 关闭
	TerminalStateActivated = 1 // 开通
)

package model

import (
	"ros-api-go/pkg/util"
	"time"
)

// MerchantConfigModel 商家设置Model
type MerchantConfigModel struct {
	ID         int64      `gorm:"primaryKey;autoIncrement" json:"id"` // 主键，自增
	MerchantNo *string    `json:"merchant_no"`                        // 商家编号
	Type       string     `json:"type"`                               // 设置配置类型（ignore price）
	Value      *util.JSON `json:"value"`                              // 值
	State      *int64     `json:"state"`                              // 状态
	CreatedAt  *time.Time `json:"created_at"`                         // 创建时间
	UpdatedAt  *time.Time `json:"updated_at"`                         // 更新时间
	DeletedAt  *time.Time `json:"deleted_at"`                         // 删除时间
}

func (a *MerchantConfigModel) TableName() string {
	return "merchant_config"
}

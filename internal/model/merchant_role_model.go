package model

import (
	"ros-api-go/pkg/util"
	"time"
)

const (
	MerchantRoleStateEnabled  = 1
	MerchantRoleStateDisabled = 0
	MerchantRoleStateDeleted  = -1
)

// MerchantRoleModel 商家用户角色
type MerchantRoleModel struct {
	ID          int64                    `gorm:"column:id;size:20;primarykey;"` // Unique ID
	MerchantNo  string                   `gorm:"column:merchant_no;size:20;"`   // 商家编号
	NameUg      string                   `gorm:"column:name_ug;size:50;"`       // 名称(维语)
	NameZh      string                   `gorm:"column:name_zh;size:50;"`       // 名称(中文)
	Permissions util.TArray[string]      `gorm:"column:permissions;type:json;"`
	State       int8                     `gorm:"column:state;size:20;index"` // -1 已删除、 0 禁用、 1 启用
	CreatedAt   time.Time                `gorm:"column:created_at"`          // Create time
	UpdatedAt   time.Time                `gorm:"column:updated_at"`          // Update time
	DeletedAt   *time.Time               `gorm:"deleted_at"`                 // Delete time
	Employees   []*MerchantEmployeeModel `gorm:"many2many:user_roles;foreignKey:ID;joinForeignKey:RoleID;references:ID;joinReferences:EmployeeID"`
}

func (a *MerchantRoleModel) TableName() string {

	return "merchant_roles"
}

package model

import "time"

const (
	RefundBatchStatusInit       = 0 // 待处理
	RefundBatchStatusProcessing = 1 // 处理中
	RefundBatchStatusDone       = 2 // 已完成
	RefundBatchStatusFail       = 3 // 处理失败
)

type RefundBatchModel struct {
	ID           int64     `json:"id"`            // 主键
	MerchantNo   string    `json:"merchant_no"`   // 商户号
	BatchNo      string    `json:"batch_no"`      // 批次号
	OrderID      int64     `json:"order_id"`      // 订单号
	TotalRefunds int64     `json:"total_refunds"` // 退款总数
	TotalAmount  int64     `json:"total_amount"`  // 总订单金额
	Status       int64     `json:"status"`        // 状态
	CashierID    int64     `json:"cashier_id"`    // 收银员ID
	CreatedAt    time.Time `json:"created_at"`    // 创建时间
	UpdatedAt    time.Time `json:"updated_at"`    // 更新时间

	OrderRefundLogs []*RefundOrderLogModel    `gorm:"foreignKey:BatchID;references:ID"` // 退款菜品记录
	RefundLogs      []*MerchantRefundLogModel `gorm:"foreignKey:BatchID;references:ID"` // 具体退款记录
}

func (m *RefundBatchModel) TableName() string {
	return "refund_batch"
}

package model

import (
	"time"
)

// MethodGroupModel 做法分组
type MethodGroupModel struct {
	ID         int64     `json:"id" gorm:"size:20;primaryKey;autoIncrement"` // ID
	MerchantNo string    `json:"merchant_no" gorm:"size:20;not null"`        // 商家编号
	Name       string    `json:"name" gorm:"size:32;not null"`               // 分组名称
	Sort       int64     `json:"sort" gorm:"default:0"`                      // 排序
	CreatedAt  time.Time `gorm:"column:created_at"`                          // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at"`                          // 更新时间
}

func (m *MethodGroupModel) TableName() string {
	return "method_groups"
}

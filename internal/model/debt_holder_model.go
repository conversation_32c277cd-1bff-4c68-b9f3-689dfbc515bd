package model

import (
	"time"
)

// DebtHolder states
const (
	DebtHolderStateDisabled = 0 // 禁用
	DebtHolderStateEnabled  = 1 // 启用
)

// DebtHolderModel represents the debt_holders table in the database
type DebtHolderModel struct {
	ID          int64      `gorm:"column:id;primaryKey;auto_increment"`
	MerchantNo  string     `gorm:"column:merchant_no;size:100;not null"`                 // 商户号
	NameZh      string     `gorm:"column:name_zh;size:100"`                              // 中文姓名
	NameUg      string     `gorm:"column:name_ug;size:100"`                              // 维语姓名
	Phone       string     `gorm:"column:phone;size:15;not null"`                        // 手机号
	CreditLimit int64      `gorm:"column:credit_limit;default:0;not null"`               // 信用额度(分)
	Balance     int64      `gorm:"column:balance;default:0;not null"`                    // 当前赊账金额
	Status      uint8      `gorm:"column:status;default:1;not null"`                     // 状态：0 禁用 1 启用
	CreatedBy   int64      `gorm:"column:created_by;not null"`                           // 创建人
	CreatedAt   time.Time  `gorm:"column:created_at;default:CURRENT_TIMESTAMP;not null"` // 创建时间
	UpdatedAt   *time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP"`          // 更新时间

	Cashier *MerchantEmployeeModel `gorm:"foreignKey:CreatedBy,MerchantNo;references:UserID,MerchantNo"` // 收银员
}

// TableName returns the table name for the DebtHolderModel
func (d *DebtHolderModel) TableName() string {
	return "debt_holders"
}

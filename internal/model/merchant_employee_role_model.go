package model

import (
	"time"
)

// MerchantEmployeeRoleModel 用户角色关系表
type MerchantEmployeeRoleModel struct {
	ID         int64     `gorm:"column:id;primaryKey"` // Unique ID
	EmployeeID int64     `gorm:"column:employee_id"`
	RoleID     int64     `gorm:"column:role_id;index"` // From RoleModel.ID
	CreatedAt  time.Time `gorm:"column:created_at"`    // Create time
	UpdatedAt  time.Time `gorm:"column:updated_at"`    // Update time
	//RoleName   string    `json:"role_name" gorm:"<-:false;-:migration;"` // From RoleModel.Name
}

func (a *MerchantEmployeeRoleModel) TableName() string {
	return "merchant_employee_roles"
}

type UserRoles []*MerchantEmployeeRoleModel

// ToEmployeeIDMap 提取用户ID和角色列表的映射
func (a UserRoles) ToEmployeeIDMap() map[int64]UserRoles {
	m := make(map[int64]UserRoles)
	for _, userRole := range a {
		m[userRole.EmployeeID] = append(m[userRole.EmployeeID], userRole)
	}
	return m
}

// ToRoleIDs 提取角色ID列表
func (a UserRoles) ToRoleIDs() []int64 {
	var ids []int64
	for _, item := range a {
		ids = append(ids, item.RoleID)
	}
	return ids
}

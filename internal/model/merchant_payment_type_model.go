package model

import (
	"time"
)

// MerchantPaymentTypeModel 商家支付类型模型
type MerchantPaymentTypeModel struct {
	ID            int64      `gorm:"primaryKey;autoIncrement" json:"id"` // 主键，自增
	MerchantID    int64      `json:"merchant_id"`                        // 商家编号
	MerchantNo    string     `json:"merchant_no"`                        // 商家唯一编号
	PaymentTypeID int64      `json:"payment_type_id"`                    // 支付类型编号
	PayMethod     *int64     `json:"pay_method"`                         // 支付产品类型（微信支付1：刷卡支付、2：扫码支付）
	Sort          *int64     `json:"sort"`                               // 排序
	State         *int64     `json:"state"`                              // 状态
	CreatedAt     *time.Time `json:"created_at"`                         // 创建时间
	UpdatedAt     *time.Time `json:"updated_at"`                         // 更新时间
	DeletedAt     *time.Time `json:"deleted_at"`                         // 删除时间
}

func (a *MerchantPaymentTypeModel) TableName() string {
	return "merchant_payment_types"
}

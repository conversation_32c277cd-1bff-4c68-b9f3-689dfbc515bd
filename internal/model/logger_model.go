package model

import (
	"time"
)

// LoggerModel management
type LoggerModel struct {
	ID        string    `gorm:"size:20;primaryKey;" json:"id"`           // Unique ID
	Level     string    `gorm:"size:20;index;" json:"level"`             // Log level
	TraceID   string    `gorm:"size:64;index;" json:"trace_id"`          // Trace ID
	UserID    string    `gorm:"size:20;index;" json:"user_id"`           // UserModel ID
	Tag       string    `gorm:"size:32;index;" json:"tag"`               // Log tag
	Message   string    `gorm:"size:1024;" json:"message"`               // Log message
	Stack     string    `gorm:"type:text;" json:"stack"`                 // Error stack
	Data      string    `gorm:"type:text;" json:"data"`                  // Log data
	CreatedAt time.Time `gorm:"index;" json:"created_at"`                // Create time
	LoginName string    `json:"login_name" gorm:"<-:false;-:migration;"` // From UserModel.Username
	UserName  string    `json:"user_name" gorm:"<-:false;-:migration;"`  // From UserModel.Name
}

func (a *LoggerModel) TableName() string {
	return "logger"
}

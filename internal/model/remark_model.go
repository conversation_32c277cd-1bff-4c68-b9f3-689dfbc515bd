package model

import (
	"time"
)

// RemarkModel 商家备注
type RemarkModel struct {
	ID         int64      `json:"id" gorm:"size:20;primaryKey;"` // Unique ID
	MerchantNo string     `json:"merchant_no" gorm:"size:20;"`   // 商家编号
	Tag        string     `json:"tag" gorm:"size:20;"`           // 标签
	NameUg     string     `json:"name_ug" gorm:"size:50;"`       // 名称(维语)
	NameZh     string     `json:"name_zh" gorm:"size:50;"`       // 名称(中文)
	Sort       int64      `json:"sort"`                          // 排序
	State      int64      `json:"state" gorm:"size:10;"`         // 状态
	CreatedAt  time.Time  `gorm:"column:created_at"`             // 创建时间
	UpdatedAt  time.Time  `gorm:"column:updated_at"`             // 更新时间
	DeletedAt  *time.Time `gorm:"column:deleted_at"`             // 删除时间
}

func (a *RemarkModel) TableName() string {
	return "remarks"
}

package model

import (
	"time"
)

// MerchantInfoUpdateModel 商家信息更新记录
type MerchantInfoUpdateModel struct {
	MerchantNo           string    `json:"merchant_no" gorm:"size:20;primaryKey"`         // 商家编号
	UserUpdatedAt        time.Time `json:"user_updated_at" gorm:"column:user_updated_at"` // 用户信息更新时间
	AreaUpdatedAt        time.Time `json:"area_updated_at"`                               // 餐厅区域更新时间
	TableUpdatedAt       time.Time `json:"table_updated_at"`                              // 餐桌信息更新时间
	FoodUpdatedAt        time.Time `json:"food_updated_at"`                               // 菜品信息更新时间
	RemarkUpdatedAt      time.Time `json:"remark_updated_at"`                             // 备注信息更新时间
	PaymentTypeUpdatedAt time.Time `json:"payment_type_updated_at"`                       // 支付方式更新时间
	PrinterUpdatedAt     time.Time `json:"printer_updated_at"`                            // 打印机信息更新时间
	FoodPrinterUpdatedAt time.Time `json:"food_printer_updated_at"`                       // 打印机关联菜品信息更新时间
	PermissionUpdatedAt  time.Time `json:"permission_updated_at"`                         // 权限信息更新时间
	CreatedAt            time.Time `json:"created_at"`                                    // Create time
	UpdatedAt            time.Time `json:"updated_at"`                                    // Update time
}

func (a *MerchantInfoUpdateModel) TableName() string {
	return "merchant_info_updates"
}

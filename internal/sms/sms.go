package sms

import (
	"context"
	"fmt"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/sms"
	"ros-api-go/pkg/sms/providers/aliyun"
	"ros-api-go/pkg/sms/providers/zhutong"
	"ros-api-go/pkg/util"
	"time"
)

var SmsManager *sms.Manager

func InitSMSManager(redisClient *redis.Client) error {
	err := InitSMSProviders()
	if err != nil {
		return err
	}
	SmsManager, err = sms.NewManager(config.C.SMS.DefaultProvider, redisClient)
	if err != nil {
		return err
	}
	return nil
}

// InitSMSProviders 初始化短信服务提供商
func InitSMSProviders() error {
	// 注册阿里云短信服务提供商
	aliyunProvider := aliyun.NewProvider()
	if err := aliyunProvider.Initialize(config.C.SMS.Providers.Aliyun); err != nil {
		return err
	}
	sms.GetFactory().Register(sms.ProviderAliyun, aliyunProvider)

	// 注册助通短信服务提供商
	zhutongProvider := zhutong.NewProvider()
	if err := zhutongProvider.Initialize(config.C.SMS.Providers.ZhuTong); err != nil {
		return err
	}
	sms.GetFactory().Register(sms.ProviderZhuTong, zhutongProvider)

	return nil
}

// SendUserPasswordSMS 发送用户密码短信。
// 使用指定的短信模板发送包含用户密码的短信。
func SendUserPasswordSMS(mobile, password string) error {
	request := &sms.SendRequest{
		PhoneNumbers: []string{mobile},
		TemplateID:   SmsManager.Templates.UserPasswordSMS,
		TemplateParam: map[string]string{
			"password": password,
		},
	}
	return SmsManager.SendSMS(request)
}

// SendForgotPasswordSMS 发送忘记密码验证码短信
func SendForgotPasswordSMS(ctx context.Context, phone string) (string, error) {
	err := CheckRateLimit(ctx, phone)
	if err != nil {
		return "", err
	}

	code := util.RandomNumber(4)
	batchID := util.RandomNumber(10)

	// 保存验证码
	SmsManager.RedisClient.Set(ctx,
		fmt.Sprintf(consts.CacheKeyForForgetPwdSMSCode, phone, batchID),
		code,
		10*time.Minute,
	)

	// 发送短信
	request := &sms.SendRequest{
		PhoneNumbers: []string{phone},
		TemplateID:   SmsManager.Templates.ForgotPasswordSMS,
		TemplateParam: map[string]string{
			"code": code,
		},
	}

	if err := SmsManager.SendSMS(request); err != nil {
		logging.Context(ctx).Error("Failed to send SMS",
			zap.String("phone", phone),
			zap.String("batchID", batchID),
			zap.String("code", code),
			zap.Error(err),
		)
		return "", errors.BadRequest("", "FailedToSendSMS")
	}

	return batchID, nil
}

// SendOperationPasswordSMS 发送操作密码修改短信验证码
func SendOperationPasswordSMS(ctx context.Context, phone string) (string, error) {
	err := CheckRateLimit(ctx, phone)
	if err != nil {
		return "", err
	}

	code := util.RandomNumber(4)
	batchID := util.RandomNumber(10)

	SmsManager.RedisClient.Set(ctx,
		fmt.Sprintf(consts.CacheKeyForOperationPwdSMSCode, phone, batchID),
		code,
		10*time.Minute,
	)

	request := &sms.SendRequest{
		PhoneNumbers: []string{phone},
		TemplateID:   SmsManager.Templates.OperationPasswordSMS,
		TemplateParam: map[string]string{
			"code": code,
		},
	}

	if err := SmsManager.SendSMS(request); err != nil {
		logging.Context(ctx).Error("Failed to send SMS",
			zap.String("phone", phone),
			zap.String("batchID", batchID),
			zap.String("code", code),
			zap.Error(err),
		)
		return "", errors.BadRequest("", "FailedToSendSMS")
	}

	return batchID, nil
}

// VerifySmsCode 验证短信验证码。
// 检查用户提供的验证码是否与存储在 Redis 中的验证码匹配。
// 验证成功后会删除 Redis 中的验证码。
//
// 参数:
//   - ctx: 上下文
//   - phone: 手机号
//   - batchID: 发送短信时返回的批次ID
//   - code: 用户提供的验证码
//
// 返回:
//   - bool: 验证是否成功
//   - error: 验证过程中发生错误时返回
func VerifySmsCode(ctx context.Context, phone string, batchID string, key, code string) (bool, error) {
	savedCode, err := SmsManager.RedisClient.Get(ctx, fmt.Sprintf(key, phone, batchID)).Result()
	if err != nil {
		return false, err
	}
	if savedCode == code {
		SmsManager.RedisClient.Del(ctx, fmt.Sprintf(key, phone, batchID))
		return true, nil
	}
	return false, nil
}

// SendCustomerRechargeSMS 发送会员充值短信
func SendCustomerRechargeSMS(mobile, restaurantName, price, leftPrice string) error {
	request := &sms.SendRequest{
		PhoneNumbers: []string{mobile},
		TemplateID:   SmsManager.Templates.CustomerRechargeSMS,
		TemplateParam: map[string]string{
			"restaurant_name": restaurantName,
			"price":           price,
			"left_price":      leftPrice,
		},
	}

	return SmsManager.SendSMS(request)
}

// SendCustomerPaySMS 发送会员支付短信
func SendCustomerPaySMS(mobile, restaurantName, price, leftPrice string) error {
	request := &sms.SendRequest{
		PhoneNumbers: []string{mobile},
		TemplateID:   SmsManager.Templates.CustomerPaySMS,
		TemplateParam: map[string]string{
			"restaurant_name": restaurantName,
			"price":           price,
			"left_price":      leftPrice,
		},
	}

	return SmsManager.SendSMS(request)
}

// CheckRateLimit 检查短信发送频率限制
func CheckRateLimit(ctx context.Context, phone string) error {
	// 检查发送频率
	smsCountKey := fmt.Sprintf(consts.CacheKeyForSMSLimitCount, phone)
	count, err := SmsManager.RedisClient.Get(ctx, smsCountKey).Int()
	if err != nil && !errors.Is(err, redis.Nil) {
		logging.Context(ctx).Error("Failed to get SMS count",
			zap.String("phone", phone),
			zap.Error(err),
		)
		return errors.BadRequest("", "FailedToSendSMS")
	}

	if count >= 5 {
		return errors.BadRequest("", "SMSRateLimitExceeded")
	}

	// 增加计数并设置过期时间
	pipe := SmsManager.RedisClient.Pipeline()
	pipe.Incr(ctx, smsCountKey)
	pipe.Expire(ctx, smsCountKey, 1*time.Hour)
	_, err = pipe.Exec(ctx)
	if err != nil {
		logging.Context(ctx).Error("Failed to increment SMS count",
			zap.String("phone", phone),
			zap.Error(err),
		)
		return errors.BadRequest("", "FailedToSendSMS")
	}
	return nil
}

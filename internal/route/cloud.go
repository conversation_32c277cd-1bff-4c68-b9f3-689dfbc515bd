package route

import (
	"context"
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/controller/v2/foods"
	"ros-api-go/internal/http/controller/v2/order"
	"ros-api-go/internal/http/middleware"
	"ros-api-go/pkg/util"
)

type CloudRouteGroup struct {
	OrderController           *order.OrderController
	OrderSyncHandler          *handler.OrderSyncHandler
	OrderPaymentController    *order.OrderPaymentController
	PaymentReverseController  *order.PaymentReverseController
	FoodCategoryController    *foods.FoodCategoryController
	FoodController            *foods.FoodController
	CheckUserMerchantProvider *middleware.CheckUserMerchantProvider
}

func (o *CloudRouteGroup) RegisterRouters(ctx context.Context, cloudRouter *gin.RouterGroup) {

	cloudV2 := cloudRouter.Group("v2").
		Use(middleware.AuthMiddleware()).
		Use(o.CheckUserMerchantProvider.Check())

	cloudV2.GET("tables", middleware.CasbinMiddleware(), o.OrderController.TableList)
	cloudV2.POST("orders", middleware.CasbinMiddleware(), o.OrderController.CreateOrder)
	cloudV2.GET("table-details/:id", middleware.CasbinMiddleware(), o.OrderController.TableDetails)
	cloudV2.GET("order/:order_id", middleware.CasbinMiddleware(), o.OrderController.OrderDetails)
	cloudV2.GET("orders/payment-list/:id", middleware.CasbinMiddleware(), o.OrderController.PaymentList)
	cloudV2.POST("orders/add-foods/:id", middleware.CasbinMiddleware(), o.OrderController.AddFoods)
	cloudV2.POST("orders/cancel-food/:order_id/:od_id", middleware.CasbinMiddleware(), o.OrderController.CancelFood)
	cloudV2.POST("orders/undo-cancel-food/:order_id/:od_id", middleware.CasbinMiddleware(), o.OrderController.UndoCancelFood)
	cloudV2.GET("orders/urge/:table_id/:order_id/:order_detail_id", middleware.CasbinMiddleware(), o.OrderController.Urge)
	cloudV2.GET("orders/urge/all/:table_id/:order_id", middleware.CasbinMiddleware(), o.OrderController.UrgeAll)
	cloudV2.POST("orders/split/:order_id", middleware.CasbinMiddleware(), o.OrderController.SplitOrder)
	cloudV2.POST("orders/collage/:order_id", middleware.CasbinMiddleware(), o.OrderController.CollageOrder)
	cloudV2.POST("orders/changeTable/:order_id", middleware.CasbinMiddleware(), o.OrderController.ChangeTable)
	cloudV2.POST("orders/cancel-all-foods/:order_id", middleware.CasbinMiddleware(), o.OrderController.CancelAllFoods)

	cloudV2.POST("orders/ignore-price", middleware.CasbinMiddleware(), o.OrderController.IgnorePrice)
	cloudV2.POST("orders/checkout/:id", middleware.CasbinMiddleware(), o.OrderController.Checkout)
	cloudV2.POST("orders/sync/:id", func(c *gin.Context) {
		o.OrderSyncHandler.SyncOrder(c.Request.Context(), util.StrToInt64(c.Param("id")), c.Request.Header.Get("Merchantno"))
	})

	cloudV2.POST("payment/offline", middleware.CasbinMiddleware(), o.OrderPaymentController.OfflinePay)
	cloudV2.POST("payment/micro-pay", middleware.CasbinMiddleware(), o.OrderPaymentController.MicroPay)
	cloudV2.POST("payment/customer-pay", middleware.CasbinMiddleware(), o.OrderPaymentController.CustomerPay)
	cloudV2.POST("payment/debt-pay", middleware.CasbinMiddleware(), o.OrderPaymentController.DebtPay)
	cloudV2.POST("payment/qrcode", middleware.CasbinMiddleware(), o.OrderPaymentController.PaymentQrcodeUrl)
	cloudV2.GET("payment/query/:paymentNo", middleware.CasbinMiddleware(), o.OrderPaymentController.PaymentQuery)
	cloudV2.POST("payment/reverse", middleware.CasbinMiddleware(), o.PaymentReverseController.PaymentReverse)

	cloudV2.GET("foodCategories", o.FoodCategoryController.GetAvailableFoodCategories)
	cloudV2.GET("foods", o.FoodController.GetMerchantAvailableFoods)
}

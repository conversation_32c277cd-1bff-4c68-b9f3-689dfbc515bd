package route

import (
	"context"
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/controller/v2/local"
	"ros-api-go/internal/http/middleware"
)

// 本地服务路由

type LocalRouterGroup struct {
	PrinterController   *local.PrinterController
	BroadcastController *local.BroadcastController
}

func (router *LocalRouterGroup) Register(ctx context.Context, group *gin.RouterGroup) {
	localAPI := group.Group("/local")
	{
		localAPI.Group("").Use(middleware.AuthMiddleware()).POST("/printer/print", router.PrinterController.Print)
		localAPI.Group("").Use(middleware.ServerAuthMiddleware()).POST("/broadcast", router.BroadcastController.PostBroadcast)
	}
}

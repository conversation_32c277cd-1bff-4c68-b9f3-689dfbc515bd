package route

import (
	"context"
	merchant "ros-api-go/internal/http/controller/v2/merchant"
	"ros-api-go/internal/http/middleware"

	"github.com/gin-gonic/gin"
)

// MerchantRouterGroup 商户路由组
type MerchantRouterGroup struct {
	MerchantRoleController    *merchant.RoleController
	CheckUserMerchantProvider *middleware.CheckUserMerchantProvider
	EmployeeController        *merchant.EmployeeController
}

// Register 商户路由组注册
func (m *MerchantRouterGroup) Register(ctx context.Context, group *gin.RouterGroup) {
	merchantRouter := group.Group("/merchant").
		Use(middleware.AuthMiddleware()).
		Use(m.CheckUserMerchantProvider.Check())
	{
		// 角色管理
		merchantRouter.POST("/roles", middleware.CasbinMiddleware(), m.MerchantRoleController.Create)
		merchantRouter.PUT("/roles/:id", middleware.CasbinMiddleware(), m.MerchantRoleController.Update)
		merchantRouter.PUT("/roles/:id/status", middleware.CasbinMiddleware(), m.MerchantRoleController.UpdateState)
		merchantRouter.DELETE("/roles/:id", middleware.CasbinMiddleware(), m.MerchantRoleController.Delete)
		merchantRouter.GET("/roles/:id", middleware.CasbinMiddleware(), m.MerchantRoleController.Get)
		merchantRouter.GET("/roles", m.MerchantRoleController.List)
		// 员工管理
		merchantRouter.GET("/employees", m.EmployeeController.List)
		merchantRouter.POST("/employees", middleware.CasbinMiddleware(), m.EmployeeController.Create)
		merchantRouter.PUT("/employees/:id", middleware.CasbinMiddleware(), m.EmployeeController.Update)
		merchantRouter.PUT("/employees/:id/status", middleware.CasbinMiddleware(), m.EmployeeController.UpdateState)
		merchantRouter.DELETE("/employees/:id", middleware.CasbinMiddleware(), m.EmployeeController.Delete)
		merchantRouter.GET("/employees/:id", middleware.CasbinMiddleware(), m.EmployeeController.Get)
	}
}

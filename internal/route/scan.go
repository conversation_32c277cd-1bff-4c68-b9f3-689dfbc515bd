package route

import (
	"context"
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/controller/v2/scan"
	"ros-api-go/internal/http/middleware"
)

// 扫码点菜路由

type ScanRouterGroup struct {
	WechatAuthController *scan.WechatAuthController
	ScanOrderController  *scan.ScanOrderController
	ServiceController    *scan.ServiceController
}

func (router *ScanRouterGroup) Register(ctx context.Context, group *gin.RouterGroup) {
	scanAPI := group.Group("/scan")
	{
		scanAPI.GET("open_id", router.WechatAuthController.GetOpenId)
		scanAPI.POST("wechat_login", router.WechatAuthController.WechatLogin)

		order := scanAPI.Group("/order")
		{
			order.POST("", middleware.WechatUserMiddleware(), router.ScanOrderController.Create)
			order.GET("list", middleware.WechatUserMiddleware(), router.ScanOrderController.GetOrderList)
			order.GET("detail/:id", middleware.WechatUserMiddleware(), router.ScanOrderController.GetOrderDetails)
		}

		service := scanAPI.Group("/service")
		{
			service.POST("commit", middleware.WechatUserMiddleware(), router.ServiceController.ServiceCommit)
		}
	}
}

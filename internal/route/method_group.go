package route

import (
	"context"

	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/controller/v2"
	"ros-api-go/internal/http/middleware"
)

type MethodGroupRouterGroup struct {
	MethodGroupController *v2.MethodGroupController
}

func (r *MethodGroupRouterGroup) Register(ctx context.Context, router *gin.RouterGroup) {
	methodGroupsApi := router.Group("method-groups").Use(middleware.AuthMiddleware())
	{
		// 获取做法分组列表
		methodGroupsApi.GET("", r.MethodGroupController.GetMethodGroupList)
		// 创建做法分组
		methodGroupsApi.POST("", middleware.CasbinMiddleware(), r.MethodGroupController.CreateMethodGroup)
		// 更新做法分组
		methodGroupsApi.PUT("/:id", middleware.CasbinMiddleware(), r.MethodGroupController.UpdateMethodGroup)
		// 删除做法分组
		methodGroupsApi.DELETE("/:id", middleware.CasbinMiddleware(), r.MethodGroupController.DeleteMethodGroup)
		// 保存做法分组排序
		methodGroupsApi.POST("/sort", middleware.CasbinMiddleware(), r.MethodGroupController.SaveMethodGroupSort)
	}
}

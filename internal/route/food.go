package route

import (
	"context"
	"ros-api-go/internal/http/controller/v2/foods"
	"ros-api-go/internal/http/middleware"

	"github.com/gin-gonic/gin"
)

type FoodRouterGroup struct {
	FoodController         *foods.FoodController
	FoodCategoryController *foods.FoodCategoryController
	FoodSpecController     *foods.FoodSpecController
}

func (r *FoodRouterGroup) Register(ctx context.Context, router *gin.RouterGroup) {
	foodsApi := router.Group("foodsApi").Use(middleware.AuthMiddleware())
	{
		// 获取美食列表（分页）
		foodsApi.GET("", r.FoodController.GetFoodsList)
		// 创建美食
		foodsApi.POST("", middleware.CasbinMiddleware(), r.FoodController.CreateFood)
		// 更新美食
		foodsApi.PUT("/:id", middleware.CasbinMiddleware(), r.FoodController.UpdateFood)
		// 获取双屏显示食品列表
		foodsApi.GET("/dual", r.FoodController.FoodListForDualScreen)
		// 获取默认的美食图片
		foodsApi.GET("/default-images", r.FoodController.GetDefaultFoodImages)
		// 保存美食排序
		foodsApi.POST("/sort", middleware.CasbinMiddleware(), r.FoodController.SaveFoodSort)
		// 修改美食状态
		foodsApi.PUT("/:id/state", r.FoodController.ChangeState)
		// 修改美食是否支持扫码点单
		foodsApi.PUT("/:id/support-scan-order", r.FoodController.ChangeSupportScanOrder)
		// 删除美食
		foodsApi.DELETE("/:id", r.FoodController.Destroy)
	}

	foodCategoryApi := router.Group("foodCategories").Use(middleware.AuthMiddleware())
	{
		// 获取菜品分类列表（分页）
		foodCategoryApi.GET("", r.FoodCategoryController.GetFoodCategoryList)
		// 获取单个菜品分类
		foodCategoryApi.GET("/:id", r.FoodCategoryController.GetFoodCategory)
		// 创建菜品分类
		foodCategoryApi.POST("", r.FoodCategoryController.CreateFoodCategory)
		// 更新菜品分类
		foodCategoryApi.PUT("/:id", r.FoodCategoryController.UpdateFoodCategory)
		// 删除菜品分类
		foodCategoryApi.DELETE("/:id", r.FoodCategoryController.DeleteFoodCategory)
		// 保存美食分类排序
		foodCategoryApi.POST("/sort", middleware.CasbinMiddleware(), r.FoodCategoryController.SaveFoodCategorySort)
		// 更新菜品分类状态
		foodCategoryApi.PUT("/:id/state", r.FoodCategoryController.UpdateFoodCategoryState)
	}

	// 规格管理路由
	foodSpecApi := router.Group("foodSpecs").Use(middleware.AuthMiddleware())
	{
		// 获取规格列表
		foodSpecApi.GET("", r.FoodSpecController.GetFoodSpecList)
		// 获取单个规格
		foodSpecApi.GET("/:id", r.FoodSpecController.GetFoodSpec)
		// 获取规格关联的美食列表
		foodSpecApi.GET("/:id/foods", r.FoodSpecController.GetFoodsBySpecID)
		// 保存规格排序
		foodSpecApi.POST("/sort", middleware.CasbinMiddleware(), r.FoodSpecController.SaveFoodSpecSort)
		// 保存规格美食关联关系
		foodSpecApi.POST("/foods", middleware.CasbinMiddleware(), r.FoodSpecController.SaveFoodSpecFoods)
		// 解除规格美食关联关系
		foodSpecApi.DELETE("/foods", middleware.CasbinMiddleware(), r.FoodSpecController.RemoveFoodSpecFoods)
		// 创建规格
		foodSpecApi.POST("", middleware.CasbinMiddleware(), r.FoodSpecController.CreateFoodSpec)
		// 更新规格
		foodSpecApi.PUT("/:id", middleware.CasbinMiddleware(), r.FoodSpecController.UpdateFoodSpec)
		// 删除规格
		foodSpecApi.DELETE("/:id", middleware.CasbinMiddleware(), r.FoodSpecController.DeleteFoodSpec)
	}
}

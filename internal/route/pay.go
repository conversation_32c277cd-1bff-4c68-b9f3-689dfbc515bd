package route

import (
	"context"
	"github.com/gin-gonic/gin"
	pay "ros-api-go/internal/http/controller/v2/pay"
	"ros-api-go/internal/http/middleware"
)

type PayRouterGroup struct {
	PaymentController         *pay.PaymentController
	PaymentReverseController  *pay.PaymentReverseController
	WechatPayController       *pay.WechatPayController
	AliPayController          *pay.AliPayController
	CustomerPayController     *pay.CustomerPayController
	RechargeController        *pay.RechargeController
	DebtPayController         *pay.DebtPayController
	DebtRepaymentController   *pay.DebtRepaymentController
	PaymentTypeController     *pay.PaymentTypeController
	CheckUserMerchantProvider *middleware.CheckUserMerchantProvider
}

func (router *PayRouterGroup) Register(ctx context.Context, group *gin.RouterGroup) {

	group.GET("/payment-types-for-recharge", router.PaymentTypeController.GetPaymentTypesForRecharge) // 获取支付类型
	// 微信支付相关接口
	payRouter := group.Group("/payment")
	{
		payRouter.POST("/wechat/payment-notify/:paymentNo", router.WechatPayController.HandlePaymentNotify)              // 微信支付结果通知
		payRouter.POST("/wechat/refund-notify/:paymentNo", router.WechatPayController.HandleRefundNotify)                // 微信退款结果通知
		payRouter.POST("/wechat/recharge-notify/:paymentNo", router.WechatPayController.HandleRechargeNotify)            // 微信会员充值结果通知
		payRouter.POST("/wechat/debt-repayment-notify/:paymentNo", router.WechatPayController.HandleDebtRepaymentNotify) // 微信赊账还款结果通知

		payRouter.POST("/alipay/payment-notify/:paymentNo", router.AliPayController.HandlePaymentNotify)              // 支付宝支付结果通知
		payRouter.POST("/alipay/recharge-notify/:paymentNo", router.AliPayController.HandleRechargeNotify)            // 支付宝会员充值结果通知
		payRouter.POST("/alipay/debt-repayment-notify/:paymentNo", router.AliPayController.HandleDebtRepaymentNotify) // 支付宝赊账还款结果通知

		payRouter.POST("/wechat/jsapi/:merchantNo/:paymentNo", router.WechatPayController.JsAPIPay)
		payRouter.POST("/alipay/jsapi/:merchantNo/:paymentNo", router.AliPayController.JsAPIPay)
	}
	// 支付相关接口
	payment := group.Group("/payment").Use(middleware.ServerAuthMiddleware())
	{
		payment.POST("qrcode", router.PaymentController.PaymentQrcodeUrl)       // 获取支付二维码
		payment.POST("micro-pay", router.PaymentController.MicroPay)            // 付款码支付
		payment.GET("query/:paymentNo", router.PaymentController.PaymentQuery)  // 支付查询
		payment.POST("reverse", router.PaymentReverseController.PaymentReverse) // 撤销支付
		payment.POST("customer-pay", router.CustomerPayController.Pay)          // 会员支付
		payment.POST("debt-pay", router.DebtPayController.Pay)                  // 赊账支付
	}
	// 会员充值
	recharge := group.Group("/recharge", middleware.AuthMiddleware(), router.CheckUserMerchantProvider.Check())
	{
		recharge.POST("offline-pay", router.RechargeController.OfflinePay)       // 线下支付
		recharge.POST("native-pay", router.RechargeController.NativePay)         // 获取支付二维码
		recharge.POST("micro-pay", router.RechargeController.MicroPay)           // 付款码支付
		recharge.GET("query/:paymentNo", router.RechargeController.PaymentQuery) // 支付查询
	}
	// 赊账还款
	repayment := group.Group("/debt/repayment", middleware.AuthMiddleware(), router.CheckUserMerchantProvider.Check())
	{
		repayment.POST("offline-pay", router.DebtRepaymentController.OfflinePay)       // 线下支付
		repayment.POST("native-pay", router.DebtRepaymentController.NativePay)         // 获取支付二维码
		repayment.POST("micro-pay", router.DebtRepaymentController.MicroPay)           // 付款码支付
		repayment.GET("query/:paymentNo", router.DebtRepaymentController.PaymentQuery) // 支付查询
	}
}

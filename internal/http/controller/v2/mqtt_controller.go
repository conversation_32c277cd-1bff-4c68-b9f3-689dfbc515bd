package v2

import (
	"github.com/gin-gonic/gin"
	"net"
	"net/url"
	"ros-api-go/internal/config"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

type MqttController struct {
	MqttAclService     *service.MqttAclService
	LocalServerService *service.LocalServerService
}

func (ctrl *MqttController) Config(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取商户号
	merchantNo := util.FromServerMerchantNo(ctx)

	// 获取本地服务
	localServer, err := ctrl.LocalServerService.GetActiveServerByMerchantNo(ctx, merchantNo)

	if err != nil {
		util.ResError(c, errors.BadRequest("", "FailedToGetActiveServer"))
		return
	} else if localServer == nil { // 本地服务未注册
		util.ResError(c, errors.BadRequest("", "LocalServerNotRegistered"))
		return
	}

	// 注册MQTT认证信息
	// 发布权限
	ok, err := ctrl.MqttAclService.CreateClientPublishPermission(ctx, merchantNo, localServer.ID)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "FailedToCreateClientPublishPermission"))
		return
	} else if !ok {
		util.ResError(c, errors.BadRequest("", "FailedToCreateClientPublishPermission"))
		return
	}
	// 订阅权限
	ok, err = ctrl.MqttAclService.CreateClientSubscribePermission(ctx, merchantNo, localServer.ID)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "FailedToCreateClientPublishPermission"))
		return
	} else if !ok {
		util.ResError(c, errors.BadRequest("", "FailedToCreateClientPublishPermission"))
		return
	}
	// 获取MQTT认证信息
	tokenString, exp, err := ctrl.MqttAclService.GetClientMqttAuthInfo(ctx, localServer.ID)

	if err != nil {
		util.ResError(c, errors.BadRequest("", "FailedToGetCloudAuthInfo"))
		return
	}

	// 获取MQTT地址
	mqttUrl, err := url.Parse(config.C.MQTT.Host)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidMQTTHost"))
		return
	}

	mqttHost, _, err := net.SplitHostPort(mqttUrl.Host)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidMQTTHost"))
		return
	}

	// 返回MQTT配置信息
	mqttServer := resource.MQTTServerResource{
		ClientID: localServer.ID,
		Host:     mqttHost,
		Token:    tokenString,
		Expires:  exp,
	}
	util.ResSuccess(c, mqttServer, "GetSuccess")
}

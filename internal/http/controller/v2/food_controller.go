package v2

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/resource/order_resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"
)

type FoodController struct {
	FoodsService service.FoodsService
}

func (fc *FoodController) GetFoods(c *gin.Context) {
	ctx := c.Request.Context()
	MerchantNo := c.Request.Header.Get("MerchantNo")

	// 从查询参数中获取食物类别ID
	catId := util.StrToInt64(c.Query("food_category_id"))
	// 从查询参数中获取搜索关键词
	keyword := c.Query("keyword")
	// 从查询参数中获取是否清理所有销售标志
	sellClearAll := c.Query("sell_clear_all")

	result, err := fc.FoodsService.GetMerchantAvailableFoods(ctx, MerchantNo, catId, keyword, sellClearAll)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&order_resource.FoodResource{}).Collection(&ctx, result))
}

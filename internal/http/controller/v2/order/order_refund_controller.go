package order

import (
	"context"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request/order_request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/collect"
	"ros-api-go/pkg/crypto/hash"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"

	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
)

// OrderRefundController 处理订单详情相关业务
type OrderRefundController struct {
	Trans                         *util.Trans
	MerchantService               *service.MerchantService
	OrderService                  *service.OrderService
	UserService                   *service.UserService
	OrderDetailService            *service.OrderDetailService
	RefundOrderLogService         *service.RefundOrderLogService
	ChangeOrderLogService         *service.ChangeOrderLogService
	PaymentService                *service.PaymentService
	PaymentLogService             *service.PaymentLogService
	RefundBatchService            *service.RefundBatchService
	RefundLogService              *service.RefundLogService
	CustomerService               *service.CustomerService
	CustomerConsumptionLogService *service.CustomerConsumptionLogService
	RefundHandler                 *handler.RefundHandler
}

// OrderRefund 订单退款 (已结账订单)
//
// @Tags 支付相关接口
// @Security ApiTokenAuth
// @Summary
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/order/refund/{orderId} [get]
// @Param orderId path int64 true "订单ID"
// @X-Author ["Merdan"]
// @X-Date ["2025/1/5 16:25"]
// @X-Version ["2.0"]
func (ctrl *OrderRefundController) OrderRefund(c *gin.Context) {

	ctx := c.Request.Context()
	var formItem order_request.RefundOrderForm
	err := util.ParseJSON(c, &formItem)
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 获取订单ID
	formItem.OrderID = util.StrToInt64(c.Param("orderId"))
	// 获取MerchantNO
	formItem.MerchantNo = c.Request.Header.Get("Merchantno")
	// 获取用户ID
	formItem.CashierID = util.FromUserID(ctx)

	user, err := ctrl.UserService.GetByUserIDWithEmployeeData(ctx, formItem.CashierID, formItem.MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 是否设置操作密码
	if user.OperationPassword == "" {
		util.ResError(c, errors.BadRequest("", "OperationPasswordNotSet"))
		return
	}
	// 验证操作密码
	if err := hash.CompareHashAndPassword(user.OperationPassword, formItem.Password); err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidOperationPassword"))
		return
	}

	// 获取商户信息
	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, formItem.MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	order, err := ctrl.OrderService.GetByID(ctx, formItem.OrderID, merchant.No)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if order == nil {
		util.ResError(c, errors.BadRequest("", "InvalidOrderID"))
		return
	}

	if order.State != consts.ORDER_STATE_PAID {
		util.ResError(c, errors.BadRequest("", "InvalidOrderState"))
		return
	}

	// 获取订单详情
	details := formItem.OrderDetails
	detailIds := make([]int64, 0)
	for _, detail := range details {
		detailIds = append(detailIds, detail.ID)
	}
	orderDetails, err := ctrl.OrderDetailService.GetByIDs(ctx, formItem.OrderID, detailIds)

	if err != nil {
		util.ResError(c, err)
		return
	}

	if len(orderDetails) == 0 {
		util.ResError(c, errors.BadRequest("", "InvalidOrderDetailID"))
		return
	}

	detailCollect := collect.ToMap(orderDetails, func(item *model.OrderDetailModel) int64 {
		return item.ID
	})

	refundAmount := 0.0                                                    // 总退款金额
	refundOrderLogs := make([]*model.RefundOrderLogModel, 0, len(details)) // 退菜日志
	ChangeOrderLogs := make([]*model.ChangeOrderLogModel, 0, len(details)) // 操作日志

	for index, detail := range details {
		// 数据验证
		orderDetail := detailCollect[detail.ID]
		if orderDetail == nil {
			util.ResError(c, errors.BadRequest("", "InvalidOrderDetailID"))
			return
		}

		if err = detail.Validate(ctx, orderDetail); err != nil {
			util.ResError(c, err)
			return
		}
		// 填充数据
		details[index].Price = orderDetail.Price
		details[index].VipPrice = orderDetail.VipPrice
		details[index].OriginalPrice = orderDetail.OriginalPrice
		details[index].TotalPrice = orderDetail.TotalPrice

		detailRefundAmount := ctrl.calculateRefundAmount(orderDetail, &detail)
		refundAmount = util.AddFloat(refundAmount, detailRefundAmount)
		refundOrderLogs = append(
			refundOrderLogs,
			&model.RefundOrderLogModel{
				MerchantNo:    orderDetail.MerchantNo,
				OrderID:       orderDetail.OrderID,
				OrderDetailID: detail.ID,
				FoodID:        orderDetail.FoodID,
				RefundCount:   detail.FoodsCount,
				RefundAmount:  detailRefundAmount,
				Remarks:       detail.Remarks,
				CashierID:     formItem.CashierID,
				State:         1,
			},
		)
		ChangeOrderLogs = append(
			ChangeOrderLogs,
			&model.ChangeOrderLogModel{
				MerchantNo:    orderDetail.MerchantNo,
				OrderID:       orderDetail.OrderID,
				OrderDetailID: detail.ID,
				FoodID:        orderDetail.FoodID,
				RealPrice:     orderDetail.Price,
				Count:         detail.FoodsCount,
				Remarks:       detail.Remarks,
				TotalPrice:    detailRefundAmount,
				Type:          consts.LOG_TYPE_REFUND_FOOD,
				Discount:      100, // 退菜无折扣
				UserID:        formItem.CashierID,
				State:         consts.LOG_STATE_NORMAL,
			},
		)
	}

	// 退款金额不能大于订单金额-已退款金额
	if refundAmount > util.SubtractFloat(order.Price, order.RefundPrice) {
		util.ResError(c, errors.BadRequest("1000", "RefundAmountExceed"))
		return
	}

	var refundLogs []*model.MerchantRefundLogModel

	err = ctrl.Trans.Exec(ctx, func(ctx context.Context) error {

		// 退款批次
		batch, err := ctrl.RefundBatchService.CreateRefundBatch(ctx, order.ID, order.MerchantNo, refundAmount, formItem.CashierID)
		if err != nil {
			return err
		}

		// 变更日志
		if err = ctrl.ChangeOrderLogService.Create(ctx, ChangeOrderLogs); err != nil {
			return err
		}

		// 退款日志
		for _, log := range refundOrderLogs {
			log.BatchID = batch.ID
		}
		if err = ctrl.RefundOrderLogService.Create(ctx, refundOrderLogs); err != nil {
			return err
		}

		isVip := order.CustomerID > 0
		refundIgnorePrice := 0.00

		// 更新订单详情
		for _, detail := range details {
			if isVip {
				refundIgnorePrice = util.AddFloat(refundIgnorePrice, util.MultiplyFloat(util.SubtractFloat(detail.VipPrice, detail.Price), detail.FoodsCount))
			} else {
				refundIgnorePrice = util.AddFloat(refundIgnorePrice, util.MultiplyFloat(util.SubtractFloat(detail.OriginalPrice, detail.Price), detail.FoodsCount))
			}
			refundPrice := util.MultiplyFloat(detail.Price, detail.FoodsCount)
			err = ctrl.OrderDetailService.UpdateForRefund(ctx, detail.ID, detail.FoodsCount, refundPrice)
			if err != nil {
				return err
			}
		}

		// 更新order表
		if err = ctrl.OrderService.UpdateForRefund(ctx, order, order.MerchantNo, refundIgnorePrice); err != nil {
			return err
		}

		//退款逻辑
		refundLogs, err = ctrl.handleOrderRefund(ctx, order.ID, merchant, batch.ID, refundAmount, formItem.Payments)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		logging.Context(ctx).Error("=========退款失败=========",
			zap.String("merchant_no", formItem.MerchantNo),
			zap.Int64("order_no", formItem.OrderID),
			zap.Any("form_item", formItem),
			zap.Any("error", err))
		util.ResError(c, err)
		return
	}

	util.ResOK(c, "OrderRefundSuccess")

	for _, refundLog := range refundLogs {
		go ctrl.RefundHandler.HandleRefundByRefundLog(ctx, refundLog, formItem.CashierID)
	}

}

func (ctrl *OrderRefundController) calculateRefundAmount(orderDetail *model.OrderDetailModel, refundDetail *order_request.RefundOrderDetailForm) float64 {

	if orderDetail.FoodsCount == refundDetail.FoodsCount {
		return orderDetail.TotalPrice
	}

	return util.RoundUp(util.MultiplyFloat(util.DivideFloat(orderDetail.TotalPrice, orderDetail.FoodsCount), refundDetail.FoodsCount))
}

// handleOrderRefund 处理订单退款
func (ctrl *OrderRefundController) handleOrderRefund(ctx context.Context, orderID int64, merchant *model.MerchantModel, batchID int64, refundAmount float64, paymentData map[int64]float64) ([]*model.MerchantRefundLogModel, error) {

	var paymentIds []int64
	for k := range paymentData {
		paymentIds = append(paymentIds, k)
	}
	payments, err := ctrl.PaymentService.ListByIdsForRefund(ctx, orderID, paymentIds, merchant.No)

	if err != nil {
		return nil, err
	}
	if len(payments) == 0 {
		return nil, errors.BadRequest("1000", "PaymentNotFound")
	}

	remainRefundAmount := int64(util.MultiplyFloat(refundAmount, 100))
	var paymentLog *model.MerchantPaymentLogModel

	refundLogs := make([]*model.MerchantRefundLogModel, 0, len(payments))

	// 按照paymentIds顺序退款
	// 根据paymentIds排序
	paymentsMap := collect.ToMap(payments, func(item *model.MerchantPaymentModel) int64 {
		return item.ID
	})
	for _, paymentId := range paymentIds {
		payment := paymentsMap[paymentId]
		if remainRefundAmount <= 0 {
			return nil, errors.BadRequest("1001", "RefundAmountExceed")
		}

		refundAmt := util.Min(int64(util.MultiplyFloat(paymentData[paymentId], 100)), remainRefundAmount)
		if refundAmt > payment.Amount-payment.RefundAmount {
			return nil, errors.BadRequest("1002", "RefundAmountExceed")
		}
		if refundAmt != int64(util.MultiplyFloat(paymentData[paymentId], 100)) {
			return nil, errors.BadRequest("1003", "RefundAmountExceed")
		}

		var refundLog *model.MerchantRefundLogModel

		// 微信/支付宝需要获取支付日志
		if payment.PaymentTypeID == consts.PAY_TYPE_WECHAT || payment.PaymentTypeID == consts.PAY_TYPE_ALIPAY {
			paymentLog, err = ctrl.PaymentLogService.GetByPaymentNo(ctx, payment.PaymentNo, payment.MerchantNo)
			if err != nil {
				return nil, err
			}
			if paymentLog == nil {
				return nil, errors.BadRequest("1002", "PaymentNotFound")
			}
			refundLog, err = ctrl.RefundLogService.CreateFromPaymentLog(ctx, merchant, orderID, batchID, paymentLog, refundAmt, consts.REFUND_TYPE_ORDER)
		} else {
			// 现金/VIP直接创建退款记录
			refundLog, err = ctrl.RefundLogService.CreateFromPayment(ctx, merchant, batchID, payment, refundAmt, consts.REFUND_TYPE_ORDER)
		}

		if err != nil {
			return nil, err
		}

		refundLogs = append(refundLogs, refundLog)
		remainRefundAmount -= refundAmt
	}

	if remainRefundAmount > 0 {
		return nil, errors.BadRequest("1004", "RefundAmountExceed")
	}

	// 更新退款数量
	if err = ctrl.RefundBatchService.UpdateRefundCount(ctx, merchant.No, batchID, len(refundLogs)); err != nil {
		return nil, err
	}

	return refundLogs, nil
}

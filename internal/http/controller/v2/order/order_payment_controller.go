package order

import (
	"context"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"ros-api-go/internal/broadcast/order_broadcast"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/request/order_request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/internal/sms"
	"ros-api-go/pkg/crypto/hash"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"strconv"
	"time"
)

type OrderPaymentController struct {
	PaymentService     *service.PaymentService
	PaymentTypeService *service.PaymentTypeService
	OrderCloudService  *service.OrderCloudService
	MerchantService    *service.MerchantService
	PaymentLogService  *service.PaymentLogService
	WechatPayService   *service.WechatPayService
	AlipayService      *service.AlipayService
	CustomerService    *service.CustomerService
	DebtHolderService  *service.DebtHolderService
	Trans              *util.Trans
}

// OfflinePay 线下支付
// @Tags 云端订单支付接口
// @Summary 线下支付
// @Accept  json
// @Produce  json
// @Param body body order_request.PaymentRequest true "body"
// @Success 200 {object} util.ResponseResult "成功"
// @Failure 400 {object} util.ResponseResult "错误"
// @Router /v2/payment/offline [post]
// @Param id path int64 true "订单ID"
// @X-Author ["Merdan"]
// @X-Date ["2025/4/9 12:16"]
// @X-Version ["2.0"]

func (ctrl *OrderPaymentController) OfflinePay(c *gin.Context) {
	ctx := c.Request.Context()
	// 从请求参数中提取订单ID。

	formItem := order_request.PaymentRequest{}

	merchantNo := c.Request.Header.Get("Merchantno")
	userID := util.FromUserID(ctx)

	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}

	order, paymentType, err := ctrl.getPaymentData(
		ctx, formItem.PaymentNo, formItem.OrderId, formItem.OrderNo,
		formItem.PaymentId, 0, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 准备线下支付记录。
	now := time.Now()
	orderOfflinePayment := &model.MerchantPaymentModel{
		MerchantNo:    merchantNo,
		OrderID:       0,
		OrderNo:       order.No,
		PaymentTypeID: paymentType.ID,
		CustomerID:    order.CustomerID,
		PaymentNo:     formItem.PaymentNo,
		Amount:        int64(util.MultiplyFloat(formItem.Amount, 100)),
		PayType:       "",
		Status:        consts.PAY_STATUS_PAID,
		PaidAt:        &now,
		CashierID:     userID,
	}

	_, err = ctrl.PaymentService.CreateOfflinePayment(ctx, orderOfflinePayment)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 推送广播，通知相关系统或模块订单支付状态已更新。
	order_broadcast.SendOrderBroadcast(order_broadcast.ActionPayment, order.MerchantNo, order.TableID, order.ID, nil)

	// 返回成功
	util.ResOK(c)
}

// MicroPay 微信付款码支付
// @Summary 微信支付
// @Tags 云端订单支付接口
// @Param body body order_request.MicroPayRequest true "body"
// @Success 200 {object} util.ResponseResult "成功"
// @Failure 400 {object} util.ResponseResult "错误"
// @Router /v2/payment/micro [post]
// @Param id path int64 true "订单ID"
// @X-Author ["Merdan"]
// @X-Date ["2025/4/9 12:16"]
// @X-Version ["2.0"]
func (ctrl *OrderPaymentController) MicroPay(c *gin.Context) {
	ctx := c.Request.Context()
	// 从请求参数中提取订单ID。

	formItem := order_request.MicroPayRequest{}

	merchantNo := c.Request.Header.Get("Merchantno")
	userID := util.FromUserID(ctx)

	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}

	// 获取商户信息
	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 商家是否开启微信支付/支付宝支付
	if merchant.GetWechatPaymentType() == model.WechatPaymentTypeNone {
		util.ResError(c, errors.BadRequest("", "MerchantNotSupportThirdPartPay"))
		return
	}

	order, paymentType, err := ctrl.getPaymentData(
		ctx, formItem.PaymentNo, formItem.OrderId, formItem.OrderNo,
		formItem.PaymentId, 1, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	paymentForm := request.MerchantPaymentForm{
		OrderNo:       order.No,
		PaymentNo:     formItem.PaymentNo,
		Amount:        int64(util.MultiplyFloat(formItem.Amount, 100)),
		MerchantNo:    merchantNo,
		PaymentTypeID: paymentType.ID,
		CustomerID:    order.CustomerID,
		CashierID:     userID,
	}

	// 获取支付方式
	paymentForm.PaymentTypeID, err = ctrl.PaymentService.GetPayTypeByAuthCode(formItem.AuthCode, merchant.GetWechatPaymentType())

	if err != nil {
		util.ResError(c, err)
		return
	}

	// 创建支付信息
	paymentInfo, err := ctrl.PaymentService.CreateMerchantPayment(ctx, &paymentForm)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 创建支付记录
	paymentLog, err := ctrl.PaymentLogService.CreatePaymentLog(ctx, merchant, paymentInfo, consts.TRADE_TYPE_MICROPAY, consts.ORDER_TYPE_ORDER, c.ClientIP())
	if err != nil {
		util.ResError(c, err)
		return
	}

	var paymentResult *schema.PaymentResult
	c.Copy()
	// 根据类型调用方相应的支付接口
	switch paymentForm.PaymentTypeID {
	case consts.PAY_TYPE_WECHAT:
		paymentResult, err = ctrl.WechatPayService.MicroPay(ctx, formItem.AuthCode, paymentLog, merchant)
		if err != nil {
			util.ResError(c, err)
			return
		}
	case consts.PAY_TYPE_ALIPAY:
		paymentResult, err = ctrl.AlipayService.MicroPay(ctx, formItem.AuthCode, paymentLog, merchant)
		if err != nil {
			util.ResError(c, err)
			return
		}
	default:
		util.ResError(c, errors.BadRequest("", "InvalidPayType"))
		return
	}
	// 如果支付成功，更新支付信息
	if paymentResult.Status == consts.PAY_STATUS_PAID {
		// 更新支付信息
		_, err = ctrl.PaymentLogService.UpdatePaymentStatus(ctx, paymentResult)
		_, err = ctrl.PaymentService.UpdatePaymentStatus(ctx, paymentResult)
		if err != nil {
			util.ResError(c, err)
			return
		}
		util.ResSuccess(c, paymentResult, "PaySuccess")
		return
	}
	util.ResSuccess(c, paymentResult, "PaySuccess")
}

// CustomerPay 会员支付
//
// @Tags 云端订单支付接口
// @Security ApiTokenAuth
// @Summary 会员支付
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /cloud/api/v2/payment/customer-pay [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/4/10 19:35"]
// @X-Version ["2.0"]
func (ctrl *OrderPaymentController) CustomerPay(c *gin.Context) {
	ctx := c.Request.Context()
	// 从请求参数中提取订单ID。

	formItem := order_request.CustomerPayRequest{}

	merchantNo := c.Request.Header.Get("Merchantno")
	userID := util.FromUserID(ctx)

	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}

	// 获取会员信息
	customer, err := ctrl.CustomerService.GetByID(ctx, formItem.CustomerID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 判断商户号是否一致
	if customer == nil || customer.MerchantNo != merchantNo {
		util.ResError(c, errors.BadRequest("", "CustomerNotFound"))
		return
	}

	// 获取商户信息
	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 判断支付密码是否正确
	if err := hash.CompareHashAndPassword(customer.Password, formItem.Password); err != nil {
		util.ResError(c, errors.BadRequest("", "CustomerPasswordNotCorrect"))
		return
	}

	// 获取订单
	order, paymentType, err := ctrl.getPaymentData(
		ctx, formItem.PaymentNo, formItem.OrderId, formItem.OrderNo, consts.PAY_TYPE_VIPCARD, 1, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 检查支付金额是否超过订单的VIP价格。
	if order.VipPrice < formItem.Amount {
		util.ResError(c, errors.BadRequest("", "AmountNotMatch"))
		return
	}

	// 判断会员余额是否足够
	if customer.Balance < formItem.Amount {
		util.ResError(c, errors.BadRequest("", "CustomerBalanceNotEnough"))
		return
	}

	paymentForm := request.MerchantPaymentForm{
		OrderNo:       order.No,
		PaymentNo:     formItem.PaymentNo,
		Amount:        int64(util.MultiplyFloat(formItem.Amount, 100)),
		MerchantNo:    merchantNo,
		PaymentTypeID: paymentType.ID,
		CustomerID:    customer.ID,
		CashierID:     userID,
	}
	// 创建支付记录
	paymentLog, err := ctrl.PaymentService.CreateMerchantPayment(ctx, &paymentForm)
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 扣除会员余额
	if ctrl.CustomerService.Pay(ctx, customer, paymentLog, userID) != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, "success")

	go ctrl.sendCustomerPaySMS(customer, merchant, paymentLog, formItem.Amount)

}

// DebtPay 赊账支付
// @Summary 赊账支付
// @Description 赊账支付
// @Tags 支付相关接口
// @Accept json
// @Produce json
// @Security ServerTokenAuth
// @Param body body request.DebtPaymentForm true "支付请求"
// @Success 200 {object} util.ResponseResult "支付响应"
// @Failure 400 {object} util.ResponseResult "错误响应"
// @Failure 422 {object} util.ResponseResult "错误响应"
// @Router /cloud/api/v2/payment/debt-pay [post]
// @X-Author ["Merdan"]
// @X-Date ["2025-05-14 10:00:00"]
// @X-Version ["2.0"]
func (ctrl *OrderPaymentController) DebtPay(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)
	userID := util.FromUserID(ctx)
	var formItem order_request.DebtPayRequest

	// 解析请求
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	// 获取赊账人信息
	holder, err := ctrl.DebtHolderService.GetByID(ctx, merchantNo, formItem.HolderID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 判断商户号是否一致
	if holder == nil || holder.MerchantNo != merchantNo {
		util.ResError(c, errors.BadRequest("1004", "DebtHolderNotFound"))
		return
	}

	// 判断赊账余额是否足够
	if holder.CreditLimit*100-holder.Balance < formItem.Amount {
		util.ResError(c, errors.BadRequest("", "DebtHolderBalanceNotEnough"))
		return
	}

	// 获取订单
	order, paymentType, err := ctrl.getPaymentData(
		ctx, formItem.PaymentNo, formItem.OrderId, formItem.OrderNo, consts.PAY_TYPE_DEBT, 1, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	paymentAmount, err := ctrl.PaymentService.GetTotalAmountByOrderID(ctx, order.ID, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 总支付金额是否与订单金额一致
	if int64(util.MultiplyFloat(order.Price, 100)) < paymentAmount+formItem.Amount {
		util.ResError(c, errors.BadRequest("", "AmountNotMatch"))
		return
	}

	// 创建支付记录
	paymentForm := request.MerchantPaymentForm{
		OrderNo:       order.No,
		PaymentNo:     formItem.PaymentNo,
		Amount:        formItem.Amount,
		MerchantNo:    merchantNo,
		PaymentTypeID: paymentType.ID,
		CustomerID:    holder.ID,
		CashierID:     userID,
	}
	paymentLog, err := ctrl.PaymentService.CreateMerchantPayment(ctx, &paymentForm)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if ctrl.DebtHolderService.Pay(ctx, holder, paymentLog, userID) != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, "success")
}

// PaymentQrcodeUrl 支付二维码生成接口
//
// @Tags 云端订单支付接口
// @Security ApiTokenAuth
// @Summary 云端订单支付接口
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/qrcode [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/4/17 11:37"]
// @X-Version ["2.0"]
func (ctrl *OrderPaymentController) PaymentQrcodeUrl(c *gin.Context) {
	ctx := c.Request.Context()
	formItem := order_request.PaymentRequest{}
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	merchantNo := c.Request.Header.Get("Merchantno")
	userID := util.FromUserID(ctx)

	paymentForm := request.MerchantPaymentForm{
		OrderNo:       formItem.OrderNo,
		PaymentNo:     formItem.PaymentNo,
		Amount:        int64(util.MultiplyFloat(formItem.Amount, 100)),
		PaymentTypeID: formItem.PaymentId,
		MerchantNo:    merchantNo,
		CustomerID:    userID,
	}

	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, paymentForm.MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if merchant == nil {
		util.ResError(c, errors.BadRequest("", "MerchantNotFound"))
		return
	}

	if merchant.State != model.MerchantStateActive {
		util.ResError(c, errors.BadRequest("", "MerchantNotActive"))
		return
	}

	// 商家是否开启微信支付/支付宝支付
	if merchant.GetWechatPaymentType() == model.WechatPaymentTypeNone {
		util.ResError(c, errors.BadRequest("", "MerchantNotSupportThirdPartPay"))
		return
	}

	// 如果没有支付宝授权码，走微信支付
	if merchant.AlipayAppAuthToken == "" && merchant.SubMchID != "" {
		paymentForm.PaymentTypeID = consts.PAY_TYPE_WECHAT
		ctrl.wechatNativePay(c, &paymentForm, merchant)
		return
	}

	// 生成二维码内容
	payUrl, err := ctrl.PaymentService.GetPaymentQrcodeUrl(ctx, &paymentForm)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, payUrl, "GetSuccess")

}

// PaymentQuery 支付结果查询接口
//
// @Tags 云端订单支付接口
// @Security ApiTokenAuth
// @Summary
// @Param paymentNo path string true "支付信息编号"
// @Success 200 {object} util.ResponseResult{data=schema.PaymentResult}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /cloud/api/v2/payment/query/{paymentNo} [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/4/10 19:56"]
// @X-Version ["2.0"]
func (ctrl *OrderPaymentController) PaymentQuery(c *gin.Context) {
	ctx := c.Request.Context()
	paymentNo := c.Param("paymentNo")

	if paymentNo == "" {
		util.ResError(c, errors.BadRequest("", "PaymentNoRequired"))
		return
	}
	// 获取MerchantNO
	merchantNo := c.Request.Header.Get("Merchantno")

	// 获取支付记录
	paymentLog, err := ctrl.PaymentService.GetByPaymentNo(ctx, paymentNo, merchantNo)

	if err != nil {
		util.ResError(c, err)
		return
	} else if paymentLog == nil {
		util.ResSuccess(c, schema.PaymentResult{
			PaymentNo:  paymentNo,
			MerchantNo: merchantNo,
		})
		return
	}
	// 判断商户号是否一致
	if merchantNo != paymentLog.MerchantNo {
		util.ResError(c, errors.BadRequest("", "DataNotFound"))
		return
	}

	paymentType, err := ctrl.PaymentTypeService.GetPaymentTypeByID(ctx, paymentLog.PaymentTypeID)

	if err != nil {
		util.ResError(c, err)
		return
	}

	// 支付结果
	paymentResult := schema.PaymentResult{
		OrderNo:       paymentLog.OrderNo,
		MerchantNo:    paymentLog.MerchantNo,
		PaymentTypeID: paymentLog.PaymentTypeID,
		PaymentNo:     paymentLog.PaymentNo,
		NameUg:        paymentType.NameUg,
		NameZh:        paymentType.NameZh,
	}

	// 如果已经支付成功，直接返回支付结果
	if paymentLog.Status == consts.PAY_STATUS_PAID {
		paymentResult.Status = paymentLog.Status
		paymentResult.PaidAt = paymentLog.PaidAt
	}
	util.ResSuccess(c, paymentResult, "PaymentQuerySuccess")
}

// wechatNativePay 微信扫码支付二维码
func (ctrl *OrderPaymentController) wechatNativePay(
	c *gin.Context,
	paymentForm *request.MerchantPaymentForm,
	merchant *model.MerchantModel) {
	ctx := c.Request.Context()
	// 创建支付信息
	paymentForm.PaymentTypeID = consts.PAY_TYPE_WECHAT
	paymentInfo, err := ctrl.PaymentService.CreateMerchantPayment(ctx, paymentForm)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 创建支付记录
	paymentLog, err := ctrl.PaymentLogService.CreatePaymentLog(ctx, merchant, paymentInfo, consts.TRADE_TYPE_NATIVE, consts.ORDER_TYPE_ORDER, c.ClientIP())
	if err != nil {
		util.ResError(c, err)
		return
	}
	payUrl, err := ctrl.WechatPayService.NativePay(ctx, paymentLog, merchant)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, payUrl, "GetSuccess")
}

// getPaymentData 获取支付数据
func (ctrl *OrderPaymentController) getPaymentData(
	ctx context.Context, paymentNo string, orderId int64, orderNo string, paymentTypeId int64, online int64, merchantNo string) (
	*model.CloudOrderModel, *model.PaymentTypeModel, error) {

	// 获取订单
	order, err := ctrl.OrderCloudService.GetCloudOrderByID(ctx, orderId, merchantNo)
	if err != nil {
		return nil, nil, errors.BadRequest("", "OrderNotExist")
	}
	// 检查订单状态是否允许支付。
	if order.State != 1 && order.State != 2 {
		return nil, nil, errors.BadRequest("", "OrderStateNotAllowPay")
	}
	// 确保订单编号匹配。
	if order.No != orderNo {
		return nil, nil, errors.BadRequest("", "OrderNoNotMatch")
	}

	// 检查支付信息是否存在。
	if payment, err := ctrl.PaymentService.GetByPaymentNo(ctx, paymentNo, merchantNo); err != nil && payment != nil {
		return nil, nil, errors.BadRequest("", "PaymentExists")
	}

	// 获取支付方式
	paymentType, err := ctrl.PaymentTypeService.GetPaymentTypeByID(ctx, paymentTypeId)
	// 确保支付方式存在。
	if err != nil {
		return nil, nil, errors.BadRequest("", "PaymentTypeNotFound")
	}

	// 确保支付类型（线上/线下）一致
	if paymentType.Online != online {
		return nil, nil, errors.BadRequest("", "PaymentTypeNotAllowOffline")
	}
	// 确保支付方式处于启用状态。
	if paymentType.State == 0 {
		return nil, nil, errors.BadRequest("", "PaymentTypeDisabled")
	}
	return order, paymentType, nil
}

// sendCustomerPaySMS 发送会员消费消息
func (ctrl *OrderPaymentController) sendCustomerPaySMS(
	customer *model.CustomerModel, merchant *model.MerchantModel,
	paymentLog *model.MerchantPaymentModel, price float64) {
	if merchant.SMSCount > 0 {
		leftPrice := customer.Balance - price
		newCtx := context.Background()
		defer func() {
			if r := recover(); r != nil {
				logging.Context(newCtx).Error("sms_fail 会员消费发送消息失败（panic）",
					zap.String("mobile", customer.Mobile),
					zap.Int64("customer_id", customer.ID),
					zap.String("merchant_no", merchant.No),
					zap.Int64("payment_log_id", paymentLog.ID),
					zap.String("price", strconv.FormatFloat(price, 'f', 2, 64)),
					zap.String("leftPrice", strconv.FormatFloat(leftPrice, 'f', 2, 64)),
					zap.Any("error", r))
			}
		}()
		err := sms.SendCustomerPaySMS(
			customer.Mobile,
			merchant.NameZh,
			strconv.FormatFloat(price, 'f', 2, 64),
			strconv.FormatFloat(leftPrice, 'f', 2, 64),
		)
		if err != nil {
			logging.Context(newCtx).Error("sms_fail 会员消费发送消息失败",
				zap.String("mobile", customer.Mobile),
				zap.Int64("customer_id", customer.ID),
				zap.String("merchant_no", merchant.No),
				zap.Int64("payment_log_id", paymentLog.ID),
				zap.String("price", strconv.FormatFloat(price, 'f', 2, 64)),
				zap.String("leftPrice", strconv.FormatFloat(leftPrice, 'f', 2, 64)),
				zap.Error(err))
		}
		ctrl.MerchantService.DecreaseSMSCount(newCtx, merchant.No)
	}
}

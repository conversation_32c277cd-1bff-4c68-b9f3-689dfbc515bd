package v2

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
	"strconv"
)

// DebtHolderController handles debt holder-related API endpoints
type DebtHolderController struct {
	DebtHolderService *service.DebtHolderService
	PaymentService    *service.PaymentService
	MerchantService   *service.MerchantService
}

// GetList 获取赊账人列表
//
// @Tags 赊账相关接口
// @Security ApiTokenAuth
// @Summary 获取赊账人列表
// @Description 获取赊账人列表
// @Produce json
// @Param page query int false "页码，默认1"
// @Param limit query int false "每页数量，默认20"
// @Param keyword query string false "搜索关键词（手机号、姓名）"
// @Param state query int false "状态（0禁用，1启用）"
// @Success 200 {object} util.ResponseResult{data=[]resource.DebtHolderResource,meta=util.PaginationResult}
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/debt/holders [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/13 11:10"]
// @X-Version ["2.0"]
func (ctrl *DebtHolderController) GetList(c *gin.Context) {
	ctx := c.Request.Context()

	// 绑定请求参数
	var queryForm request.DebtHolderListRequest
	if err := util.ParseQuery(c, &queryForm); err != nil {
		util.ResError(c, err)
		return
	}

	merchantNo := c.Request.Header.Get("Merchantno")

	debtHolders, pagination, err := ctrl.DebtHolderService.GetList(ctx, merchantNo, &queryForm)
	if err != nil {
		util.ResError(c, err)
		return
	}
	totalBalance, err := ctrl.DebtHolderService.GetTotalBalance(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	totalCount, err := ctrl.DebtHolderService.GetHoldersCount(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	listItemResource := resource.DebtHolderResource{}
	debtHolderResource := resource.DebtHolderListResource{
		Message: i18n.Msg(&ctx, "GetSuccess"),
		Data:    listItemResource.Collection(&ctx, debtHolders),
		Top: resource.DebtHolderTop{
			TotalCount:   totalCount,
			TotalBalance: totalBalance,
		},
		Meta: pagination,
	}

	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 添加赊账支付方式
	err = ctrl.PaymentService.CreatePaymentTypeForMerchant(ctx, merchant, consts.PAY_TYPE_DEBT)
	if err != nil {
		return
	}

	util.ResJSON(c, http.StatusOK, debtHolderResource)
}

// GetAvailableList 获取可用赊账人列表
//
// @Tags 赊账相关接口
// @Security ApiTokenAuth
// @Produce json
// @Param page query int false "页码，默认1"
// @Param limit query int false "每页数量，默认20"
// @Param keyword query string false "搜索关键词（手机号、姓名）"
// @Param state query int false "状态（0禁用，1启用）"
// @Success 200 {object} util.ResponseResult{data=[]resource.DebtHolderResource,meta=util.PaginationResult}
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/debt/holders/list [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/28 10:35"]
// @X-Version ["2.0"]
func (ctrl *DebtHolderController) GetAvailableList(c *gin.Context) {
	ctx := c.Request.Context()

	// 绑定请求参数
	var queryForm request.DebtHolderListRequest
	if err := util.ParseQuery(c, &queryForm); err != nil {
		util.ResError(c, err)
		return
	}

	queryForm.Status = strconv.Itoa(model.DebtHolderStateEnabled)
	queryForm.Page = 1
	queryForm.Limit = 50

	merchantNo := util.GetMerchantNo(c)

	debtHolders, pagination, err := ctrl.DebtHolderService.GetList(ctx, merchantNo, &queryForm)
	if err != nil {
		util.ResError(c, err)
		return
	}

	listItemResource := resource.DebtHolderResource{}
	util.ResPage(c, listItemResource.Collection(&ctx, debtHolders), pagination)
}

// Get 获取赊账人详情
//
// @Tags 赊账相关接口
// @Security ApiTokenAuth
// @Summary 获取赊账人详情
// @Description 获取赊账人详情
// @Produce json
// @Param id path int true "赊账人ID"
// @Success 200 {object} util.ResponseResult{data=resource.DebtHolderResource}
// @Failure 404 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/debt/holders/{id} [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/13 11:10"]
// @X-Version ["2.0"]
func (ctrl *DebtHolderController) Get(c *gin.Context) {
	ctx := c.Request.Context()

	// Parse ID from path
	id := util.StrToInt64(c.Param("id"))

	merchantNo := c.Request.Header.Get("Merchantno")

	debtHolder, err := ctrl.DebtHolderService.GetByID(ctx, merchantNo, id)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if debtHolder == nil {
		util.ResError(c, errors.NotFound("1003", "DebtHolderNotFound"))
		return
	}

	debtHolderResource := resource.DebtHolderResource{}
	util.ResSuccess(c, debtHolderResource.Make(&ctx, debtHolder))
}

// Create 创建赊账人
//
// @Tags 赊账相关接口
// @Security ApiTokenAuth
// @Summary 创建赊账人
// @Description 创建赊账人
// @Accept json
// @Produce json
// @Param data body request.DebtHolderRequest true "赊账人信息"
// @Success 200 {object} util.ResponseResult{data=resource.DebtHolderResource}
// @Failure 400 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/debt/holders [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/13 11:10"]
// @X-Version ["2.0"]
func (ctrl *DebtHolderController) Create(c *gin.Context) {
	ctx := c.Request.Context()

	// Get user ID from context
	userID := util.FromUserID(ctx)

	var req request.CreateDebtHolderRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	req.MerchantNo = c.Request.Header.Get("Merchantno")

	debtHolder, err := ctrl.DebtHolderService.Create(ctx, &req, userID)
	if err != nil {
		util.ResError(c, err)
		return
	}

	debtHolderResource := resource.DebtHolderResource{}
	util.ResSuccess(c, debtHolderResource.Make(&ctx, debtHolder))
}

// Update 更新赊账人
//
// @Tags 赊账相关接口
// @Security ApiTokenAuth
// @Summary 更新赊账人
// @Description 更新赊账人
// @Accept json
// @Produce json
// @Param id path int true "赊账人ID"
// @Param data body request.DebtHolderRequest true "赊账人信息"
// @Success 200 {object} util.ResponseResult{data=resource.DebtHolderResource}
// @Failure 400 {object} util.ResponseResult
// @Failure 404 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/debt/holders/{id} [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/13 11:11"]
// @X-Version ["2.0"]
func (ctrl *DebtHolderController) Update(c *gin.Context) {
	ctx := c.Request.Context()

	// Parse ID from path
	id := util.StrToInt64(c.Param("id"))

	var req request.UpdateDebtHolderRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	req.MerchantNo = c.Request.Header.Get("Merchantno")

	debtHolder, err := ctrl.DebtHolderService.Update(ctx, id, &req)
	if err != nil {
		util.ResError(c, err)
		return
	}

	debtHolderResource := resource.DebtHolderResource{}
	util.ResSuccess(c, debtHolderResource.Make(&ctx, debtHolder))
}

// UpdateStatus 更新赊账人状态
//
// @Tags 赊账相关接口
// @Security ApiTokenAuth
// @Summary 更新赊账人状态
// @Description 更新赊账人状态
// @Accept json
// @Produce json
// @Param id path int true "赊账人ID"
// @Param data body request.DebtHolderStatusRequest true "赊账人状态"
// @Success 200 {object} util.ResponseResult
// @Failure 400 {object} util.ResponseResult
// @Failure 404 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/debt/holders/{id}/status [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/13 11:11"]
// @X-Version ["2.0"]
func (ctrl *DebtHolderController) UpdateStatus(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("MerchantNo")
	printerID := c.Param("id")

	var req request.DebtHolderStatusRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.DebtHolderService.UpdateStatus(ctx, merchantNo, printerID, req.Status)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
}

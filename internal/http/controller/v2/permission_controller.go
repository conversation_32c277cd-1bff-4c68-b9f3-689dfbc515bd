package v2

import (
	"os"
	"ros-api-go/internal/http/resource/permission_response"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

// 权限管理
type PermissionController struct {
}

// List 获取权限列表
//
// @Tags 权限管理
// @Security ApiTokenAuth
// @Summary 获取权限列表
// @Success 200 {object} util.ResponseResult{data=[]permission_response.PermissionResource}
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/permissions [GET]
// @X-Author ["Alim Kirem"]
// @X-Date ["2024/12/25 12:27"]
// @X-Version ["2.0"]
func (ctrl *PermissionController) List(c *gin.Context) {
	// 读取配置文件 configs/permissionList.json
	permissionJson, err := os.ReadFile("configs/permissionList.json")
	if err != nil {
		util.ResError(c, err)
		return
	}
	var permissionList []permission_response.PermissionResource
	err = json.Unmarshal(permissionJson, &permissionList)
	if err != nil {
		util.ResError(c, err)
		return
	}

	var permissionListPtr []*permission_response.PermissionResource
	for i := range permissionList {
		permissionListPtr = append(permissionListPtr, &permissionList[i])
	}
	util.ResSuccess(c, permission_response.Make(permissionListPtr))
}

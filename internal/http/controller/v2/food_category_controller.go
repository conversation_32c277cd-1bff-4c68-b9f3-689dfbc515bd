package v2

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"
)

type FoodCategoryController struct {
	FoodCategoryService service.FoodCategoryService
}

func (fc *FoodCategoryController) GetFoodCategories(c *gin.Context) {
	ctx := c.Request.Context()
	MerchantNo := c.Request.Header.Get("MerchantNo")
	result, err := fc.FoodCategoryService.GetAvailableFoodCategories(ctx, MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&resource.FoodCategoryResource{}).Collection(result))
}

package sync

import (
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type PrinterController struct {
	PrinterService *service.PrinterService
}

// GetMerchantPrinters 同步餐厅打印机接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步餐厅打印机接口
// @Success 200 {object} util.ResponseResult{data=[]resource.PrinterResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/printers [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/2 16:43"]
// @X-Version ["2.0"]
func (ctrl *PrinterController) GetMerchantPrinters(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.PrinterService.GetMerchantPrinters(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	printerResource := resource.PrinterResource{}
	util.ResSuccess(c, printerResource.Collection(result))
}

// SyncPrinter 同步打印机信息 (本地  ===》 云端)
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步打印机信息 (本地  ===》 云端)
// @Accept json
// @Produce json
// @Param body body request.PrinterSyncRequest true "打印机信息"
// @Success 200 {object} util.ResponseResult
// @Failure 400 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/sync-printer [post]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/2 16:43"]
// @X-Version ["2.0"]
func (ctrl *PrinterController) SyncPrinter(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.PrinterSyncRequest
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	// 补充商户号
	formItem.MerchantNo = util.FromServerMerchantNo(ctx)

	if err := ctrl.PrinterService.SyncPrinter(ctx, &formItem); err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
}

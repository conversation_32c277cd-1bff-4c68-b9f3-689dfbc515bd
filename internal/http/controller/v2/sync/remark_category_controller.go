package sync

import (
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type RemarkCategoryController struct {
	RemarkCategoryService *service.RemarkCategoryService
}

// GetMerchantRemarkCategories - 获取商户备注分类
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步备注分类
// @Success 200 {object} util.ResponseResult{data=resource.RemarkCategoryResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/remark-categories [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/2 17:06"]
// @X-Version ["2.0"]
func (ctrl *RemarkCategoryController) GetMerchantRemarkCategories(c *gin.Context) {
	ctx := c.Request.Context()

	result, err := ctrl.RemarkCategoryService.GetRemarkCategories(ctx)
	if err != nil {
		util.ResError(c, err)
		return
	}
	categoryResource := resource.RemarkCategoryResource{}
	util.ResSuccess(c, categoryResource.Collection(result))
}

package sync

import (
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type MerchantRoleController struct {
	MerchantRoleService *service.MerchantRoleService
}

// GetMerchantRoles 同步商家角色权限列表接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步商家角色权限列表接口
// @Success 200 {object} util.ResponseResult{data=[]resource.MerchantRoleResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/roles [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/2 16:29"]
// @X-Version ["2.0"]
func (ctrl *MerchantRoleController) GetMerchantRoles(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)
	result, err := ctrl.MerchantRoleService.GetMerchantRoles(ctx, merchantNo, nil)
	if err != nil {
		util.ResError(c, err)
		return
	}
	roleResource := resource.MerchantRoleResource{}
	util.ResSuccess(c, roleResource.Collection(result), "GetSuccess")
}

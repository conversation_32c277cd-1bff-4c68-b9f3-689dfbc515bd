package sync

import (
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type AreaController struct {
	AreaService *service.AreaService
}

// GetMerchantAreas 同步餐厅区域接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步餐厅区域接口
// @Success 200 {object} util.ResponseResult{data=[]resource.AreaResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/areas [get]
// @X-Author {"name": "merdan"}
// @X-Date ["2024-12-02 12:50:39"]
// @X-Version ["2.0"]
func (ctrl *AreaController) GetMerchantAreas(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)
	result, err := ctrl.AreaService.GetMerchantAreas(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	areaResource := resource.AreaResource{}
	util.ResSuccess(c, areaResource.Collection(result))
}

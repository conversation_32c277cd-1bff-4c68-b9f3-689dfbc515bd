package sync

import (
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

// FoodsController 菜品同步接口
type FoodsController struct {
	FoodsService *food_service.FoodsService
}

// GetMerchantFoodsForSync 同步菜品接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步菜品接口
// @Success 200 {object} util.ResponseResult{data=resource.FoodResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/foods [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/2 16:04"]
// @X-Version ["2.0"]
func (ctrl *FoodsController) GetMerchantFoodsForSync(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.FoodsService.GetMerchantFoodsForSync(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	foodResource := resource.FoodResource{}
	util.ResSuccess(c, foodResource.Collection(result))
}

// UpdateFoodSellClearData 同步菜品沽清数据接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步菜品沽清数据接口
// @Param body body request.FoodSellClearDataRequest true "沽清数据"
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/foods/sell-clear-data [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/2/19 15:38"]
// @X-Version ["2.0"]
func (ctrl *FoodsController) UpdateFoodSellClearData(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.FoodSellClearDataRequest
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	formItem.MerchantNo = util.FromServerMerchantNo(ctx)

	if err := ctrl.FoodsService.UpdateFoodSellClearData(ctx, formItem); err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
}

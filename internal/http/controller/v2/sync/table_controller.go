package sync

import (
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

// Defining the `TableService` api.
type TableController struct {
	TableService *service.TableService
}

// GetMerchantTables 同步餐桌信息接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步餐桌信息接口
// @Success 200 {object} util.ResponseResult{data=resource.TableResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/tables [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/3 17:06"]
// @X-Version ["2.0"]
func (ctrl *TableController) GetMerchantTables(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.TableService.GetMerchantTables(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	tableResource := resource.TableResource{}
	util.ResSuccess(c, tableResource.Collection(result))
}

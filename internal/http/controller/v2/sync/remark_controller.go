package sync

import (
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type RemarkController struct {
	RemarkService *service.RemarkService
}

// GetMerchantRemarks - 获取商户备注列表
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 获取商户备注列表
// @Success 200 {object} util.ResponseResult{data=[]resource.RemarkResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/remarks [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/2 16:44"]
// @X-Version ["2.0"]
func (ctrl *RemarkController) GetMerchantRemarks(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.RemarkService.GetMerchantRemarks(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	categoryResource := resource.RemarkResource{}
	util.ResSuccess(c, categoryResource.Collection(result))
}

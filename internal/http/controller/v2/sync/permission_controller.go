package sync

import (
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type PermissionController struct {
	PermissionService *service.PermissionService
}

// GetAllPermissions 同步权限列表接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步权限列表接口
// @Success 200 {object} util.ResponseResult{data=[]resource.PermissionResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/permissions [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/10 16:42"]
// @X-Version ["2.0"]
func (ctrl *PermissionController) GetAllPermissions(c *gin.Context) {
	ctx := c.Request.Context()
	result, err := ctrl.PermissionService.GetAllPermissions(ctx)
	if err != nil {
		util.ResError(c, err)
		return
	}
	permissionResource := resource.PermissionResource{}
	util.ResSuccess(c, permissionResource.Collection(result), "GetSuccess")
}

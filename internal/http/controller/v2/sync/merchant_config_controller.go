package sync

import (
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type MerchantConfigController struct {
	MerchantConfigService *service.MerchantConfigService
}

// GetMerchantConfigs 同步商户设置接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步商户设置接口
// @Success 200 {object} util.ResponseResult{data=[]resource.MerchantConfigResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/merchant-configs [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/2 16:25"]
// @X-Version ["2.0"]
func (ctrl *MerchantConfigController) GetMerchantConfigs(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.MerchantConfigService.GetMerchantMerchantConfigs(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	merchantConfigResource := resource.MerchantConfigResource{}
	util.ResSuccess(c, merchantConfigResource.Collection(result))
}

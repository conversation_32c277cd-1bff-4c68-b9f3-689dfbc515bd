package sync

import (
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type FoodPrinterController struct {
	FoodPrinterService *food_service.FoodPrinterService
}

// GetMerchantFoodPrinters 同步美食打印机关联信息接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步美食打印机关联信息接口
// @Success 200 {object} util.ResponseResult{data=[]resource.FoodPrinterResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/food-printers [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/2 16:23"]
// @X-Version ["2.0"]
func (ctrl *FoodPrinterController) GetMerchantFoodPrinters(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.FoodPrinterService.GetMerchantFoodPrinters(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	FoodPrinterResource := resource.FoodPrinterResource{}
	util.ResSuccess(c, FoodPrinterResource.Collection(result))
}

// SyncFoodPrinter 同步美食打印机接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步美食打印机接口
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/food-printers [post]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/2 16:23"]
// @X-Version ["2.0"]
func (ctrl *FoodPrinterController) SyncFoodPrinter(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	var req request.PrinterFoodsSyncRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	// 保存Mapping关系
	foods := make([]*model.FoodPrinterModel, 0, len(req.FoodIds))
	for _, foodID := range req.FoodIds {
		foods = append(foods, &model.FoodPrinterModel{
			MerchantNo: merchantNo,
			FoodID:     foodID,
			PrinterID:  req.PrinterId,
		})
	}
	if err := ctrl.FoodPrinterService.SyncPrinterFoods(ctx, merchantNo, req.PrinterId, foods); err != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, nil)
}

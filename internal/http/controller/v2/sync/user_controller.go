package sync

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"
)

// UserController
type UserController struct {
	UserService *service.UserService
}

// GetMerchantUsers 同步用户数据
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步用户数据
// @Success 200 {object} util.ResponseResult{data=resource.UserResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/users [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/3 17:07"]
// @X-Version ["2.0"]
func (ctrl *UserController) GetMerchantUsers(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.UserService.GetMerchantUsersWithPermissions(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	loginResource := resource.LoginUserResource{}
	util.ResSuccess(c, loginResource.Collection(ctx, result))
}

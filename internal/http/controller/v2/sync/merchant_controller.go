package sync

import (
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type MerchantInfoUpdateController struct {
	MerchantInfoUpdateService *service.MerchantInfoUpdateService
}

// GetMerchantInfoUpdate 商家各项信息更新时间接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 商家各项信息更新时间接口
// @Success 200 {object} util.ResponseResult{data=resource.MerchantInfoUpdateResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/merchant-info-updates [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/2 16:27"]
// @X-Version ["2.0"]
func (ctrl *MerchantInfoUpdateController) GetMerchantInfoUpdate(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)
	result, err := ctrl.MerchantInfoUpdateService.GetMerchantInfoUpdate(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	updateResource := resource.MerchantInfoUpdateResource{}
	util.ResSuccess(c, updateResource.Make(result))
}

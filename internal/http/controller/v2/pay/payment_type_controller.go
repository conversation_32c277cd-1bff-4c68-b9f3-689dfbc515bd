package pay

import (
	"github.com/samber/lo"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type PaymentTypeController struct {
	PaymentTypeService *service.PaymentTypeService
	MerchantService    *service.MerchantService
}

// GetPaymentTypesForRecharge 获取商家用于充值/还款的支付方式列表
//
// @Tags 支付方式
// @Security ApiTokenAuth
// @Summary 同步平台支付方式接口
// @Success 200 {object} util.ResponseResult{data=[]resource.PaymentTypeResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/payment-types [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/9 17:02"]
// @X-Version ["2.0"]
func (ctrl *PaymentTypeController) GetPaymentTypesForRecharge(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)
	result, err := ctrl.PaymentTypeService.GetPaymentTypesForRecharge(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 过滤掉支付宝
	result = lo.Filter(result, func(item *model.PaymentTypeModel, i int) bool {
		return item.ID != consts.PAY_TYPE_ALIPAY
	})

	if merchant.AlipayAppAuthToken != "" {
		wechat, found := lo.Find(result, func(item *model.PaymentTypeModel) bool {
			return item.ID == consts.PAY_TYPE_WECHAT
		})
		if found {
			wechat.NameZh = "二维码支付"
			wechat.NameUg = "ئىككىلىك كود"
			wechat.Icon = "/uploads/images/payment_type/qrcode.png"
		}
	}

	typeResource := resource.PaymentTypeResource{}
	util.ResSuccess(c, typeResource.Collection(result))
}

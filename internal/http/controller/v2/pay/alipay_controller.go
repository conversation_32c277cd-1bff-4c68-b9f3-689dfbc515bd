package pay

import (
	"context"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/cachex"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
)

type AliPayController struct {
	PaymentService            *service.PaymentService
	MerchantService           *service.MerchantService
	PaymentLogService         *service.PaymentLogService
	AlipayService             *service.AlipayService
	CustomerService           *service.CustomerService
	PaymentHandler            *handler.PaymentHandler
	RefundHandler             *handler.RefundHandler
	RechargeHandler           *handler.RechargeHandler
	RechargeOrderService      *service.RechargeOrderService
	DebtRepaymentHandler      *handler.DebtRepaymentHandler
	DebtRepaymentOrderService *service.DebtRepaymentOrderService
	DebtHolderService         *service.DebtHolderService
	Cache                     cachex.Cacher
	Trans                     *util.Trans
}

// JsAPIPay 支付宝JSAPI支付接口
//
// @Tags 支付相关接口
// @Security ServerTokenAuth
// @Summary 支付宝JSAPI支付接口
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/alipay/jsapi-pay/{merchantNo}/{paymentNo} [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/17 13:19"]
// @X-Version ["2.0"]
func (ctrl *AliPayController) JsAPIPay(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取参数(authCode, merchantNo, paymentNo)
	merchantNo := c.Param("merchantNo")
	paymentNo := c.Param("paymentNo")
	var formItem request.JsAPIPaymentForm
	err := util.ParseJSON(c, &formItem)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 根据code获取openid
	token, err := ctrl.AlipayService.SystemOauthToken(ctx, merchantNo, formItem.AuthCode)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 获取商户信息
	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if merchant == nil {
		util.ResError(c, errors.BadRequest("", "MerchantNotFound"))
		return
	}

	if merchant.State != model.MerchantStateActive {
		util.ResError(c, errors.BadRequest("", "MerchantNotActive"))
		return
	}

	// 如果未开通支付宝特约商户
	if merchant.AlipayAppAuthToken == "" {
		util.ResError(c, errors.BadRequest("", "PleaseUseWechatPay"))
		return
	}

	var paymentLog *model.MerchantPaymentLogModel
	switch formItem.OrderType {
	case consts.ORDER_TYPE_ORDER:
		paymentLog, err = ctrl.getPaymentInfoForOrder(ctx, paymentNo, merchant, util.GetClientIP(c.Request))
	case consts.ORDER_TYPE_RECHARGE:
		paymentLog, err = ctrl.getPaymentInfoForRecharge(ctx, paymentNo, merchant, util.GetClientIP(c.Request))
	case consts.ORDER_TYPE_DEBT_REPAYMENT:
		paymentLog, err = ctrl.getPaymentInfoForDebtRepayment(ctx, paymentNo, merchant, util.GetClientIP(c.Request))
	default:
		util.ResError(c, errors.BadRequest("", "InvalidOrderType"))
		return
	}

	if err != nil {
		util.ResError(c, err)
		return
	}

	// 调用支付宝支付接口
	jsAPIPay, err := ctrl.AlipayService.JsAPIPay(ctx, token.OpenId, paymentLog, merchant)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 返回数据
	util.ResSuccess(c, jsAPIPay, "Success")
}

// getPaymentInfoForOrder 获取订单支付信息
func (ctrl *AliPayController) getPaymentInfoForOrder(ctx context.Context, paymentNo string, merchant *model.MerchantModel, clientIP string) (*model.MerchantPaymentLogModel, error) {
	// 先查询支付记录
	paymentInfo, err := ctrl.PaymentService.GetByPaymentNo(ctx, paymentNo, merchant.No)
	if err != nil {
		return nil, err
	}

	// 如果使用微信扫过,则修改支付方式为支付宝
	if paymentInfo != nil && paymentInfo.PaymentTypeID != consts.PAY_TYPE_ALIPAY {
		if err = ctrl.PaymentService.SetPaymentType(ctx, paymentNo, consts.PAY_TYPE_ALIPAY); err == nil {
			paymentInfo.PaymentTypeID = consts.PAY_TYPE_ALIPAY
		} else {
			return nil, err
		}
	}

	paymentForm := new(request.MerchantPaymentForm)
	// 如果不存在就创建支付记录
	if paymentInfo == nil {
		// 获取支付缓存信息
		paymentCache, _, err := ctrl.Cache.Get(ctx, consts.CacheNSForPayment, paymentNo)

		if err != nil || paymentCache == "" {
			return nil, errors.BadRequest("1001", "PaymentExpired")
		}

		if err = json.Unmarshal([]byte(paymentCache), &paymentForm); err != nil {
			return nil, err
		}

		// 设置支付方式为微信支付
		paymentForm.PaymentTypeID = consts.PAY_TYPE_ALIPAY

		// 创建支付信息
		paymentInfo, err = ctrl.PaymentService.CreateMerchantPayment(ctx, paymentForm)
		if err != nil {
			return nil, err
		}
	}

	if paymentInfo.Status == consts.PAY_STATUS_PAID {
		return nil, errors.BadRequest("", "PaymentAlreadyPaid")
	}

	// 创建支付记录
	paymentLog, err := ctrl.PaymentLogService.CreatePaymentLog(ctx, merchant, paymentInfo, consts.TRADE_TYPE_JSAPI, consts.ORDER_TYPE_ORDER, clientIP)
	if err != nil {
		return nil, err
	}

	return paymentLog, nil
}

// getPaymentInfoForRecharge 获取充值支付信息
func (ctrl *AliPayController) getPaymentInfoForRecharge(ctx context.Context, paymentNo string, merchant *model.MerchantModel, clientIP string) (*model.MerchantPaymentLogModel, error) {

	rechargeOrder, err := ctrl.RechargeOrderService.GetByOrderNo(ctx, merchant.No, paymentNo)
	if err != nil {
		return nil, err
	}
	if rechargeOrder == nil {
		return nil, errors.BadRequest("", "OrderNotFound")
	}

	if rechargeOrder.Status == model.RechargeOrderStatusSuccess {
		return nil, errors.BadRequest("", "OrderAlreadyPaid")
	}

	// 设置支付方式为微信支付
	rechargeOrder.PaymentTypeID = consts.PAY_TYPE_ALIPAY

	paymentLog, err := ctrl.RechargeHandler.CreateRechargePaymentLog(ctx, rechargeOrder, consts.TRADE_TYPE_JSAPI, clientIP, merchant)

	return paymentLog, err
}

// getPaymentInfoForDebtRepayment 获取赊账还款支付信息
func (ctrl *AliPayController) getPaymentInfoForDebtRepayment(ctx context.Context, paymentNo string, merchant *model.MerchantModel, clientIP string) (*model.MerchantPaymentLogModel, error) {

	repaymentOrder, err := ctrl.DebtRepaymentOrderService.GetByOrderNo(ctx, merchant.No, paymentNo)

	// 设置支付方式为微信支付
	repaymentOrder.PaymentTypeID = consts.PAY_TYPE_ALIPAY

	paymentLog, err := ctrl.DebtRepaymentHandler.CreateDebtRepaymentPaymentLog(ctx, repaymentOrder, consts.TRADE_TYPE_JSAPI, clientIP, merchant)

	return paymentLog, err
}

// HandlePaymentNotify
//
// @Tags 支付宝支付相关接口
// @Summary
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/alipay/payment-notify/{paymentNo} [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/12 10:09"]
// @X-Version ["2.0"]
func (ctrl *AliPayController) HandlePaymentNotify(c *gin.Context) {
	ctx := c.Request.Context()

	// 将request写入logger
	logging.Context(ctx).Info("支付宝支付通知", zap.Any("request", c.Request.Body))

	// 解析支付宝支付通知
	paymentResult, err := ctrl.AlipayService.ParsePaymentNotify(ctx, c.Request)
	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "解析支付宝支付通知失败"})
		return
	}

	// 获取支付记录
	paymentLog, err := ctrl.PaymentLogService.GetByOutTradeNo(ctx, paymentResult.OutTradeNo, paymentResult.MerchantNo)

	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "支付信息查询失败"})
		return
	} else if paymentLog == nil { // 支付记录不存在, 无需处理
		c.String(200, "success")
		return
	}

	if paymentLog.Status == consts.PAY_STATUS_PAID { // 支付记录已处理, 无需处理
		c.String(200, "success")
		return
	}

	// 更新支付记录
	if paymentResult.Status == consts.PAY_STATUS_PAID {

		err = ctrl.Trans.Exec(ctx, func(ctx context.Context) error {
			// 更新支付信息
			if _, err = ctrl.PaymentLogService.UpdatePaymentStatus(ctx, paymentResult); err != nil {
				return err
			}

			if _, err := ctrl.PaymentService.UpdatePaymentStatus(ctx, paymentResult); err != nil {
				return err
			}
			// 处理云端扫码点菜部分
			if err := ctrl.PaymentHandler.HandleScanOrder(ctx, paymentLog, paymentResult); err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			util.ResError(c, err)
			return
		}
	}
	// 删除支付缓存
	ctrl.Cache.Delete(ctx, consts.CacheNSForPayment, paymentResult.PaymentNo)
	// 处理重复支付
	ctrl.RefundHandler.HandleRepeatedPayment(ctx, paymentLog, paymentResult)

	c.String(http.StatusOK, "success")
}

// HandleRechargeNotify 处理VIP充值通知
//
// @Tags 支付宝支付相关接口
// @Security ServerTokenAuth
// @Summary 处理VIP充值通知
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/alipay/recharge-notify/{paymentNo} [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/12 12:59"]
// @X-Version ["2.0"]
func (ctrl *AliPayController) HandleRechargeNotify(c *gin.Context) {
	ctx := c.Request.Context()

	// 解析支付宝支付通知
	paymentResult, err := ctrl.AlipayService.ParsePaymentNotify(ctx, c.Request)
	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "解析支付宝支付通知失败"})
		return
	}

	// 获取支付记录
	paymentLog, err := ctrl.PaymentLogService.GetByOutTradeNo(ctx, paymentResult.OutTradeNo, paymentResult.MerchantNo)

	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "支付信息查询失败"})
		return
	} else if paymentLog == nil { // 支付记录不存在, 无需处理
		return
	}

	if paymentLog.Status == consts.PAY_STATUS_PAID { // 支付记录已处理, 无需处理
		c.Status(200)
		return
	}

	// 更新支付记录
	if paymentResult.Status == consts.PAY_STATUS_PAID {
		order, err := ctrl.CustomerService.GetRechargeOrderByOrderNo(ctx, paymentLog.MerchantNo, paymentLog.OrderNo)
		if err != nil {
			util.ResError(c, err)
		}
		err = ctrl.RechargeHandler.HandleOnlinePaySuccess(ctx, order.Customer, order, paymentResult)
		if err != nil {
			util.ResError(c, err)
		}
	}
	// 删除支付缓存
	ctrl.Cache.Delete(ctx, consts.CacheNSForPayment, paymentResult.PaymentNo)

	c.Status(200)
}

func (ctrl *AliPayController) HandleDebtRepaymentNotify(c *gin.Context) {
	ctx := c.Request.Context()

	// 解析微信支付通知
	paymentResult, err := ctrl.AlipayService.ParsePaymentNotify(ctx, c.Request)
	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "解析支付宝支付通知失败"})
		return
	}

	// 获取支付记录
	paymentLog, err := ctrl.PaymentLogService.GetByOutTradeNo(ctx, paymentResult.OutTradeNo, paymentResult.MerchantNo)

	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "支付信息查询失败"})
		return
	} else if paymentLog == nil { // 支付记录不存在, 无需处理
		return
	}

	if paymentLog.Status == consts.PAY_STATUS_PAID { // 支付记录已处理, 无需处理
		c.Status(200)
		return
	}

	// 更新支付记录
	if paymentResult.Status == consts.PAY_STATUS_PAID {
		order, err := ctrl.DebtHolderService.GetRepaymentOrderByPaymentNo(ctx, paymentResult.PaymentNo)
		if err != nil {
			util.ResError(c, err)
		}
		err = ctrl.DebtRepaymentHandler.HandleOnlinePaySuccess(ctx, order.Holder, order, paymentResult)
		if err != nil {
			util.ResError(c, err)
		}
	}

	// 删除支付缓存
	ctrl.Cache.Delete(ctx, consts.CacheNSForPayment, paymentResult.PaymentNo)

	c.Status(200)
}

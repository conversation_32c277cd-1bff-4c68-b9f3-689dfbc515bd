package pay

import (
	"context"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/internal/sms"
	"ros-api-go/pkg/crypto/hash"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"strconv"

	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
)

type CustomerPayController struct {
	CustomerService *service.CustomerService
	MerchantService *service.MerchantService
	PaymentService  *service.PaymentService // 支付服务
}

// Pay 会员支付
// @Summary 会员支付
// @Description 会员支付
// @Tags 支付相关接口
// @Accept json
// @Produce json
// @Param body body request.CustomerPaymentForm true "支付请求"
// @Success 200 {object} util.ResponseResult "支付响应"
// @Failure 400 {object} util.ResponseResult "错误响应"
// @Failure 422 {object} util.ResponseResult "错误响应"
// @Router /api/v2/payment/customer-pay [post]
// @X-Author ["Alim Kirem"]
// @X-Date ["2025-01-02"]
// @X-Version ["2.0"]
func (ctrl *CustomerPayController) Pay(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)
	var formItem request.CustomerPaymentForm

	// 解析请求
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	// 获取会员信息
	customer, err := ctrl.CustomerService.GetByID(ctx, formItem.CustomerID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 判断商户号是否一致
	if customer == nil || customer.MerchantNo != merchantNo {
		util.ResError(c, errors.BadRequest("", "CustomerNotFound"))
		return
	}
	// 判断会员状态是否正常
	if customer.State != model.CustomerStateActive {
		util.ResError(c, errors.BadRequest("", "CustomerNotFound"))
		return
	}

	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, merchantNo)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "MerchantNotFound"))
		return
	}
	// 判断支付密码是否正确
	if err := hash.CompareHashAndPassword(customer.Password, formItem.Password); err != nil {
		util.ResError(c, errors.BadRequest("", "CustomerPasswordNotCorrect"))
		return
	}
	amount := int64(util.MultiplyFloat(customer.Balance, 100))
	// 判断会员余额是否足够
	if amount < formItem.Amount {
		util.ResError(c, errors.BadRequest("", "CustomerBalanceNotEnough"))
		return
	}
	formItem.MerchantNo = merchantNo
	formItem.PaymentTypeID = consts.PAY_TYPE_VIPCARD
	formItem.MerchantPaymentForm.CustomerID = formItem.CustomerID
	// 创建支付记录
	paymentLog, err := ctrl.PaymentService.CreateMerchantPayment(ctx, &formItem.MerchantPaymentForm)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if ctrl.CustomerService.Pay(ctx, customer, paymentLog, formItem.CashierID) != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, "success")

	go ctrl.sendCustomerPaySMS(customer, merchant, paymentLog, &formItem)
}

func (ctrl *CustomerPayController) sendCustomerPaySMS(
	customer *model.CustomerModel, merchant *model.MerchantModel,
	paymentLog *model.MerchantPaymentModel, requestData *request.CustomerPaymentForm) {
	if merchant.SMSCount > 0 {
		price := util.DivideFloat(float64(requestData.Amount), 100)
		leftPrice := customer.Balance - price

		newCtx := context.Background()
		defer func() {
			if r := recover(); r != nil {
				logging.Context(newCtx).Error("sms_fail 会员消费发送消息失败（panic）",
					zap.String("mobile", customer.Mobile),
					zap.Int64("customer_id", customer.ID),
					zap.String("merchant_no", merchant.No),
					zap.Int64("payment_log_id", paymentLog.ID),
					zap.String("price", strconv.FormatFloat(price, 'f', 2, 64)),
					zap.String("leftPrice", strconv.FormatFloat(leftPrice, 'f', 2, 64)),
					zap.Any("error", r))
			}
		}()
		err := sms.SendCustomerPaySMS(
			customer.Mobile,
			merchant.NameZh,
			strconv.FormatFloat(price, 'f', 2, 64),
			strconv.FormatFloat(leftPrice, 'f', 2, 64),
		)
		if err != nil {
			logging.Context(newCtx).Error("sms_fail 会员消费发送消息失败",
				zap.String("mobile", customer.Mobile),
				zap.Int64("customer_id", customer.ID),
				zap.String("merchant_no", merchant.No),
				zap.Int64("payment_log_id", paymentLog.ID),
				zap.String("price", strconv.FormatFloat(price, 'f', 2, 64)),
				zap.String("leftPrice", strconv.FormatFloat(leftPrice, 'f', 2, 64)),
				zap.Error(err))
		}
		ctrl.MerchantService.DecreaseSMSCount(newCtx, merchant.No)
	}
}

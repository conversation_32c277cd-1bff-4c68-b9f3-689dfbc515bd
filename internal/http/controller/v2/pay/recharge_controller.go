package pay

import (
	"context"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/internal/sms"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"strconv"
)

type RechargeController struct {
	Trans                *util.Trans
	MerchantService      *service.MerchantService      // 商户服务
	PaymentService       *service.PaymentService       // 支付服务
	PaymentTypeService   *service.PaymentTypeService   // 支付方式服务
	WechatPayService     *service.WechatPayService     // 微信支付服务
	AlipayService        *service.AlipayService        // 支付宝支付服务
	PaymentLogService    *service.PaymentLogService    // 支付日志服务
	CustomerService      *service.CustomerService      // 客户服务
	RechargeOrderService *service.RechargeOrderService // 充值订单服务
	RechargeHandler      *handler.RechargeHandler      // 充值处理器
}

//	线下支付充值
//
// @Tags VIP充值相关接口
// @Security ApiTokenAuth
// @Summary 线下支付充值 : 现金/个人微信/个人支付宝等线下支付方式都走这个接口
// @Param body body request.RechargeOfflineForm true "body"
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/recharge/offline-pay [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/1/17 16:49"]
// @X-Version ["2.0"]
func (ctrl *RechargeController) OfflinePay(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.RechargeOfflineForm
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}

	// 设置MerchantNo
	formItem.MerchantNo = c.Request.Header.Get("Merchantno")
	formItem.CashierID = util.FromUserID(c.Request.Context())
	formItem.RechargeRequest.PaymentTypeID = formItem.PaymentTypeID

	paymentType, err := ctrl.PaymentTypeService.GetMerchantActivePaymentTypeByID(ctx, formItem.MerchantNo, formItem.PaymentTypeID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if paymentType == nil {
		util.ResError(c, errors.BadRequest("", "PaymentTypeNotFound"))
		return
	}
	if paymentType.Online == 1 {
		util.ResError(c, errors.BadRequest("", "NotSupportOfflinePayment"))
		return
	}

	// 获取客户信息
	customer, err := ctrl.CustomerService.GetByID(ctx, formItem.CustomerID)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if customer == nil || customer.MerchantNo != formItem.MerchantNo {
		util.ResError(c, errors.BadRequest("", "CustomerNotFound"))
		return
	}
	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, formItem.MerchantNo)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "MerchantNotFound"))
		return
	}
	// 创建充值订单
	var rechargeOrder *model.CustomerRechargeOrderModel

	err = ctrl.Trans.Exec(ctx, func(ctx context.Context) error {
		rechargeOrder, err = ctrl.RechargeHandler.CreateRechargeOrder(ctx, &formItem.RechargeRequest)
		if err != nil {
			return err
		}
		if err = ctrl.RechargeHandler.UpdateCustomerBalance(ctx, customer, rechargeOrder, formItem.PaymentTypeID); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 支付类型
	payType, err := ctrl.PaymentTypeService.GetPaymentTypeByID(ctx, formItem.PaymentTypeID)
	if err != nil {
		util.ResError(c, err)
	}
	// 重新获取客户信息
	customer, err = ctrl.CustomerService.GetByID(ctx, rechargeOrder.CustomerID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	paymentTypeName := payType.NameUg
	if i18n.IsZh(&ctx) {
		paymentTypeName = payType.NameZh
	}
	rechargeResult := schema.RechargeResult{
		Status:         1, // 1 充值成功
		Mobile:         customer.Mobile,
		Name:           customer.Name,
		Balance:        customer.Balance,
		RechargeAmount: util.DivideFloat(float64(rechargeOrder.RechargeAmount), 100),
		PresentAmount:  util.DivideFloat(float64(rechargeOrder.PresentAmount), 100),
		PaymentType:    paymentTypeName,
	}
	util.ResSuccess(c, rechargeResult, "PaySuccess")
	if merchant.SMSCount > 0 {
		price := util.DivideFloat(float64(rechargeOrder.RechargeAmount+rechargeOrder.PresentAmount), 100)
		leftPrice := customer.Balance

		go func() {
			newCtx := context.Background()
			defer func() {
				if r := recover(); r != nil {
					logging.Context(newCtx).Error("sms_fail 会员消费发送消息失败（panic）",
						zap.String("mobile", customer.Mobile),
						zap.Int64("customer_id", customer.ID),
						zap.String("merchant_no", customer.MerchantNo),
						zap.Int64("recharge_id", rechargeOrder.ID),
						zap.String("price", strconv.FormatFloat(price, 'f', 2, 64)),
						zap.String("leftPrice", strconv.FormatFloat(leftPrice, 'f', 2, 64)),
						zap.Any("error", r))
				}
			}()
			err := sms.SendCustomerRechargeSMS(
				customer.Mobile,
				merchant.NameZh,
				strconv.FormatFloat(price, 'f', 2, 64),
				strconv.FormatFloat(leftPrice, 'f', 2, 64),
			)
			if err != nil {
				logging.Context(newCtx).Error("sms_fail 会员消费发送消息失败",
					zap.String("mobile", customer.Mobile),
					zap.Int64("customer_id", customer.ID),
					zap.String("merchant_no", customer.MerchantNo),
					zap.Int64("recharge_id", rechargeOrder.ID),
					zap.String("price", strconv.FormatFloat(price, 'f', 2, 64)),
					zap.String("leftPrice", strconv.FormatFloat(leftPrice, 'f', 2, 64)),
					zap.Error(err))
			}
			ctrl.MerchantService.DecreaseSMSCount(newCtx, merchant.No)
		}()
	}
}

// MicroPay 微信付款码充值
//
// @Tags VIP充值相关接口
// @Security ApiTokenAuth
// @Summary 微信付款码充值
// @Param body body request.RechargeMicroPayForm true "body"
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/recharge/micro-pay [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/1/17 16:05"]
// @X-Version ["2.0"]
func (ctrl *RechargeController) MicroPay(c *gin.Context) {
	ctx := c.Request.Context()
	var rechargeForm request.RechargeMicroPayForm
	err := util.ParseJSON(c, &rechargeForm)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 获取MerchantNO
	rechargeForm.MerchantNo = util.GetMerchantNo(c)
	rechargeForm.CashierID = util.FromUserID(ctx)

	// 获取客户信息
	customer, merchant, err := ctrl.getRechargerInfo(ctx, &rechargeForm.RechargeRequest)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 商家是否开启微信支付/支付宝支付
	if merchant.GetWechatPaymentType() == model.WechatPaymentTypeNone {
		util.ResError(c, errors.BadRequest("", "MerchantNotSupportThirdPartPay"))
		return
	}

	// 获取支付方式
	if rechargeForm.PaymentTypeID, err = ctrl.PaymentService.GetPayTypeByAuthCode(rechargeForm.AuthCode, merchant.GetWechatPaymentType()); err != nil {
		util.ResError(c, err)
		return
	}
	rechargeForm.RechargeRequest.PaymentTypeID = rechargeForm.PaymentTypeID
	// 创建充值订单
	var rechargeOrder *model.CustomerRechargeOrderModel
	var paymentLog *model.MerchantPaymentLogModel
	err = ctrl.Trans.Exec(ctx, func(ctx context.Context) error {
		rechargeOrder, err = ctrl.RechargeHandler.CreateRechargeOrder(ctx, &rechargeForm.RechargeRequest)
		if err != nil {
			return err
		}
		paymentLog, err = ctrl.RechargeHandler.CreateRechargePaymentLog(ctx, rechargeOrder, consts.TRADE_TYPE_MICROPAY, util.GetClientIP(c.Request), merchant)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		util.ResError(c, err)
		return
	}

	var paymentResult *schema.PaymentResult
	// 根据类型调用方相应的支付接口
	switch rechargeForm.PaymentTypeID {
	case consts.PAY_TYPE_WECHAT:
		paymentResult, err = ctrl.WechatPayService.MicroPay(ctx, rechargeForm.AuthCode, paymentLog, merchant)
		if err != nil {
			util.ResError(c, err)
			return
		}
	case consts.PAY_TYPE_ALIPAY:
		paymentResult, err = ctrl.AlipayService.MicroPay(ctx, rechargeForm.AuthCode, paymentLog, merchant)
		if err != nil {
			util.ResError(c, err)
			return
		}
	default:
		util.ResError(c, errors.BadRequest("", "InvalidPayType"))
		return
	}
	// 如果支付成功，更新支付信息
	if paymentResult.Status == consts.PAY_STATUS_PAID {
		if err = ctrl.RechargeHandler.HandleOnlinePaySuccess(ctx, customer, rechargeOrder, paymentResult); err != nil {
			util.ResError(c, err)
			return
		}
		// 支付类型
		payType, err := ctrl.PaymentTypeService.GetPaymentTypeByID(ctx, rechargeForm.PaymentTypeID)
		if err != nil {
			util.ResError(c, err)
		}
		// 重新获取客户信息
		customer, err = ctrl.CustomerService.GetByID(ctx, rechargeOrder.CustomerID)
		if err != nil {
			util.ResError(c, err)
			return
		}
		paymentTypeName := payType.NameUg
		if i18n.IsZh(&ctx) {
			paymentTypeName = payType.NameZh
		}
		rechargeResult := schema.RechargeResult{
			Status:         1, // 1 充值成功
			Mobile:         customer.Mobile,
			Name:           customer.Name,
			Balance:        customer.Balance,
			RechargeAmount: util.DivideFloat(float64(rechargeOrder.RechargeAmount), 100),
			PresentAmount:  util.DivideFloat(float64(rechargeOrder.PresentAmount), 100),
			PaymentType:    paymentTypeName,
		}
		util.ResSuccess(c, rechargeResult, "PaySuccess")
		return
	}
	util.ResError(c, errors.BadRequest("", "PaymentFailed"))
}

// NativePay 微信扫码支付充值
//
// @Tags 同步接口
// @Security ApiTokenAuth
// @Summary 微信扫码支付充值
// @Param body body request.RechargeRequest true "body"
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/recharge/native-pay [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/1/17 16:05"]
// @X-Version ["2.0"]
func (ctrl *RechargeController) NativePay(c *gin.Context) {
	ctx := c.Request.Context()
	var rechargeForm request.RechargeRequest
	if err := util.ParseJSON(c, &rechargeForm); err != nil {
		util.ResError(c, err)
		return
	}
	// 设置MerchantNo
	rechargeForm.MerchantNo = util.GetMerchantNo(c)
	rechargeForm.CashierID = util.FromUserID(ctx)

	// 获取客户信息
	_, merchant, err := ctrl.getRechargerInfo(ctx, &rechargeForm)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 生成充值订单
	rechargeOrder, err := ctrl.RechargeHandler.CreateRechargeOrder(ctx, &rechargeForm)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 如果没有支付宝授权码，走微信Native支付
	if merchant.AlipayAppAuthToken == "" && merchant.SubMchID != "" {
		ctrl.wechatNativePay(c, rechargeOrder, merchant)
		return
	}

	// 生成二维码内容
	payUrl, err := ctrl.PaymentService.GetRechargeQrcodeUrl(rechargeOrder)
	if err != nil {
		util.ResError(c, err)
		return
	}
	res := map[string]string{
		"payment_no": rechargeOrder.No,
		"pay_url":    payUrl,
	}
	util.ResSuccess(c, res, "GetSuccess")

}

func (ctrl *RechargeController) wechatNativePay(
	c *gin.Context,
	rechargeOrder *model.CustomerRechargeOrderModel,
	merchant *model.MerchantModel) {
	ctx := c.Request.Context()
	// 创建支付信息
	// 创建充值订单
	paymentLog, err := ctrl.RechargeHandler.CreateRechargePaymentLog(ctx, rechargeOrder, consts.TRADE_TYPE_NATIVE, util.GetClientIP(c.Request), merchant)
	if err != nil {
		util.ResError(c, err)
		return
	}

	payUrl, err := ctrl.WechatPayService.NativePay(ctx, paymentLog, merchant)
	if err != nil {
		util.ResError(c, err)
		return
	}
	res := map[string]string{
		"payment_no": paymentLog.PaymentNo,
		"pay_url":    payUrl,
	}
	util.ResSuccess(c, res, "GetSuccess")
}

// PaymentQuery 支付结果查询
//
// @Tags VIP充值相关接口
// @Security ApiTokenAuth
// @Summary 支付结果查询
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/recharge/query/{paymentNo} [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/1/17 17:52"]
// @X-Version ["2.0"]
func (ctrl *RechargeController) PaymentQuery(c *gin.Context) {
	ctx := c.Request.Context()
	paymentNo := c.Param("paymentNo")
	// 获取MerchantNO
	merchantNo := c.Request.Header.Get("Merchantno")

	order, err := ctrl.RechargeOrderService.GetByOrderNo(ctx, merchantNo, paymentNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if order == nil {
		util.ResError(c, errors.BadRequest("", "OrderNotFound"))
		return
	}

	rechargeResult := schema.RechargeResult{}

	if order.Status == model.RechargeOrderStatusSuccess {

		payType, err := ctrl.PaymentTypeService.GetPaymentTypeByID(ctx, order.PaymentTypeID)
		if err != nil {
			util.ResError(c, err)
			return
		}
		paymentTypeName := payType.NameUg
		if i18n.IsZh(&ctx) {
			paymentTypeName = payType.NameZh
		}

		rechargeResult.Status = 1
		rechargeResult.Balance = order.Customer.Balance
		rechargeResult.RechargeAmount = util.DivideFloat(float64(order.RechargeAmount), 100)
		rechargeResult.PresentAmount = util.DivideFloat(float64(order.PresentAmount), 100)
		rechargeResult.Name = order.Customer.Name
		rechargeResult.Mobile = order.Customer.Mobile
		rechargeResult.PaymentType = paymentTypeName
	}

	util.ResSuccess(c, rechargeResult, "PaymentQuerySuccess")
}

// 获取充值者信息
func (ctrl *RechargeController) getRechargerInfo(ctx context.Context, formItem *request.RechargeRequest) (*model.CustomerModel, *model.MerchantModel, error) {

	// 获取商户信息
	var merchant *model.MerchantModel
	var err error
	if merchant, err = ctrl.MerchantService.GetByMerchantNo(ctx, formItem.MerchantNo); err != nil {
		return nil, nil, err
	}

	if merchant == nil {
		return nil, nil, errors.BadRequest("", "MerchantNotFound")
	}
	// 如果未开通微信特约商户
	if merchant.SubMchID == "" {
		return nil, nil, errors.BadRequest("", "MerchantNotSupportWechatPay")
	}

	if merchant.State != model.MerchantStateActive {
		return nil, nil, errors.BadRequest("", "MerchantNotActive")
	}

	// 获取客户信息
	var customer *model.CustomerModel
	if customer, err = ctrl.CustomerService.GetByID(ctx, formItem.CustomerID); err != nil {
		return nil, nil, err
	}

	if customer.MerchantNo != formItem.MerchantNo {
		return nil, nil, errors.BadRequest("", "CustomerNotFound")
	}
	return customer, merchant, nil
}

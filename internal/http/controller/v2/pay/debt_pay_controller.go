package pay

import (
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type DebtPayController struct {
	DebtHolderService *service.DebtHolderService
	MerchantService   *service.MerchantService
	PaymentService    *service.PaymentService // 支付服务
}

// Pay 赊账支付
// @Summary 赊账支付
// @Description 赊账支付
// @Tags 支付相关接口
// @Accept json
// @Produce json
// @Security ServerTokenAuth
// @Param body body request.DebtPaymentForm true "支付请求"
// @Success 200 {object} util.ResponseResult "支付响应"
// @Failure 400 {object} util.ResponseResult "错误响应"
// @Failure 422 {object} util.ResponseResult "错误响应"
// @Router /api/v2/payment/debt-pay [post]
// @X-Author ["Merdan"]
// @X-Date ["2025-05-14 10:00:00"]
// @X-Version ["2.0"]
func (ctrl *DebtPayController) Pay(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)
	var formItem request.DebtPaymentForm

	// 解析请求
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	// 获取会员信息
	holder, err := ctrl.DebtHolderService.GetByID(ctx, merchantNo, formItem.HolderID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 判断商户号是否存在
	if holder == nil {
		util.ResError(c, errors.BadRequest("1005", "DebtHolderNotFound"))
		return
	}

	if holder.Status != model.DebtHolderStateEnabled {
		util.ResError(c, errors.BadRequest("", "DebtHolderNotFound"))
		return
	}

	// 判断会员余额是否足够
	if holder.CreditLimit*100-holder.Balance < formItem.Amount {
		util.ResError(c, errors.BadRequest("", "DebtHolderBalanceNotEnough"))
		return
	}
	formItem.MerchantNo = merchantNo
	formItem.PaymentTypeID = consts.PAY_TYPE_DEBT
	// 这里公用 customer_id 字段
	formItem.MerchantPaymentForm.CustomerID = formItem.HolderID
	// 创建支付记录
	paymentLog, err := ctrl.PaymentService.CreateMerchantPayment(ctx, &formItem.MerchantPaymentForm)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if ctrl.DebtHolderService.Pay(ctx, holder, paymentLog, formItem.CashierID) != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, "success")
}

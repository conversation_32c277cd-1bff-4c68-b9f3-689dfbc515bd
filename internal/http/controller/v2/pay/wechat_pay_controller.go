package pay

import (
	"context"
	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/wechat/v3"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/internal/service/scan_service"
	"ros-api-go/pkg/cachex"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"ros-api-go/pkg/wechatx"
	"time"
)

type WechatPayController struct {
	MerchantService           *service.MerchantService
	PaymentService            *service.PaymentService
	PaymentLogService         *service.PaymentLogService
	RefundLogService          *service.RefundLogService
	WechatPayService          *service.WechatPayService
	DebtRepaymentHandler      *handler.DebtRepaymentHandler
	DebtRepaymentOrderService *service.DebtRepaymentOrderService
	CustomerService           *service.CustomerService
	DebtHolderService         *service.DebtHolderService
	ScanOrderService          *scan_service.ScanOrderService
	OrderCloudService         *service.OrderCloudService
	RechargeOrderService      *service.RechargeOrderService
	PaymentHandler            *handler.PaymentHandler
	RefundHandler             *handler.RefundHandler
	RechargeHandler           *handler.RechargeHandler
	Cache                     cachex.Cacher
	Trans                     *util.Trans
}

// JsAPIPay 微信JSAPI支付
//
// @Tags 支付相关接口
// @Security ServerTokenAuth
// @Summary 微信JSAPI支付(小程序)
// @Success 200 {object} util.ResponseResult{data=wechat.JSAPIPayParams}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/wechat/jsapi/{merchantNo}/{paymentNo} [post]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/9 17:21"]
// @X-Version ["2.0"]
func (ctrl *WechatPayController) JsAPIPay(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取参数(authCode, merchantNo, paymentNo)
	merchantNo := c.Param("merchantNo")
	paymentNo := c.Param("paymentNo")
	var formItem request.JsAPIPaymentForm
	err := util.ParseJSON(c, &formItem)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 根据code获取openid
	openId, err := wechatx.CodeToOpenId(ctx, formItem.AuthCode)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 获取商户信息
	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if merchant == nil {
		util.ResError(c, errors.BadRequest("", "MerchantNotFound"))
		return
	}

	if merchant.State != model.MerchantStateActive {
		util.ResError(c, errors.BadRequest("", "MerchantNotActive"))
		return
	}

	// 如果未开通微信特约商户
	if merchant.SubMchID == "" {
		util.ResError(c, errors.BadRequest("", "PleaseUseAlipay"))
		return
	}

	var paymentInfo *model.MerchantPaymentLogModel
	switch formItem.OrderType {
	case consts.ORDER_TYPE_ORDER:
		paymentInfo, err = ctrl.getPaymentInfoForOrder(ctx, paymentNo, merchant, util.GetClientIP(c.Request))
	case consts.ORDER_TYPE_RECHARGE:
		paymentInfo, err = ctrl.getPaymentInfoForRecharge(ctx, paymentNo, merchant, util.GetClientIP(c.Request))
	case consts.ORDER_TYPE_DEBT_REPAYMENT:
		paymentInfo, err = ctrl.getPaymentInfoForDebtRepayment(ctx, paymentNo, merchant, util.GetClientIP(c.Request))
	}

	if err != nil {
		util.ResError(c, err)
		return
	}

	if paymentInfo == nil {
		util.ResError(c, errors.BadRequest("", "PaymentNotFound"))
		return
	}

	if paymentInfo.ExpiresAt.Before(time.Now()) {
		util.ResError(c, errors.BadRequest("", "PaymentExpired"))
		return
	}

	config, err := ctrl.generateJsApiConfig(c, paymentInfo, merchant, openId)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, config, "Success")
}

// getPaymentInfoForOrder 获取订单支付信息
func (ctrl *WechatPayController) getPaymentInfoForOrder(ctx context.Context, paymentNo string, merchant *model.MerchantModel, clientIP string) (*model.MerchantPaymentLogModel, error) {
	// 先查询支付记录
	paymentInfo, err := ctrl.PaymentService.GetByPaymentNo(ctx, paymentNo, merchant.No)
	if err != nil {
		return nil, err
	}

	// 如果使用支付宝扫过,则修改支付方式为微信支付
	if paymentInfo != nil && paymentInfo.PaymentTypeID != consts.PAY_TYPE_WECHAT {
		if err = ctrl.PaymentService.SetPaymentType(ctx, paymentNo, consts.PAY_TYPE_WECHAT); err == nil {
			paymentInfo.PaymentTypeID = consts.PAY_TYPE_WECHAT
		} else {
			return nil, err
		}
	}

	paymentForm := new(request.MerchantPaymentForm)
	// 如果不存在就创建支付记录
	if paymentInfo == nil {
		// 获取支付缓存信息
		paymentCache, _, err := ctrl.Cache.Get(ctx, consts.CacheNSForPayment, paymentNo)

		if err != nil || paymentCache == "" {
			return nil, errors.BadRequest("1001", "PaymentExpired")
		}

		if err = json.Unmarshal([]byte(paymentCache), &paymentForm); err != nil {
			return nil, err
		}

		// 设置支付方式为微信支付
		paymentForm.PaymentTypeID = consts.PAY_TYPE_WECHAT

		// 创建支付信息
		paymentInfo, err = ctrl.PaymentService.CreateMerchantPayment(ctx, paymentForm)
		if err != nil {
			return nil, err
		}
	}

	if paymentInfo.Status == consts.PAY_STATUS_PAID {
		return nil, errors.BadRequest("", "PaymentAlreadyPaid")
	}

	// 创建支付记录
	paymentLog, err := ctrl.PaymentLogService.CreatePaymentLog(ctx, merchant, paymentInfo, consts.TRADE_TYPE_JSAPI, consts.ORDER_TYPE_ORDER, clientIP)
	if err != nil {
		return nil, err
	}

	return paymentLog, nil
}

// getPaymentInfoForRecharge 获取充值支付信息
func (ctrl *WechatPayController) getPaymentInfoForRecharge(ctx context.Context, paymentNo string, merchant *model.MerchantModel, clientIP string) (*model.MerchantPaymentLogModel, error) {

	rechargeOrder, err := ctrl.RechargeOrderService.GetByOrderNo(ctx, merchant.No, paymentNo)
	if err != nil {
		return nil, err
	}
	if rechargeOrder == nil {
		return nil, errors.BadRequest("", "OrderNotFound")
	}

	if rechargeOrder.Status == model.RechargeOrderStatusSuccess {
		return nil, errors.BadRequest("", "OrderAlreadyPaid")
	}

	// 设置支付方式为微信支付
	rechargeOrder.PaymentTypeID = consts.PAY_TYPE_WECHAT

	paymentLog, err := ctrl.RechargeHandler.CreateRechargePaymentLog(ctx, rechargeOrder, consts.TRADE_TYPE_JSAPI, clientIP, merchant)

	return paymentLog, err
}

// getPaymentInfoForDebtRepayment 获取赊账还款支付信息
func (ctrl *WechatPayController) getPaymentInfoForDebtRepayment(ctx context.Context, paymentNo string, merchant *model.MerchantModel, clientIP string) (*model.MerchantPaymentLogModel, error) {

	repaymentOrder, err := ctrl.DebtRepaymentOrderService.GetByOrderNo(ctx, merchant.No, paymentNo)

	// 设置支付方式为微信支付
	repaymentOrder.PaymentTypeID = consts.PAY_TYPE_WECHAT

	paymentLog, err := ctrl.DebtRepaymentHandler.CreateDebtRepaymentPaymentLog(ctx, repaymentOrder, consts.TRADE_TYPE_JSAPI, clientIP, merchant)

	return paymentLog, err
}

// 获取支付记录

// generateJsApiConfig 生成微信JSAPI支付参数
func (ctrl *WechatPayController) generateJsApiConfig(c *gin.Context, paymentLog *model.MerchantPaymentLogModel, merchant *model.MerchantModel, openId string) (*wechat.JSAPIPayParams, error) {
	ctx := c.Request.Context()
	jsAPIPay, err := ctrl.WechatPayService.JsAPIPay(ctx, openId, paymentLog, merchant)
	if err != nil {
		return nil, err
	}
	return jsAPIPay, nil
}

// HandlePaymentNotify 处理微信支付通知
//
// @Tags 支付相关接口
// @Security ServerTokenAuth
// @Summary 处理微信支付通知
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/wechat/payment-notify/{paymentNo} [post]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/23 16:30"]
// @X-Version ["2.0"]
func (ctrl *WechatPayController) HandlePaymentNotify(c *gin.Context) {
	ctx := c.Request.Context()

	// 解析微信支付通知
	paymentResult, err := ctrl.WechatPayService.ParsePaymentNotify(ctx, c.Request)
	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "解析微信支付通知失败"})
		return
	}

	// 获取支付记录
	paymentLog, err := ctrl.PaymentLogService.GetByOutTradeNo(ctx, paymentResult.OutTradeNo, paymentResult.MerchantNo)

	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "支付信息查询失败"})
		return
	} else if paymentLog == nil { // 支付记录不存在, 无需处理
		return
	}

	if paymentLog.Status == consts.PAY_STATUS_PAID { // 支付记录已处理, 无需处理
		c.Status(200)
		return
	}

	// 更新支付记录
	if paymentResult.Status == consts.PAY_STATUS_PAID {

		err = ctrl.Trans.Exec(ctx, func(ctx context.Context) error {
			// 更新支付信息
			if paymentLog, err = ctrl.PaymentLogService.UpdatePaymentStatus(ctx, paymentResult); err != nil {
				return err
			}

			if _, err := ctrl.PaymentService.UpdatePaymentStatus(ctx, paymentResult); err != nil {
				return err
			}
			// 处理云端扫码点菜部分
			if err := ctrl.PaymentHandler.HandleScanOrder(ctx, paymentLog, paymentResult); err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			util.ResError(c, err)
			return
		}
	}
	// 删除支付缓存
	ctrl.Cache.Delete(ctx, consts.CacheNSForPayment, paymentResult.PaymentNo)
	// 处理重复支付
	ctrl.RefundHandler.HandleRepeatedPayment(ctx, paymentLog, paymentResult)

	c.Status(200)
}

// HandleRefundNotify 处理微信退款通知
//
// @Tags 支付相关接口
// @Summary 处理微信退款通知
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/wechat/refund-notify/{paymentNo} [post]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/9 17:22"]
// @X-Version ["2.0"]
func (ctrl *WechatPayController) HandleRefundNotify(c *gin.Context) {
	ctx := c.Request.Context()

	// 解析微信支付通知
	refundResult, err := ctrl.WechatPayService.ParseRefundNotify(ctx, c.Request)
	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "解析微信支付通知失败"})
		return
	}

	// 获取支付记录
	paymentLog, err := ctrl.RefundLogService.GetByOutRefundNo(ctx, refundResult.OutRefundNo)

	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "支付信息查询失败"})
		return
	} else if paymentLog == nil { // 支付记录不存在, 无需处理
		c.Status(200)
		return
	}

	// 退款记录已处理, 无需处理
	if paymentLog.RefundStatus == consts.REFUND_STATUS_SUCCESS {
		c.Status(200)
		return
	}

	// 更新退款记录
	_, err = ctrl.RefundLogService.UpdateRefundStatus(ctx, refundResult)
	if err != nil {
		util.ResError(c, err)
		return
	}

	c.Status(200)
}

// HandleRechargeNotify 处理VIP充值通知
//
// @Tags 支付相关接口
// @Security ServerTokenAuth
// @Summary 处理VIP充值通知
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/wechat/recharge-notify/{paymentNo} [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/1/16 17:50"]
// @X-Version ["2.0"]
func (ctrl *WechatPayController) HandleRechargeNotify(c *gin.Context) {
	ctx := c.Request.Context()

	// 解析微信支付通知
	paymentResult, err := ctrl.WechatPayService.ParsePaymentNotify(ctx, c.Request)
	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "解析微信支付通知失败"})
		return
	}

	// 获取支付记录
	paymentLog, err := ctrl.PaymentLogService.GetByOutTradeNo(ctx, paymentResult.OutTradeNo, paymentResult.MerchantNo)

	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "支付信息查询失败"})
		return
	} else if paymentLog == nil { // 支付记录不存在, 无需处理
		return
	}

	if paymentLog.Status == consts.PAY_STATUS_PAID { // 支付记录已处理, 无需处理
		c.Status(200)
		return
	}

	// 更新支付记录
	if paymentResult.Status == consts.PAY_STATUS_PAID {
		order, err := ctrl.CustomerService.GetRechargeOrderByOrderNo(ctx, paymentLog.MerchantNo, paymentLog.OrderNo)
		if err != nil {
			util.ResError(c, err)
		}
		err = ctrl.RechargeHandler.HandleOnlinePaySuccess(ctx, order.Customer, order, paymentResult)
		if err != nil {
			util.ResError(c, err)
		}
	}

	// 删除支付缓存
	ctrl.Cache.Delete(ctx, consts.CacheNSForPayment, paymentResult.PaymentNo)

	c.Status(200)
}

// HandleDebtRepaymentNotify 处理赊账还款结果
//
// @Tags 支付相关接口
// @Security ServerTokenAuth
// @Summary 处理赊账还款结果
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/wechat/debt-repayment-notify/{paymentNo} [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/14 17:05"]
// @X-Version ["2.0"]
func (ctrl *WechatPayController) HandleDebtRepaymentNotify(c *gin.Context) {
	ctx := c.Request.Context()

	// 解析微信支付通知
	paymentResult, err := ctrl.WechatPayService.ParsePaymentNotify(ctx, c.Request)
	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "解析微信支付通知失败"})
		return
	}

	// 获取支付记录
	paymentLog, err := ctrl.PaymentLogService.GetByOutTradeNo(ctx, paymentResult.OutTradeNo, paymentResult.MerchantNo)

	if err != nil {
		util.ResJSON(c, 400, gin.H{"code": 400, "message": "支付信息查询失败"})
		return
	} else if paymentLog == nil { // 支付记录不存在, 无需处理
		return
	}

	if paymentLog.Status == consts.PAY_STATUS_PAID { // 支付记录已处理, 无需处理
		c.Status(200)
		return
	}

	// 更新支付记录
	if paymentResult.Status == consts.PAY_STATUS_PAID {
		order, err := ctrl.DebtHolderService.GetRepaymentOrderByPaymentNo(ctx, paymentResult.PaymentNo)
		if err != nil {
			util.ResError(c, err)
		}
		err = ctrl.DebtRepaymentHandler.HandleOnlinePaySuccess(ctx, order.Holder, order, paymentResult)
		if err != nil {
			util.ResError(c, err)
		}
	}

	// 删除支付缓存
	ctrl.Cache.Delete(ctx, consts.CacheNSForPayment, paymentResult.PaymentNo)

	c.Status(200)
}

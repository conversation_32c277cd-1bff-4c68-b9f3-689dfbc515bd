package local

import (
	"encoding/json"
	"ros-api-go/internal/http/request/local/print_request"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

type PrinterController struct {
	Client *redis.Client
}

type PrintData struct {
	Action     string `json:"action"`
	MerchantNo string `json:"merchant_no"`
	DeviceID   string `json:"device_id"`
	Content    string `json:"content"`
}

type PrintCmd struct {
	Data PrintData `json:"data"`
}

// Print 打印
// @Summary 打印
// @Description 打印
// @Tags 打印
// @Security ServerTokenAuth
// @Accept json
// @Produce json
// @Param data body print_request.PrintRequest true "打印数据"
// @Success 200 {object} util.ResponseResult{message=string}
// @Router /printer/print [post]
// @X-Author {"name": "<PERSON><PERSON>"}
// @X-Date ["2025-01-14 12:50:39"]
// @X-Version ["2.0"]
func (ctrl *PrinterController) Print(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")
	var req print_request.PrintRequest
	if err := util.ParseJSON(c, &req); err != nil {
		logging.Context(ctx).Error("打印数据解析失败",
			zap.String("merchant_no", merchantNo),
			zap.String("device_id", req.DeviceID),
			zap.String("content", req.Content),
			zap.Error(err))
		util.ResError(c, errors.New("打印数据解析失败", "打印数据解析失败", 400))
	}

	printData := PrintData{
		Action:     "打印指令",
		MerchantNo: merchantNo,
		DeviceID:   req.DeviceID,
		Content:    req.Content,
	}

	var printCmd = PrintCmd{
		Data: printData,
	}
	var cmdStr []byte
	var err error
	cmdStr, err = json.Marshal(printCmd)
	if err != nil {
		logging.Context(ctx).Error("打印数据序列化失败", zap.String("merchant_no", merchantNo),
			zap.String("device_id", req.DeviceID),
			zap.String("content", req.Content),
			zap.Error(err))
		util.ResError(c, errors.New("打印数据序列化失败", "打印数据序列化失败", 500))
	}
	ctrl.Client.Publish(ctx, "print", cmdStr)
	util.ResSuccess(c, nil)
}

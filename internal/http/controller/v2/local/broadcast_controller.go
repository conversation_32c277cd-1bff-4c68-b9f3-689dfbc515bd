package local

import (
	"github.com/gin-gonic/gin"
	"log"
	"ros-api-go/pkg/util"
	"ros-api-go/pkg/websocket"
)

type BroadcastController struct {
}

type BroadcastData struct {
	ID        string      `json:"id" binding:"required"`        // 唯一ID
	Action    string      `json:"action" binding:"required"`    // 操作类型
	TableID   int         `json:"table_id"`                     // 餐桌ID
	OrderID   int         `json:"order_id"`                     // 订单ID
	Data      interface{} `json:"data"`                         // 其他数据
	Timestamp int64       `json:"timestamp" binding:"required"` // 时间戳
}

func (ctrl *BroadcastController) PostBroadcast(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)
	var message BroadcastData
	err := util.ParseJSON(c, &message)
	if err != nil {
		util.ResError(c, err)
		return
	}
	log.Printf("Broadcast: %v \n", message)

	websocket.Broadcast(merchantNo, message)
	util.ResOK(c)
}

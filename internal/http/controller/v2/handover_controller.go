package v2

import (
	"context"
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/resource/handover_resource"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/collect"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"
)

type HandoverController struct {
	HandoverService           *service.HandoverService
	OrderService              *service.OrderService
	PaymentService            *service.PaymentService
	PaymentTypeService        *service.PaymentTypeService
	UserService               *service.UserService
	EmployeeService           *service.MerchantEmployeeService
	RechargeOrderService      *service.RechargeOrderService
	CustomerService           *service.CustomerService
	ShiftService              *service.ShiftService
	OrderCloudService         *service.OrderCloudService
	DebtHolderService         *service.DebtHolderService
	DebtRepaymentOrderService *service.DebtRepaymentOrderService
	Trans                     *util.Trans
}

// Open 开班
//
// @Tags 交接班接口
// @Security ApiTokenAuth
// @Summary 开班
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/handover/open [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/1/22 17:06"]
// @X-Version ["2.0"]
func (ctrl *HandoverController) Open(c *gin.Context) {
	ctx := c.Request.Context()

	var openForm request.HandoverOpenForm

	if err := util.ParseJSON(c, &openForm); err != nil {
		util.ResError(c, err)
		return
	}
	openForm.UserID = util.FromUserID(ctx)
	openForm.MerchantNo = c.Request.Header.Get("Merchantno")

	isHandover, err := ctrl.HandoverService.HasActiveHandover(ctx, openForm.UserID, openForm.MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 如果有未交班的记录，则不能开通
	if isHandover {
		util.ResError(c, errors.Conflict("", "HasNoHandoverShift"))
		return
	}

	shift, err := ctrl.ShiftService.GetByID(ctx, openForm.Shift)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if shift == nil {
		util.ResError(c, errors.BadRequest("", "ShiftNotExist"))
		return
	}

	if shift.State != model.ShiftStateOpen {
		util.ResError(c, errors.BadRequest("", "ShiftNotOpen"))
		return
	}

	_, err = ctrl.HandoverService.Open(ctx, openForm)

	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
}

// HandoverInfo 获取交接班详情
//
// @Tags 交接班接口
// @Security ApiTokenAuth
// @Summary 获取交接班详情
// @Success 200 {object} util.ResponseResult{data=handover_resource.HandoverResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/handover/info [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/1/22 17:10"]
// @X-Version ["2.0"]
func (ctrl *HandoverController) HandoverInfo(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")
	userId := util.FromUserID(ctx)

	cashier, err := ctrl.EmployeeService.GetByUserID(ctx, merchantNo, userId)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if cashier == nil {
		util.ResError(c, errors.BadRequest("", "UserNotExist"))
		return
	}

	// 获取可交班记录
	handoverLog, err := ctrl.HandoverService.GetAvailableHandoverLog(ctx, userId, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if handoverLog == nil {
		util.ResError(c, errors.BadRequest("", "NoHandoverShift"))
		return
	}

	endAt := time.Now()

	overviewFromOrder, err := ctrl.OrderService.GetHandoverData(ctx, merchantNo, cashier, handoverLog.StartAt, endAt)
	if err != nil {
		util.ResError(c, err)
		return
	}
	details, err := ctrl.createHandOverDetailData(ctx, handoverLog, endAt)
	if err != nil {
		util.ResError(c, err)
		return
	}

	paymentDetails, totals, err := ctrl.getHandoverDetails(ctx, handoverLog, details)
	if err != nil {
		util.ResError(c, err)
		return
	}

	newCustomersCount, err := ctrl.CustomerService.GetHandoverNewCustomer(ctx, handoverLog, endAt)
	if err != nil {
		util.ResError(c, err)
		return
	}

	newHolderCount, err := ctrl.DebtHolderService.GetHandoverNewHoldersCount(ctx, handoverLog, endAt)
	if err != nil {
		util.ResError(c, err)
		return
	}

	businessSituation := &handover_resource.BusinessSituationResource{
		OrderCount:     overviewFromOrder.OrderCount,
		CustomerCount:  overviewFromOrder.CustomerCount,
		RealPaidAmount: overviewFromOrder.PaidAmount,
		TotalDiscount:  util.Round(util.SubtractFloat(overviewFromOrder.ReceivableAmount, overviewFromOrder.PaidAmount)),
	}
	if overviewFromOrder.CustomerCount > 0 {
		businessSituation.CustomerAvg = util.Round(util.DivideFloat(overviewFromOrder.PaidAmount, float64(overviewFromOrder.CustomerCount)))
	}
	if overviewFromOrder.OrderCount > 0 {
		businessSituation.OrderAvg = util.Round(util.DivideFloat(overviewFromOrder.PaidAmount, float64(overviewFromOrder.OrderCount)))
	}

	overviewFromOrder.ReceivableAmount = util.Round(util.SubtractFloat(util.AddFloatMore(totals.OrderCashAmount, totals.VipCashRechargeAmount, totals.DebtRepaymentCashAmount), totals.RefundCashAmount))
	overviewFromOrder.PaidAmount = util.Round(util.AddFloatMore(overviewFromOrder.ReceivableAmount, handoverLog.WorkingBalance, handoverLog.AlternateAmount))
	overview := &schema.HandoverOverview{
		HandoverOverviewFromOrder: *overviewFromOrder,
		WorkingBalance:            handoverLog.WorkingBalance,
		AlternateAmount:           handoverLog.AlternateAmount,
	}

	util.ResSuccess(c, handover_resource.HandoverResource{
		BusinessSituation: businessSituation,
		Overview:          overview,
		CashInfo:          paymentDetails,
		Vip: &handover_resource.VipResource{
			VipCount:              newCustomersCount,
			RechargeAmount:        totals.VipRechargeAmount,
			PresentRechargeAmount: totals.PresentRechargeAmount,
		},
		Debt: &handover_resource.DebtHolderResource{
			HolderCount:     newHolderCount,
			RepaymentAmount: totals.DebtRepaymentAmount,
		},
	})

}

// HandoverFromCashier 交班
//
// @Tags 交接班接口
// @Security ServerTokenAuth
// @Summary 交班
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/handover/handover [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/1/22 17:28"]
// @X-Version ["2.0"]
func (ctrl *HandoverController) HandoverFromCashier(c *gin.Context) {
	ctx := c.Request.Context()

	var handoverForm request.HandoverForm
	if err := util.ParseJSON(c, &handoverForm); err != nil {
		util.ResError(c, err)
		return
	}

	handoverForm.MerchantNo = util.FromServerMerchantNo(ctx)

	cashier, err := ctrl.EmployeeService.GetByUserID(ctx, handoverForm.MerchantNo, handoverForm.UserID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if cashier == nil {
		util.ResError(c, errors.BadRequest("", "UserNotExist"))
		return
	}

	//
	handoverLog, err := ctrl.HandoverService.GetAvailableHandoverLog(ctx, handoverForm.UserID, handoverForm.MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if handoverLog == nil {
		util.ResError(c, errors.BadRequest("", "NoHandoverShift"))
		return
	}

	endAt := time.Now()

	overviewFromOrder, err := ctrl.OrderService.GetHandoverData(ctx, handoverForm.MerchantNo, cashier, handoverLog.StartAt, endAt)
	if err != nil {
		util.ResError(c, err)
		return
	}

	details, err := ctrl.createHandOverDetailData(ctx, handoverLog, endAt)
	if err != nil {
		util.ResError(c, err)
		return
	}

	_, totals, err := ctrl.getHandoverDetails(ctx, handoverLog, details)
	if err != nil {
		util.ResError(c, err)
		return
	}

	overviewFromOrder.ReceivableAmount = util.Round(util.SubtractFloat(util.AddFloat(totals.OrderCashAmount, totals.VipRechargeAmount), totals.RefundCashAmount))
	overviewFromOrder.PaidAmount = overviewFromOrder.ReceivableAmount
	err = ctrl.HandoverService.Handover(ctx, handoverLog, overviewFromOrder)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResOK(c)
}

// HandoverFromWeb 交班
//
// @Tags 同步接口
// @Security ApiTokenAuth
// @Summary
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /cloud/api/v2/handover/handover [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/16 16:42"]
// @X-Version ["2.0"]
func (ctrl *HandoverController) HandoverFromWeb(c *gin.Context) {
	ctx := c.Request.Context()

	handoverForm := request.HandoverForm{
		MerchantNo: c.Request.Header.Get("Merchantno"),
		UserID:     util.FromUserID(ctx),
	}

	cashier, err := ctrl.EmployeeService.GetByUserID(ctx, handoverForm.MerchantNo, handoverForm.UserID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if cashier == nil {
		util.ResError(c, errors.BadRequest("", "UserNotExist"))
		return
	}

	notSyncOrderCount, err := ctrl.OrderCloudService.NotSyncOrdersCount(ctx, handoverForm.MerchantNo, handoverForm.UserID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 如果还有未同步的订单，则不能交班
	if notSyncOrderCount > 0 {
		util.ResError(c, errors.BadRequest("", "HasNotSyncedOrders"))
		return
	}

	// 获取交班记录
	handoverLog, err := ctrl.HandoverService.GetAvailableHandoverLog(ctx, handoverForm.UserID, handoverForm.MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if handoverLog == nil {
		util.ResError(c, errors.BadRequest("", "NoHandoverShift"))
		return
	}

	endAt := time.Now()

	overviewFromOrder, err := ctrl.OrderService.GetHandoverData(ctx, handoverForm.MerchantNo, cashier, handoverLog.StartAt, endAt)
	if err != nil {
		util.ResError(c, err)
		return
	}
	details, err := ctrl.createHandOverDetailData(ctx, handoverLog, endAt)
	if err != nil {
		util.ResError(c, err)
		return
	}

	_, totals, err := ctrl.getHandoverDetails(ctx, handoverLog, details)
	if err != nil {
		util.ResError(c, err)
		return
	}

	overviewFromOrder.ReceivableAmount = util.Round(util.SubtractFloat(util.AddFloat(totals.OrderCashAmount, totals.VipRechargeAmount), totals.RefundCashAmount))
	overviewFromOrder.PaidAmount = overviewFromOrder.ReceivableAmount
	err = ctrl.HandoverService.Handover(ctx, handoverLog, overviewFromOrder)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResOK(c)
}

// 生成交班详情
func (ctrl *HandoverController) createHandOverDetailData(ctx context.Context, handoverLog *model.HandoverLogModel, endAt time.Time) ([]*model.HandoverDetailModel, error) {
	startAt := handoverLog.StartAt
	paymentTypes, err := ctrl.PaymentTypeService.GetMerchantPaymentTypes(ctx, handoverLog.MerchantNo)
	if err != nil {
		return nil, err
	}

	payments, err := ctrl.PaymentService.ListForHandoverDetail(ctx, handoverLog.UserID, handoverLog.MerchantNo, startAt, endAt)
	if err != nil {
		return nil, err
	}

	paymentCollect := collect.ToMap(payments, func(item *schema.CashierPaymentStatistics) int64 {
		return item.PaymentTypeID
	})

	recharges, err := ctrl.RechargeOrderService.ListForHandoverDetail(ctx, handoverLog.UserID, handoverLog.MerchantNo, startAt, endAt)
	if err != nil {
		return nil, err
	}
	rechargeCollect := collect.ToMap(recharges, func(item *schema.CashierRechargeStatistics) int64 {
		return item.PaymentTypeID
	})

	debtRepayments, err := ctrl.DebtRepaymentOrderService.ListForHandoverDetail(ctx, handoverLog.UserID, handoverLog.MerchantNo, startAt, endAt)
	if err != nil {
		return nil, err
	}
	debtRepaymentCollect := collect.ToMap(debtRepayments, func(item *schema.CashierDebtStatistics) int64 {
		return item.PaymentTypeID
	})

	handoverDetailData := make([]*model.HandoverDetailModel, 0)

	for _, paymentType := range paymentTypes {
		handoverDetail := &model.HandoverDetailModel{
			MerchantNo:    handoverLog.MerchantNo,
			UserID:        handoverLog.UserID,
			HandoverLogID: handoverLog.ID,
			PaymentTypeID: paymentType.ID,
		}
		if payment, ok := paymentCollect[paymentType.ID]; ok {
			handoverDetail.OrderAmount = util.DivideFloat(float64(payment.OrderAmount), 100)
			handoverDetail.OrderCount = payment.OrderCount
			handoverDetail.RefundAmount = util.DivideFloat(float64(payment.RefundAmount), 100)
		}
		if recharge, ok := rechargeCollect[paymentType.ID]; ok {
			handoverDetail.VipRechargeAmount = util.DivideFloat(float64(recharge.RechargeAmount), 100)
			handoverDetail.PresentRechargeAmount = util.DivideFloat(float64(recharge.PresentAmount), 100)
		}
		if repayment, ok := debtRepaymentCollect[paymentType.ID]; ok {
			handoverDetail.DebtRepaymentAmount = util.DivideFloat(float64(repayment.Amount), 100)
		}

		// 如果是有订单，则创建交班明细
		if handoverDetail.OrderCount > 0 || handoverDetail.VipRechargeAmount > 0 || handoverDetail.RefundAmount > 0 {
			handoverDetailData = append(handoverDetailData, handoverDetail)
		}
	}

	err = ctrl.Trans.Exec(ctx, func(ctx context.Context) error {
		if err = ctrl.HandoverService.ClearHandoverDetail(ctx, handoverLog.ID); err != nil {
			return err
		}
		if len(handoverDetailData) > 0 {
			if err = ctrl.HandoverService.CreateHandoverDetail(ctx, handoverDetailData); err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	return ctrl.HandoverService.GetHandoverDetails(ctx, handoverLog.ID)
}

// 格式化获取交班记录(包括合计)
func (ctrl *HandoverController) getHandoverDetails(ctx context.Context, handoverLog *model.HandoverLogModel, details []*model.HandoverDetailModel) ([]*handover_resource.HandoverDetailResource, *model.HandoverDetailModel, error) {

	totalAmount := 0.00        // 订单总金额
	totalCount := 0            // 订单总数
	totalRefund := 0.00        // 订单总退款金额
	totalVipRecharge := 0.00   // 会员充值总金额
	totalPresentAmount := 0.00 // 赠送充值总金额
	totalDebtRepayment := 0.00 // 赊账还款总金额

	totalCashAmount := 0.00        // 现金支付总金额
	totalCashRefund := 0.00        // 现金退款总金额
	totalCashVipRecharge := 0.00   // 会员充值总金额
	totalCashDebtRepayment := 0.00 // 赊账还款总金额
	for _, detail := range details {
		totalAmount = util.AddFloat(totalAmount, detail.OrderAmount)
		totalCount += detail.OrderCount
		totalRefund = util.AddFloat(totalRefund, detail.RefundAmount)
		totalVipRecharge = util.AddFloat(totalVipRecharge, detail.VipRechargeAmount)
		totalPresentAmount = util.AddFloat(totalPresentAmount, detail.PresentRechargeAmount)
		totalDebtRepayment = util.AddFloat(totalDebtRepayment, detail.DebtRepaymentAmount)
		if detail.PaymentTypeID == consts.PAY_TYPE_CASH {
			totalCashAmount = util.AddFloat(totalCashAmount, detail.OrderAmount)
			totalCashVipRecharge = util.AddFloat(totalCashVipRecharge, detail.VipRechargeAmount)
			totalCashRefund = util.AddFloat(totalCashRefund, detail.RefundAmount)
			totalCashDebtRepayment = util.AddFloat(totalCashDebtRepayment, detail.DebtRepaymentAmount)
		}
	}

	totals := &model.HandoverDetailModel{
		MerchantNo:              handoverLog.MerchantNo,
		UserID:                  handoverLog.UserID,
		HandoverLogID:           handoverLog.ID,
		PaymentTypeID:           0,
		OrderAmount:             util.Round(totalAmount),
		OrderCount:              totalCount,
		RefundAmount:            util.Round(totalRefund),
		VipCount:                0,
		VipRechargeAmount:       util.Round(totalVipRecharge),
		PresentRechargeAmount:   util.Round(totalPresentAmount),
		DebtRepaymentAmount:     util.Round(totalDebtRepayment),
		OrderCashAmount:         util.Round(totalCashAmount),
		RefundCashAmount:        util.Round(totalCashRefund),
		VipCashRechargeAmount:   util.Round(totalCashVipRecharge),
		DebtRepaymentCashAmount: totalCashDebtRepayment,
		PaymentType: &model.PaymentTypeModel{
			ID:     0,
			NameZh: "合计",
			NameUg: "جەمئىي",
		},
	}

	details = append(details, totals)
	detailResource := handover_resource.HandoverDetailResource{}
	return detailResource.Collection(&ctx, details), totals, nil
}

package v2

import (
	"github.com/gin-gonic/gin"
	"io"
	"ros-api-go/internal/handler"
	"ros-api-go/pkg/util"
)

// RPCInvokeController RpcInvokeController 主收银服务器调用
type RPCInvokeController struct {
	RpcHandler *handler.RpcHandler
}

// Post 主收银台方法调用
func (ctrl *RPCInvokeController) Post(c *gin.Context) {

	merchantNo := c.Request.Header.Get("Merchantno")
	clientId := c.Request.Header.Get("ClientId")
	body, _ := io.ReadAll(c.Request.Body)

	response, err := ctrl.RpcHandler.HandleRpc(merchantNo, clientId, body)
	if err != nil {
		util.ResError(c, err)
		return
	}
	c.JSO<PERSON>(response.Code, response.Body)
}

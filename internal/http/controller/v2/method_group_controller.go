package v2

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

type MethodGroupController struct {
	MethodGroupService *service.MethodGroupService
}

// GetMethodGroupList 获取做法分组列表
//
// @Tags 做法分组管理
// @Security ApiAuthToken
// @Summary 获取做法分组列表
// @Param page query int false "页码"
// @Param limit query int false "每页数量"
// @Param keyword query string false "搜索关键词"
// @Success 200 {object} util.ResponseResult{Data=[]resource.MethodGroupResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/method-groups [get]
// @X-Author ["Assistant"]
// @X-Date ["2025/07/07 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodGroupController) GetMethodGroupList(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req request.MethodGroupListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResError(c, err)
		return
	}

	methodGroups, pagination, err := ctrl.MethodGroupService.GetList(ctx, merchantNo, &req)
	if err != nil {
		util.ResError(c, err)
		return
	}

	resource := &resource.MethodGroupResource{}
	util.ResSuccessWithPagination(c, resource.Collection(methodGroups), pagination)
}

// CreateMethodGroup 创建做法分组
//
// @Tags 做法分组管理
// @Security ApiAuthToken
// @Summary 创建做法分组
// @Param request body request.CreateMethodGroupRequest true "创建做法分组请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=resource.MethodGroupResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/method-groups [post]
// @X-Author ["Assistant"]
// @X-Date ["2025/07/07 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodGroupController) CreateMethodGroup(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req request.CreateMethodGroupRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	methodGroup, err := ctrl.MethodGroupService.Create(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	resource := &resource.MethodGroupResource{}
	util.ResSuccess(c, resource.Transform(methodGroup))
}

// UpdateMethodGroup 更新做法分组
//
// @Tags 做法分组管理
// @Security ApiAuthToken
// @Summary 更新做法分组
// @Param id path int true "做法分组ID"
// @Param request body request.UpdateMethodGroupRequest true "更新做法分组请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=resource.MethodGroupResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/method-groups/{id} [put]
// @X-Author ["Assistant"]
// @X-Date ["2025/07/07 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodGroupController) UpdateMethodGroup(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}

	var req request.UpdateMethodGroupRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	methodGroup, err := ctrl.MethodGroupService.Update(ctx, id, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	resource := &resource.MethodGroupResource{}
	util.ResSuccess(c, resource.Transform(methodGroup))
}

// DeleteMethodGroup 删除做法分组
//
// @Tags 做法分组管理
// @Security ApiAuthToken
// @Summary 删除做法分组
// @Param id path int true "做法分组ID"
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/method-groups/{id} [delete]
// @X-Author ["Assistant"]
// @X-Date ["2025/07/07 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodGroupController) DeleteMethodGroup(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}

	err = ctrl.MethodGroupService.Delete(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c, "DeleteSuccess")
}

// SaveMethodGroupSort 保存做法分组排序
//
// @Tags 做法分组管理
// @Security ApiAuthToken
// @Summary 保存做法分组排序
// @Param request body request.SaveMethodGroupSortRequest true "排序请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/method-groups/sort [post]
// @X-Author ["Assistant"]
// @X-Date ["2025/07/07 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodGroupController) SaveMethodGroupSort(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req request.SaveMethodGroupSortRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.MethodGroupService.SaveSort(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c, "SortSaveSuccess")
}

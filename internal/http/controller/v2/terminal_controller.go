package v2

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"
)

// TerminalController handles terminal-related API endpoints
type TerminalController struct {
	TerminalService *service.TerminalService
}

// GetActiveTerminals retrieves all active terminals
//
// @Tags 终端管理
// @Security ApiTokenAuth
// @Summary 获取状态为开通的客户端列表
// @Description 获取状态为开通的客户端列表
// @Success 200 {object} util.ResponseResult{data=[]resource.TerminalResource}
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/terminals [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/4/29 12:48"]
// @X-Version ["2.0"]
func (ctrl *TerminalController) GetActiveTerminals(c *gin.Context) {
	ctx := c.Request.Context()

	terminals, err := ctrl.TerminalService.GetActiveTerminals(ctx)
	if err != nil {
		util.ResError(c, err)
		return
	}

	terminalResource := resource.TerminalResource{}
	util.ResSuccess(c, terminalResource.Collection(terminals))
}

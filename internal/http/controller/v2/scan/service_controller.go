package scan

import (
	"github.com/gin-gonic/gin"
	"log"
	"ros-api-go/internal/broadcast/order_broadcast"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request/scan_request"
	"ros-api-go/internal/http/resource/scan_resource"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/internal/service/scan_service"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/util"
	"time"
)

type ServiceController struct {
	TableService           *service.TableService
	MerchantService        *service.MerchantService
	MerchantServiceService *scan_service.MerchantServiceService
	RpcHandler             *handler.RpcHandler
}

// @Tags 扫码点菜
// @Security WechatTokenAuth
// @Summary 小程序呼叫服务
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/scan/service/commit [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/3/12 15:45"]
// @X-Version ["2.0"]
func (ctrl *ServiceController) ServiceCommit(c *gin.Context) {
	ctx := c.Request.Context()
	var formData scan_request.ServiceRequest
	err := util.ParseJSON(c, &formData)
	if err != nil {
		util.ResError(c, err)
		return
	}
	log.Printf("ServiceCommit: %v \n", formData)

	merchantNo := c.Request.Header.Get("Merchantno")
	clientId := c.Request.Header.Get("ClientId")

	table, err := ctrl.TableService.GetTableByID(ctx, merchantNo, formData.TableID)
	if err != nil {
		return
	}

	services, err := ctrl.MerchantServiceService.GetMerchantServicesForBroadcast(ctx, merchantNo, formData.Services)
	if err != nil {
		util.ResError(c, err)
		return
	}

	ctrl.sendRequest(c, merchantNo, clientId, table, services)
}

// sendRequest 广播消息
func (ctrl *ServiceController) sendRequest(c *gin.Context, merchantNo string, clientId string, table *model.TableModel, services []*model.MerchantServiceModel) {

	ctx := c.Request.Context()

	data := scan_resource.ServiceCommitResource{
		TableId:     table.ID,
		MerchantNo:  merchantNo,
		TableNameUg: table.NameUg,
		TableNameZh: table.NameZh,
		CallAt:      time.Now(),
	}
	for _, serviceModel := range services {
		data.ServiceNameUg += "، " + serviceModel.ServiceNameUg
		data.ServiceNameZh += "，" + serviceModel.ServiceNameZh
	}

	data.ServiceNameUg = data.ServiceNameUg[2:]
	data.ServiceNameZh = data.ServiceNameZh[3:]

	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
	}

	// 如果是云端模式, 则保存记录并广播
	if merchant.Mode == model.MerchantModeOnline {
		ctrl.MerchantServiceService.CreateServiceHistory(ctx, merchantNo, table.ID, data.ServiceNameUg, data.ServiceNameZh, 1)
		order_broadcast.SendOrderBroadcast(order_broadcast.ActionCallWaiter, merchantNo, 0, 0, data)
		util.ResOK(c)
		return
	}

	payload := handler.Payload{
		Path:   "/local/api/v2/service/commit",
		Method: "POST",
		Headers: map[string]string{
			"MerchantNo": merchantNo,
			"ClientId":   clientId,
		},
		Body: map[string]interface{}{
			"data": []scan_resource.ServiceCommitResource{
				data,
			},
		},
	}

	payloadBody, _ := json.Marshal(payload)
	response, err := ctrl.RpcHandler.HandRpcWithServerToken(ctx, merchantNo, clientId, payloadBody)

	if err != nil {
		util.ResError(c, err)
		return
	}
	c.JSON(response.Code, response.Body)
}

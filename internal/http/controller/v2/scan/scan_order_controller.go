package scan

import (
	"context"
	"github.com/go-pay/gopay/alipay/v3"
	wechatV3 "github.com/go-pay/gopay/wechat/v3"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/request/scan_request"
	"ros-api-go/internal/http/resource/scan_resource"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/internal/service/scan_service"
	"ros-api-go/pkg/cachex"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type ScanOrderController struct {
	Trans             *util.Trans
	RpcHandler        *handler.RpcHandler
	WechatUserService *service.WechatUserService
	ScanOrderService  *scan_service.ScanOrderService
	MerchantService   *service.MerchantService
	PaymentService    *service.PaymentService
	PaymentLogService *service.PaymentLogService
	WechatPayService  *service.WechatPayService
	AlipayService     *service.AlipayService
	OrderCloudService *service.OrderCloudService
	Cache             cachex.Cacher
}

// Create 创建订单
//
// @Tags 扫码点菜部分
// @Security WechatTokenAuth
// @Summary 创建订单
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/scan/order [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/2/20 16:00"]
// @X-Version ["2.0"]
func (ctrl *ScanOrderController) Create(c *gin.Context) {
	ctx := c.Request.Context()
	// 绑定请求参数
	var formItem scan_request.ScanOrderCreateRequest
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}

	wechatUser, err := ctrl.WechatUserService.GetByID(ctx, util.FromWechatUserID(ctx))
	if err != nil {
		util.ResError(c, err)
		return
	}

	merchantNo := c.Request.Header.Get("Merchantno")
	clientId := c.Request.Header.Get("ClientId")

	formItem.MerchantNo = merchantNo
	formItem.WechatUserID = wechatUser.ID

	switch formItem.PaymentID {
	case consts.PAY_TYPE_WECHAT:
		formItem.OpenID = wechatUser.OpenID
	case consts.PAY_TYPE_ALIPAY:
		formItem.OpenID = wechatUser.AlipayOpenID
	}

	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if merchant == nil {
		util.ResError(c, errors.BadRequest("", "MerchantNotFound"))
		return
	}

	// 商户是否支持微信支付
	if merchant.GetWechatPaymentType() == model.WechatPaymentTypeNone {
		util.ResError(c, errors.BadRequest("", "MerchantNotSupportThirdPartPay"))
		return
	}

	var paymentForm request.MerchantPaymentForm

	// 如果商户是本地模式，则调用本地服务
	if merchant.Mode == model.MerchantModeLocal {
		bodyJson, _ := json.Marshal(formItem)

		var body map[string]interface{}

		if err := json.Unmarshal(bodyJson, &body); err != nil {
			util.ResError(c, err)
			return
		}

		payload := handler.Payload{
			Path:   "/local/api/v2/orders/scan",
			Method: "POST",
			Headers: map[string]string{
				"MerchantNo": merchantNo,
				"ClientId":   clientId,
			},
			Body: body,
		}

		payloadBody, _ := json.Marshal(payload)

		response, err := ctrl.RpcHandler.HandRpcWithServerToken(c, merchantNo, clientId, payloadBody)
		if err != nil {
			util.ResError(c, err)
			return
		}

		if response.Code != 200 {
			c.JSON(response.Code, response.Body)
			return
		}
		jsonRes, _ := json.Marshal(response.Body["data"])
		err = json.Unmarshal(jsonRes, &paymentForm)
		if err != nil {
			util.ResError(c, err)
			return
		}
	} else {
		order, err := ctrl.OrderCloudService.CreateScanOrder(ctx, formItem)
		if err != nil {
			util.ResError(c, err)
			return
		}
		paymentForm = request.MerchantPaymentForm{
			OrderNo:       order.No,
			PaymentNo:     order.No,
			Amount:        int64(util.MultiplyFloat(order.Price, 100)),
			PaymentTypeID: formItem.PaymentID,
			CustomerID:    order.CustomerID,
		}
	}

	paymentForm.MerchantNo = merchantNo

	// 正式环境下，订单金额小于1分钱，不允许支付
	if !config.C.General.Debug && paymentForm.Amount < 1 {
		util.ResError(c, errors.BadRequest("", "OrderAmountTooSmall"))
		return
	}

	var jsAPIPay any

	switch paymentForm.PaymentTypeID {
	case consts.PAY_TYPE_WECHAT:
		if merchant.GetWechatPaymentType() == model.WechatPaymentTypeAlipay {
			util.ResError(c, errors.BadRequest("", "PleaseUseAlipay"))
			return
		}
		formItem.OpenID = wechatUser.OpenID
		jsAPIPay, err = ctrl.wechatJsAPIPay(ctx, wechatUser, &paymentForm, c.ClientIP())
	case consts.PAY_TYPE_ALIPAY:
		if merchant.GetWechatPaymentType() == model.WechatPaymentTypeWechat {
			util.ResError(c, errors.BadRequest("", "PleaseUseWechatPay"))
			return
		}
		formItem.OpenID = wechatUser.AlipayOpenID
		jsAPIPay, err = ctrl.alipayJsAPIPay(ctx, wechatUser, &paymentForm, c.ClientIP())
	default:
		util.ResError(c, errors.BadRequest("", "不支持的支付类型:%d", paymentForm.PaymentTypeID))
		return
	}
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, jsAPIPay)

}

// wechatJsAPIPay 生成微信JSAPI支付
func (ctrl *ScanOrderController) wechatJsAPIPay(ctx context.Context,
	wechatUser *model.WechatUserModel, paymentForm *request.MerchantPaymentForm, clientIP string) (*wechatV3.JSAPIPayParams, error) {

	// 设置支付方式为微信支付
	paymentForm.PaymentTypeID = consts.PAY_TYPE_WECHAT

	// 获取商户信息
	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, paymentForm.MerchantNo)
	if err != nil {
		return nil, err
	}

	var jsAPIPay *wechatV3.JSAPIPayParams

	err = ctrl.Trans.Exec(ctx, func(ctx context.Context) error {

		// 创建支付信息
		paymentInfo, err := ctrl.PaymentService.CreateMerchantPayment(ctx, paymentForm)
		if err != nil {
			return err
		}

		// 创建支付记录
		paymentLog, err := ctrl.PaymentLogService.CreatePaymentLog(ctx, merchant, paymentInfo, consts.TRADE_TYPE_JSAPI, consts.ORDER_TYPE_ORDER, clientIP)
		if err != nil {
			return err
		}

		// 调用微信支付接口
		jsAPIPay, err = ctrl.WechatPayService.JsAPIPay(ctx, wechatUser.OpenID, paymentLog, merchant)
		if err != nil {
			return err
		}

		// 返回数据
		return err
	})
	if err != nil {
		return nil, err
	}
	return jsAPIPay, nil
}

// wechatJsAPIPay 生成微信JSAPI支付
func (ctrl *ScanOrderController) alipayJsAPIPay(ctx context.Context,
	wechatUser *model.WechatUserModel, paymentForm *request.MerchantPaymentForm, clientIP string) (*alipay.TradeCreateRsp, error) {

	// 设置支付方式为微信支付
	paymentForm.PaymentTypeID = consts.PAY_TYPE_ALIPAY

	// 获取商户信息
	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, paymentForm.MerchantNo)
	if err != nil {
		return nil, err
	}

	var jsAPIPay *alipay.TradeCreateRsp

	err = ctrl.Trans.Exec(ctx, func(ctx context.Context) error {

		// 创建支付信息
		paymentInfo, err := ctrl.PaymentService.CreateMerchantPayment(ctx, paymentForm)
		if err != nil {
			return err
		}

		// 创建支付记录
		paymentLog, err := ctrl.PaymentLogService.CreatePaymentLog(ctx, merchant, paymentInfo, consts.TRADE_TYPE_JSAPI, consts.ORDER_TYPE_ORDER, clientIP)
		if err != nil {
			return err
		}

		// 调用微信支付接口
		jsAPIPay, err = ctrl.AlipayService.JsAPIPay(ctx, wechatUser.AlipayOpenID, paymentLog, merchant)
		if err != nil {
			return err
		}

		// 返回数据
		return err
	})
	if err != nil {
		return nil, err
	}
	return jsAPIPay, nil
}

// GetOrderList 获取订单列表
//
// @Tags 扫码点菜部分
// @Security ServerTokenAuth
// @Summary 获取订单列表
// @Param params query scan_request.ScanOrderListRequest true "请求参数"
// @Success 200 {object} util.ResponseResult{data=[]scan_resource.ScanOrderResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/scan/order/list [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/2/24 16:02"]
// @X-Version ["2.0"]
func (ctrl *ScanOrderController) GetOrderList(c *gin.Context) {
	ctx := c.Request.Context()
	// 绑定请求参数
	var queryForm scan_request.ScanOrderListRequest
	if err := util.ParseQuery(c, &queryForm); err != nil {
		util.ResError(c, err)
		return
	}

	queryForm.WechatUserId = util.FromWechatUserID(ctx)

	// 调用服务层
	orders, pagination, err := ctrl.ScanOrderService.OrderList(ctx, queryForm)

	if err != nil {
		util.ResError(c, err)
		return
	}
	resource := scan_resource.ScanOrderResource{}

	// 返回分页结果
	util.ResPage(c, resource.Collection(orders), pagination)
}

// GetOrderDetails 获取订单详情
//
// @Tags 扫码点菜部分
// @Security ServerTokenAuth
// @Summary  获取订单详情
// @Param id path int true "订单ID"
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/scan/order/detail/{id} [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/2/24 16:03"]
// @X-Version ["2.0"]
func (ctrl *ScanOrderController) GetOrderDetails(c *gin.Context) {
	ctx := c.Request.Context()
	orderID := util.StrToInt64(c.Param("id"))
	wechatUserId := util.FromWechatUserID(ctx)

	// 调用服务层
	details, err := ctrl.ScanOrderService.OrderDetails(ctx, orderID, wechatUserId)

	if err != nil {
		util.ResError(c, err)
		return
	}
	resource := scan_resource.ScanOrderDetailResource{}

	// 返回分页结果
	util.ResSuccess(c, resource.Collection(details))
}

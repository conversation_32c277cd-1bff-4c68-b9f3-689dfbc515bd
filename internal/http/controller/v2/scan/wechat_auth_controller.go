package scan

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/request/scan_request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/http/resource/scan_resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"ros-api-go/pkg/wechatx"
)

type WechatAuthController struct {
	WechatUserService service.WechatUserService
}

// 根据code获取微信用户信息
//
// @Tags 扫码点菜接口
// @Security ApiTokenAuth
// @Summary 根据code获取微信用户信息
// @Success 200 {object} util.ResponseResult{data=scan_resource.OpenIDResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/scan/open_id [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/2/8 11:39"]
// @X-Version ["2.0"]
func (ctrl *WechatAuthController) GetOpenId(c *gin.Context) {
	ctx := c.Request.Context()
	code := c.Query("code")
	if code == "" {
		util.ResError(c, errors.BadRequest("", "InvalidCode"))
		return
	}

	session, err := wechatx.CodeToSession(ctx, code)
	if err != nil {
		util.ResError(c, err)
		return
	}

	wechatUser, err := ctrl.WechatUserService.GetByOpenID(ctx, session.Openid)
	if err != nil {
		util.ResError(c, err)
		return
	}
	wechatUserResource := resource.WechatUserBaseResource{}
	loginStatus := 0
	if wechatUser != nil {
		loginStatus = 1
		if err = ctrl.WechatUserService.UpdateLastLogin(ctx, session.Openid); err != nil {
			util.ResError(c, err)
			return
		}
	}
	resp := scan_resource.OpenIDResource{
		LoginState: loginStatus,
		OpenID:     session.Openid,
		SessionKey: session.Session,
		UnionID:    session.Unionid,
		WechatUser: wechatUserResource.Make(wechatUser),
	}

	util.ResSuccess(c, resp)

}

func (ctrl *WechatAuthController) WechatLogin(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem scan_request.WechatLoginRequest
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}

	// 这里可以继续实现你的业务逻辑
	// 例如：调用微信解密API等
	data, err := wechatx.DecryptData(formItem.SessionKey, formItem.EncryptedData, formItem.Iv)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if data.PhoneNumber == "" {
		util.ResError(c, errors.BadRequest("", "InvalidPhoneNumber"))
		return
	}

	wechatUser, err := ctrl.WechatUserService.GetByMobile(c.Request.Context(), data.PhoneNumber)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if wechatUser == nil {
		wechatUser, err = ctrl.WechatUserService.CreateByMobileAndOpenID(ctx, data.PhoneNumber, formItem.OpenId)
	} else {
		err = ctrl.WechatUserService.UpdateLastLogin(ctx, formItem.OpenId)
	}

	if err != nil {
		util.ResError(c, err)
		return
	}

	wechatUserBaseResource := resource.WechatUserBaseResource{}

	util.ResSuccess(c, wechatUserBaseResource.Make(wechatUser))
}

package v2

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

type FoodSpecController struct {
	FoodSpecService service.FoodSpecService
}

// GetFoodSpecList 获取规格列表
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 获取规格列表
// @Param search query string false "搜索关键词（支持中文和维语名称搜索）"
// @Success 200 {object} util.ResponseResult{Data=[]resource.FoodSpecResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foodSpecs [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 16:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) GetFoodSpecList(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req request.FoodSpecListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResError(c, err)
		return
	}

	result, err := ctrl.FoodSpecService.GetList(ctx, merchantNo, &req)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&resource.FoodSpecResource{}).Collection(result))
}

// GetFoodSpec 获取单个规格
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 获取单个规格
// @Success 200 {object} util.ResponseResult{Data=resource.FoodSpecResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foodSpecs/{id} [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 16:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) GetFoodSpec(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	result, err := ctrl.FoodSpecService.GetByID(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&resource.FoodSpecResource{}).Make(result))
}

// CreateFoodSpec 创建规格
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 创建规格
// @Param request body request.CreateFoodSpecRequest true "创建规格请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=resource.FoodSpecResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foodSpecs [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 16:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) CreateFoodSpec(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req request.CreateFoodSpecRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	result, err := ctrl.FoodSpecService.Create(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&resource.FoodSpecResource{}).Make(result))
}

// UpdateFoodSpec 更新规格
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 更新规格
// @Param id path int true "规格ID"
// @Param request body request.UpdateFoodSpecRequest true "更新规格请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=resource.FoodSpecResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foodSpecs/{id} [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 16:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) UpdateFoodSpec(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	var req request.UpdateFoodSpecRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	result, err := ctrl.FoodSpecService.Update(ctx, id, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&resource.FoodSpecResource{}).Make(result))
}

// DeleteFoodSpec 删除规格
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 删除规格
// @Param id path int true "规格ID"
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foodSpecs/{id} [delete]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 16:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) DeleteFoodSpec(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	err := ctrl.FoodSpecService.Delete(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, nil)
}

// GetFoodsBySpecID 获取规格关联的美食列表
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 获取规格关联的美食列表
// @Param id path int true "规格ID"
// @Success 200 {object} util.ResponseResult{Data=[]resource.FoodSpecFoodResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foodSpecs/{id}/foods [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 16:30"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) GetFoodsBySpecID(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	specID := util.StrToInt64(c.Param("id"))

	if specID == 0 {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}

	foods, err := ctrl.FoodSpecService.GetFoodsBySpecID(ctx, specID, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&resource.FoodSpecFoodResource{}).Collection(foods))
}

// SaveFoodSpecSort 保存规格排序
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 保存规格排序
// @Param request body request.SaveFoodSpecSortRequest true "排序请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foodSpecs/sort [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 17:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) SaveFoodSpecSort(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req request.SaveFoodSpecSortRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.FoodSpecService.SaveSort(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, nil)
}

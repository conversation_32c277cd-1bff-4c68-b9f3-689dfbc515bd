package statistics

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/resource/statistic_resource"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service/statistic_service"
	"ros-api-go/pkg/collect"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

type HandoverStatisticController struct {
	Service statistic_service.HandoverStatisticService
}

func (ctrl *HandoverStatisticController) GetStatistics(c *gin.Context) {

	ctx := c.Request.Context()

	beginAtStr := c.Query("begin_at")
	endAtStr := c.Query("end_at")
	cashierID := util.StrToInt64(c.Query("user_id"))
	merchantNo := c.Request.Header.Get("Merchantno")

	if beginAtStr == "" || endAtStr == "" {
		util.ResError(c, errors.BadRequest("", "InvalidDateRange"))
		return
	}

	// 解析日期
	beginAt, err := util.ParseTimeWithZone("2006-01-02 15:04:05", beginAtStr)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidBeginDateRange"))
		return
	}

	endAt, err := util.ParseTimeWithZone("2006-01-02 15:04:05", endAtStr)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidEndDateRange"))
		return
	}

	// 计算天数差
	days := int(endAt.Sub(beginAt).Hours() / 24)

	if days > 31 {
		util.ResError(c, errors.BadRequest("", "TimeRangeExceedsDays", 31))
		return
	}
	handoverLogs, err := ctrl.Service.GetHandoverLogs(ctx, merchantNo, beginAt, endAt, cashierID)
	if err != nil {
		util.ResError(c, err)
		return
	}

	group := collect.GroupBy(handoverLogs, func(item *model.HandoverLogModel) int64 {
		return item.UserID
	})

	if len(group) == 0 {
		util.ResError(c, errors.BadRequest("", "EmptyQueryResult"))
		return
	}

	list := make([]statistic_resource.HandoverStatisticResource, 0, len(group))
	resource := statistic_resource.HandoverStatisticResource{}
	for _, logs := range group {
		list = append(list, resource.Make(&ctx, logs))
	}

	util.ResSuccess(c, list)
}

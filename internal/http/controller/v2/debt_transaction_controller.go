package v2

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
)

// DebtTransactionController 赊账交易记录控制器
type DebtTransactionController struct {
	DebtTransactionService *service.DebtTransactionService
}

// GetList 获取赊账交易记录列表
//
// @Tags 赊账相关接口
// @Security ApiTokenAuth
// @Summary 获取赊账交易记录列表
// @Description 获取赊账交易记录列表，支持按赊账人ID、交易类型和创建时间筛选
// @Produce json
// @Param page query int false "页码，默认1"
// @Param limit query int false "每页数量，默认20"
// @Param holder_id query int false "赊账人ID"
// @Param type query int false "交易类型 1 赊账 2 还款 3 退款"
// @Param begin_at query string false "开始时间 格式：2006-01-02 15:04:05"
// @Param end_at query string false "结束时间 格式：2006-01-02 15:04:05"
// @Success 200 {object} util.ResponseResult{data=[]resource.DebtTransactionResource,meta=util.PaginationResult}
// @Failure 400 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/debt/transactions [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/13 18:10"]
// @X-Version ["2.0"]
func (ctrl *DebtTransactionController) GetList(c *gin.Context) {
	ctx := c.Request.Context()

	// 绑定请求参数
	var queryForm request.DebtTransactionListRequest
	if err := util.ParseQuery(c, &queryForm); err != nil {
		util.ResError(c, err)
		return
	}

	merchantNo := util.GetMerchantNo(c)

	// 验证时间格式
	if queryForm.BeginAt != nil && queryForm.EndAt != nil {
		if queryForm.BeginAt.After(*queryForm.EndAt) {
			util.ResError(c, errors.BadRequest("InvalidDateRange", "InvalidDateRange"))
			return
		}
	}

	// 获取交易记录列表
	transactions, pagination, err := ctrl.DebtTransactionService.GetList(ctx, merchantNo, &queryForm)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 转换为资源
	transactionResource := resource.DebtTransactionResource{}

	util.ResJSON(c, http.StatusOK, resource.DebtTransactionListResource{
		Message: i18n.Msg(&ctx, "GetSuccess"),
		Data:    transactionResource.Collection(ctx, transactions),
		Types:   ctrl.DebtTransactionService.GetDebtTransactionTypes(&ctx, true),
		Meta:    pagination,
	})
}

// Get 获取赊账交易记录详情
//
// @Tags 赊账相关接口
// @Security ApiTokenAuth
// @Summary 获取赊账交易记录详情
// @Description 获取赊账交易记录详情
// @Produce json
// @Param id path int true "交易记录ID"
// @Success 200 {object} util.ResponseResult{data=resource.DebtTransactionResource}
// @Failure 400 {object} util.ResponseResult
// @Failure 404 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/debt/transactions/{id} [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/13 18:10"]
// @X-Version ["2.0"]
func (ctrl *DebtTransactionController) Get(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取ID参数
	id := util.StrToInt64(c.Param("id"))
	if id <= 0 {
		util.ResError(c, errors.BadRequest("InvalidID", "InvalidID"))
		return
	}
	merchantNo := util.GetMerchantNo(c)

	// 获取交易记录详情
	transaction, err := ctrl.DebtTransactionService.GetByID(ctx, merchantNo, id)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if transaction == nil {
		util.ResError(c, errors.NotFound("DebtTransactionNotFound", "DebtTransactionNotFound"))
		return
	}

	// 转换为资源
	transactionResource := resource.DebtTransactionResource{}
	util.ResSuccess(c, transactionResource.Make(ctx, transaction))
}

package v2

import (
	"net/http"
	"ros-api-go/internal/http/request/bill_request"
	"ros-api-go/internal/http/resource/bill_resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

// BillController 处理订单详情相关业务
type BillController struct {
	MerchantService *service.MerchantService
	BillService     *service.BillService
}

// GetList 获取订单列表
//
// @Tags 账单接口
// @Security ServerTokenAuth
// @Summary 获取订单列表
// @Success 200 {object} util.ResponseResult{Data=bill_resource.BillListResource,Top=service.BillTotals,Total=int64}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/bill/list [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/27 13:29"]
// @X-Version ["2.0"]
func (ctrl *BillController) GetList(c *gin.Context) {
	ctx := c.Request.Context()
	req := new(bill_request.BillListRequest)
	if err := util.ParseQuery(c, req); err != nil {
		util.ResError(c, err)
		return
	}
	if err := req.Validate(ctx); err != nil {
		util.ResError(c, err)
		return
	}
	req.MerchantNo = c.Request.Header.Get("Merchantno")

	orders, pagination, err := ctrl.BillService.GetList(ctx, req)
	if err != nil {
		util.ResError(c, err)
		return
	}
	var totals interface{}
	totals, err = ctrl.BillService.GetTotalAmounts(ctx, req)
	if err != nil {
		util.ResError(c, err)
		return
	}
	listItemResource := bill_resource.BillListItemResource{}
	util.ResJSON(c, http.StatusOK, bill_resource.BillListResource{
		Message: i18n.Msg(&ctx, "GetSuccess"),
		Data:    listItemResource.Collection(&ctx, orders),
		Top:     &totals,
		Meta:    pagination,
	})
}

// GetDetail 获取订单详情
//
// @Tags 账单接口
// @Security ServerTokenAuth
// @Summary  获取订单详情
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/bill/detail/{orderId} [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/27 13:31"]
// @X-Version ["2.0"]
func (ctrl *BillController) GetDetail(c *gin.Context) {
	ctx := c.Request.Context()
	orderId := util.StrToInt64(c.Param("orderId"))
	merchantNo := c.Request.Header.Get("Merchantno")
	detail, err := ctrl.BillService.GetDetail(ctx, orderId, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	billResource := bill_resource.BillResource{}
	util.ResSuccess(c, billResource.Make(&ctx, detail))
}

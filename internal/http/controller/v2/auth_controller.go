package v2

import (
	"context"
	"net/http"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/internal/sms"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"ros-api-go/pkg/websocket"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AuthController struct {
	AuthService               *service.AuthService
	MerchantService           *service.MerchantService
	LocalServerService        *service.LocalServerService
	UserService               *service.UserService
	MqttAclService            *service.MqttAclService
	HandoverService           *service.HandoverService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
}

// GetUserMerchants 获取用户商户列表
//
// @Tags 用户相关接口
// @Summary 获取用户商户列表
// @Success 200 {object} util.ResponseResult{data=[]resource.MerchantBasicResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/auth/merchants [post]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/17 17:41"]
// @X-Version ["2.0"]
func (ctrl *AuthController) GetUserMerchants(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.UserMerchantsRequest
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	// 登录用户
	user, err := ctrl.AuthService.LoginUser(ctx, formItem.Username, formItem.Password)

	if err != nil {
		util.ResError(c, err)
		return
	}

	// 获取用户商户列表
	merchants, err := ctrl.MerchantService.GetUserActiveMerchants(ctx, user.ID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if len(merchants) == 0 {
		util.ResError(c, errors.BadRequest("", "NoActiveMerchant"))
		return
	}
	merchantBasicResource := resource.MerchantBasicResource{}
	util.ResSuccess(c, merchantBasicResource.Collection(merchants), "GetSuccess")
}

// UserLogin 登录接口
//
// @Tags 登录接口V2
// @Summary 登录接口
// @Success 200 {object} util.ResponseResult{Data=resource.LoginResource}
// @Failure 400 {object} util.ResponseResult
// @Failure 409 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/auth/login [post]
// @X-Author ["Merdan"]
// @X-Date ["2024/11/18 17:10"]
// @X-Version ["2.0"]
func (ctrl *AuthController) UserLogin(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.LoginForm
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	// 去除空格
	formItem.Trim()

	// 登录用户
	user, err := ctrl.AuthService.LoginUserWithMerchant(ctx, formItem.Username, formItem.Password, formItem.MerchantNo)

	if err != nil {
		util.ResError(c, err)
		return
	}
	formItem.UserID = user.ID

	// 获取用户商户(必须是启用的)
	merchant, err := ctrl.MerchantService.MustGetUserActiveMerchant(ctx, user.ID, formItem.MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 获取用户角色列表
	permissions, err := ctrl.AuthService.GetUserPermissions(ctx, user.ID, merchant.No)
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 是否有收银端登录权限(如果是店长或有收银员登录权限)
	hasCashierLoginPermission := len(permissions) > 0 &&
		(permissions[0] == consts.AdminPermission || util.InArray(consts.CashierLoginPermission, permissions))

	var serverInfo *resource.LocalServerBasicResource

	// 走普通登录逻辑
	formItem.ClientType, err = model.GetClientType(c)

	// 如果是收银端登录
	if formItem.UUID != "" {
		// 如果是在线模式，则不允许登录收银端
		if merchant.Mode == model.MerchantModeOnline {
			util.ResError(c, errors.BadRequest("", "NoRightToLoginCashier"))
			return
		}
		if !hasCashierLoginPermission {
			util.ResError(c, errors.BadRequest("", "NoRightToLoginCashier"))
			logging.Context(ctx).Warn(
				"用户尝试登录收银端，但没有收银端登录权限",
				zap.String("merchantNo", merchant.No),
				zap.String("username", user.Phone))
			return
		}
		// 如果是副主机登录, 走普通登录逻辑
		if formItem.Confirm == request.LoginConfirmAsSubLogin {
			serverInfo, err = ctrl.getMerchantLocalServer(ctx, merchant.No)
		} else { // 否则走注册本地服务逻辑
			serverInfo, err = ctrl.registerLocalServer(ctx, &formItem)
		}
		if err != nil {
			util.ResError(c, err)
			return
		}
	} else {

		if err != nil {
			util.ResError(c, err)
			return
		}
		if merchant.Mode == model.MerchantModeLocal { // 如果是本地模式才发送服务器信息
			serverInfo, err = ctrl.getMerchantLocalServer(ctx, merchant.No)
		}
	}

	// 生成token
	token, err := ctrl.AuthService.GenerateUserToken(ctx, user, formItem.ClientType)

	if err != nil {
		util.ResError(c, err)
		return
	}

	// 部分数据在业务层设置
	loginUserResource := resource.LoginUserResource{}
	hasActiveHanover, err := ctrl.HandoverService.HasActiveHandover(ctx, user.ID, formItem.MerchantNo)
	loginUserResource.IsHandover = !hasActiveHanover

	merchantResource := resource.MerchantResource{}
	loginData := resource.LoginResource{
		User:        loginUserResource.Make(ctx, user),
		Permissions: permissions,
		Merchant:    merchantResource.Make(merchant),
		Token:       token,
	}

	if serverInfo != nil {
		loginData.ServerInfo = serverInfo
		loginData.ServerToken = serverInfo.Token
	}

	logging.Context(ctx).Info("用户登录成功", zap.String("merchantNo", merchant.No), zap.String("username", user.Phone))
	logging.Context(ctx).Info("用户登录成功", zap.String("server_token", loginData.ServerToken), zap.String("token", loginData.Token.AccessToken))

	util.ResSuccess(c, loginData, "UserLoggedIn")
}

// ManagerLogin 老板端登录接口
//
// @Tags 老板端接口
// @Summary 老板端登录接口
// @Success 200 {object} util.ResponseResult{Data=resource.LoginResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/auth/master-login [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/15 17:09"]
// @X-Version ["2.0"]
func (ctrl *AuthController) ManagerLogin(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.LoginForm
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	// 去除空格
	formItem.Trim()
	formItem.ClientType = model.ClientTypeManager

	// 登录用户
	user, err := ctrl.AuthService.LoginUserWithMerchant(ctx, formItem.Username, formItem.Password, formItem.MerchantNo)

	if err != nil {
		util.ResError(c, err)
		return
	}

	// 如果不是老板，则不允许登录
	if !user.IsOwner {
		util.ResError(c, errors.BadRequest("", "NoRightToLoginAsMaster"))
		return
	}

	formItem.UserID = user.ID

	// 获取用户商户(必须是启用的)
	merchant, err := ctrl.MerchantService.MustGetUserActiveMerchant(ctx, user.ID, formItem.MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 获取用户角色列表
	permissions, err := ctrl.AuthService.GetUserPermissions(ctx, user.ID, merchant.No)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 生成token
	token, err := ctrl.AuthService.GenerateUserToken(ctx, user, formItem.ClientType)

	if err != nil {
		util.ResError(c, err)
		return
	}

	// 部分数据在业务层设置
	loginUserResource := resource.LoginUserResource{}

	merchantResource := resource.MerchantResource{}
	loginData := resource.LoginResource{
		User:        loginUserResource.Make(ctx, user),
		Permissions: permissions,
		Merchant:    merchantResource.Make(merchant),
		Token:       token,
	}

	logging.Context(ctx).Info("用户登录成功", zap.String("merchantNo", merchant.No), zap.String("username", user.Phone))

	util.ResSuccess(c, loginData, "UserLoggedIn")
}

// 获取当前启用的本地服务信息
func (ctrl *AuthController) getMerchantLocalServer(ctx context.Context, merchantNo string) (*resource.LocalServerBasicResource, error) {
	localServer, err := ctrl.LocalServerService.GetActiveServerByMerchantNo(ctx, merchantNo)
	if err != nil {
		return nil, errors.Errorf("FailedToGetLocalServer")
	}
	if localServer == nil {
		return nil, errors.BadRequest("", "LocalServerNotRegistered")
	}

	return &resource.LocalServerBasicResource{
		ID:    localServer.ID,
		Ipv4:  localServer.Ipv4,
		Ipv6:  localServer.Ipv6,
		WsUrl: websocket.GetServerUrl(),
	}, nil
}

// 注册本地服务
func (ctrl *AuthController) registerLocalServer(ctx context.Context, formItem *request.LoginForm) (*resource.LocalServerBasicResource, error) {
	// 获取当前本地服务信息
	localServer, err := ctrl.LocalServerService.GetActiveServerByMerchantNo(ctx, formItem.MerchantNo)
	if err != nil {
		return nil, errors.Errorf("FailedToGetLocalServer")
	}

	if localServer != nil {
		// 如果不是同一个设备, 且未确认，则返回错误
		if localServer.UUID != formItem.UUID && formItem.UserID == localServer.UserID && formItem.Confirm == request.LoginConfirmUnconfirmed {
			return nil, errors.New("", "HasLoginAsMainServer", http.StatusNotAcceptable)
		}
		// 如果不是同一个设备, 且未确认，则返回错误
		if localServer.UUID != formItem.UUID && formItem.Confirm == request.LoginConfirmUnconfirmed {
			return nil, errors.Conflict(errors.LocalServerConflictID, "HasActiveLocalServer")
		}
	}

	// 否则，注册本地服务
	localServer, err = ctrl.LocalServerService.RegisterLocalServer(ctx, formItem)
	if err != nil {
		return nil, errors.BadRequest("", "FailedToRegisterLocalServer")
	}

	return &resource.LocalServerBasicResource{
		ID:    localServer.ID,
		Ipv4:  localServer.Ipv4,
		Ipv6:  localServer.Ipv6,
		WsUrl: websocket.GetServerUrl(),
		Token: localServer.Token,
	}, nil

}

// Info 获取用户信息
//
// @Tags 登录接口V2
// @Security ApiTokenAuth
// @Summary 获取用户信息
// @Success 200 {object} util.ResponseResult{Data=resource.LoginResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/auth/info [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/11/18 17:11"]
// @X-Version ["2.0"]
func (ctrl *AuthController) Info(c *gin.Context) {
	ctx := c.Request.Context()

	userID := util.FromUserID(ctx)
	merchantNo := c.Request.Header.Get("Merchantno")
	// 登录用户
	user, err := ctrl.UserService.GetByUserIDWithEmployeeData(ctx, userID, merchantNo)

	if err != nil {
		util.ResError(c, err)
		return
	} else if user == nil {
		util.ResError(c, errors.BadRequest("", "UserNotFound"))
		return
	}

	// 获取用户商户列表
	merchant, err := ctrl.MerchantService.MustGetUserActiveMerchant(ctx, user.ID, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 获取用户角色列表
	permissions, err := ctrl.AuthService.GetUserPermissions(ctx, user.ID, merchant.No)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "FailedToGetPermissions"))
		return
	}

	// 获取本地服务信息（注册或直接获取）
	serverInfo, err := ctrl.getMerchantLocalServer(ctx, merchant.No)

	if err != nil {
		util.ResError(c, err)
		return
	}

	// 部分数据在业务层设置
	loginUserResource := resource.LoginUserResource{}
	merchantResource := resource.MerchantResource{}
	hasActiveHanover, err := ctrl.HandoverService.HasActiveHandover(ctx, user.ID, merchant.No)
	loginUserResource.IsHandover = !hasActiveHanover

	UserData := resource.LoginResource{
		User:        loginUserResource.Make(ctx, user),
		Permissions: permissions,
		Merchant:    merchantResource.Make(merchant),
		ServerInfo:  serverInfo,
		ServerToken: serverInfo.Token,
	}
	util.ResSuccess(c, UserData, "GetSuccess")
}

// Logout 退出登录
//
// @Tags 登录接口V2
// @Security ApiTokenAuth
// @Summary 退出登录
// @Success 200 {object} util.ResponseResult{message=string}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/auth/logout [post]
// @X-Author ["Merdan"]
// @X-Date ["2024/11/18 17:19"]
// @X-Version ["2.0"]
func (ctrl *AuthController) Logout(c *gin.Context) {
	ctx := c.Request.Context()
	userToken := util.FromUserToken(ctx)
	err := ctrl.AuthService.Logout(ctx, userToken)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResOK(c)
}

// SendResetPasswordSmsCode 发送忘记密码短信
//
// @Tags 登录接口V2
// @Summary 发送忘记密码短信
// @Success 200 {object} util.ResponseResult{message=string}
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/auth/send-verify-sms [post]
// @Param body body request.SendSmsForm true "发送短信信息"
// @X-Author ["Alim Kirem"]
// @X-Date ["2025/01/24 17:19"]
// @X-Version ["2.0"]
func (ctrl *AuthController) SendResetPasswordSmsCode(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.SendSmsForm
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}

	// 检查用户是否存在
	user, err := ctrl.UserService.GetByPhone(ctx, formItem.Phone)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if user == nil {
		util.ResError(c, errors.BadRequest("", "UserNotFound"))
		return
	}

	batchID, err := sms.SendForgotPasswordSMS(ctx, formItem.Phone)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, map[string]string{"batchID": batchID})
}

// ResetPassword 验证忘记密码短信
//
// @Tags 登录接口V2
// @Summary 验证忘记密码短信
// @Success 200 {object} util.ResponseResult{message=string}
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/auth/verify-sms [post]
// @Param body body request.VerifySmsForm true "验证码信息"
// @X-Author ["Alim Kirem"]
// @X-Date ["2025/01/24 17:19"]
// @X-Version ["2.0"]
func (ctrl *AuthController) ResetPassword(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.VerifySmsForm
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	// 检查用户是否存在
	user, err := ctrl.UserService.GetByPhone(ctx, formItem.Phone)
	if err != nil {
		util.ResError(c, err)
		return
	}

	result, err := sms.VerifySmsCode(ctx, formItem.Phone, formItem.BatchID, consts.CacheKeyForForgetPwdSMSCode, formItem.Code)
	if result {
		// 更新用户密码
		err = ctrl.UserService.UpdatePassword(ctx, user.ID, formItem.Password)
		if err != nil {
			logging.Context(ctx).Error("failed to update password",
				zap.Int64("userID", user.ID),
				zap.Error(err))
			util.ResError(c, errors.BadRequest("", "FailedToUpdatePassword"))
			return
		}
		util.ResOK(c)

		merchants, _ := ctrl.MerchantService.GetUserActiveMerchants(ctx, user.ID)
		for _, merchant := range merchants {
			ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchant.No, model.UserModel{})
		}

		return
	}
	if err != nil {
		logging.Context(ctx).Info("verify sms code fail",
			zap.String("phone", formItem.Phone),
			zap.String("batchID", formItem.BatchID),
			zap.String("code", formItem.Code),
		)
	}
	util.ResError(c, errors.BadRequest("", "InvalidVerifyCode"))

}

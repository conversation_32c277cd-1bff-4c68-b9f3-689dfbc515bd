package bill_resource

import (
	"context"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
)

type BillListResource struct {
	Message string                  `json:"message"`
	Data    []*BillListItemResource `json:"data"`
	Top     *any                    `json:"top,omitempty"`
	Meta    *util.PaginationResult  `json:"meta"`
}

type BillListItemResource struct {
	OrderID       int64   `json:"order_id"`            // 订单ID
	OrderNo       string  `json:"order_no"`            // 订单号
	CancelAmount  float64 `json:"cancel_amount"`       // 取消金额
	CashierName   string  `json:"cashier_name"`        // 收银员姓名
	CreatorName   string  `json:"creator_name"`        // 下单人姓名
	IgnorePrice   float64 `json:"ignore_price"`        // 优惠金额
	IsScanOrder   bool    `json:"is_scan_order"`       // 是否扫码点单
	OriginalPrice float64 `json:"original_price"`      // 原价
	Price         float64 `json:"price"`               // 实付金额
	RefundPrice   float64 `json:"total_refund_amount"` // 总退款金额
	TableName     string  `json:"table_name"`          // 餐桌名称
	TableNo       string  `json:"table_no"`            // 餐桌号
	TaxTicket     bool    `json:"tax_ticket"`          // 是否开票 0-否 1-是
	PaidAt        *string `json:"paid_at"`             // 支付时间
	CreatedAt     *string `json:"created_at"`          // 创建时间
	UpdatedAt     *string `json:"updated_at"`          // 更新时间
}

func (rc *BillListItemResource) Make(ctx *context.Context, item *model.OrderModel) *BillListItemResource {
	if item == nil {
		return nil
	}
	isZh := i18n.IsZh(ctx)
	data := BillListItemResource{
		OrderID:       item.ID,
		OrderNo:       item.No,
		CancelAmount:  item.CanceledAmount,
		IgnorePrice:   item.IgnorePrice,
		IsScanOrder:   item.IsScanOrder,
		OriginalPrice: item.OriginalPrice,
		Price:         item.Price,
		TaxTicket:     item.TaxTicket,
		RefundPrice:   item.RefundPrice,
		PaidAt:        util.FormatDateTime(item.PaidAt),
		CreatedAt:     util.FormatDateTime(item.CreatedAt),
		UpdatedAt:     util.FormatDateTime(item.UpdatedAt),
	}
	if item.Table != nil {
		if isZh {
			data.TableName = item.Table.NameZh
		} else {
			data.TableName = item.Table.NameUg
		}
		data.TableNo = item.Table.No
	}
	if item.Cashier != nil {
		if isZh {
			data.CashierName = item.Cashier.NameZh
		} else {
			data.CashierName = item.Cashier.NameUg
		}
	}
	if item.Creator != nil {
		if isZh {
			data.CreatorName = item.Creator.NameZh
		} else {
			data.CreatorName = item.Creator.NameUg
		}
	}
	return &data
}

func (rc *BillListItemResource) Collection(ctx *context.Context, items []*model.OrderModel) []*BillListItemResource {

	if items == nil || len(items) == 0 {
		return []*BillListItemResource{}
	}
	data := make([]*BillListItemResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

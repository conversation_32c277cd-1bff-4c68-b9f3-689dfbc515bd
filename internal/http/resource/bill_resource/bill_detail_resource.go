package bill_resource

import (
	"context"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/collect"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
	"sort"
)

type BillResource struct {
	OrderID         int64                      `json:"order_id"`            // 订单ID
	OrderNo         string                     `json:"order_no"`            // 订单号
	TableName       string                     `json:"table_name"`          // 餐桌名称
	TableNo         string                     `json:"table_no"`            // 餐桌号
	CustomersCount  int                        `json:"customers_count"`     // 客户数
	FoodsCount      float64                    `json:"foods_count"`         // 菜品数量
	CancelAmount    float64                    `json:"cancel_amount"`       // 取消金额
	CashierName     string                     `json:"cashier_name"`        // 收银员姓名
	CreatorName     string                     `json:"creator_name"`        // 收银员姓名
	IgnorePrice     float64                    `json:"ignore_price"`        // 优惠金额
	IsScanOrder     bool                       `json:"is_scan_order"`       // 是否扫码点单
	OriginalPrice   float64                    `json:"original_price"`      // 原价
	PaidAt          *string                    `json:"paid_at"`             // 支付时间
	Price           float64                    `json:"price"`               // 实付金额
	TaxTicket       bool                       `json:"tax_ticket"`          // 是否开票 0-否 1-是
	CollectedAmount *float64                   `json:"collected_amount"`    // 已收金额                           // 结账人员编号
	GiveChange      *float64                   `json:"give_change"`         // 找零金额
	RefundPrice     float64                    `json:"total_refund_amount"` // 总退款金额
	CreatedAt       *string                    `json:"created_at"`          // Create time
	UpdatedAt       *string                    `json:"updated_at"`          // Update time
	BillDetails     []*BillDetailResource      `json:"bill_details"`        // 订单详情
	RefundBatches   []*BillRefundBatchResource `json:"refund_batches"`      // 退款批次
	Payments        []*BillPaymentResource     `json:"payments"`            // 支付记录
}

func (rc *BillResource) Make(ctx *context.Context, item *model.OrderModel) *BillResource {
	if item == nil {
		return nil
	}
	isZh := i18n.IsZh(ctx)
	data := BillResource{
		OrderID:        item.ID,
		OrderNo:        item.No,
		CustomersCount: item.CustomersCount,
		FoodsCount:     item.FoodsCount,
		CancelAmount:   0,
		IgnorePrice:    item.IgnorePrice,
		IsScanOrder:    item.IsScanOrder,
		OriginalPrice:  item.OriginalPrice,
		Price:          item.Price,
		TaxTicket:      item.TaxTicket,
		RefundPrice:    item.RefundPrice,
		PaidAt:         util.FormatDateTime(item.PaidAt),
		CreatedAt:      util.FormatDateTime(item.CreatedAt),
		UpdatedAt:      util.FormatDateTime(item.UpdatedAt),
	}
	if item.Table != nil {
		if isZh {
			data.TableName = item.Table.NameZh
		} else {
			data.TableName = item.Table.NameUg
		}
		data.TableNo = item.Table.No
	}
	if item.Cashier != nil {
		if isZh {
			data.CashierName = item.Cashier.NameZh
		} else {
			data.CashierName = item.Cashier.NameUg
		}
	}
	if item.Creator != nil {
		if isZh {
			data.CreatorName = item.Creator.NameZh
		} else {
			data.CreatorName = item.Creator.NameUg
		}
	}
	data.BillDetails = make([]*BillDetailResource, 0)
	data.RefundBatches = make([]*BillRefundBatchResource, 0)
	data.Payments = make([]*BillPaymentResource, 0)
	if item.Details != nil && len(item.Details) > 0 {
		detailResource := BillDetailResource{}
		for _, detail := range item.Details {
			// 如果撤销退菜/并单的详情/数量为0(全部退菜)不用显示
			if detail.State > consts.ORDER_DETAIL_STATE_CANCEL || detail.FoodsCount == 0 {
				continue
			}
			// 已取消的
			exists := collect.Exists(data.BillDetails, func(i *BillDetailResource) bool {
				return i.FoodID == detail.FoodID
			})
			data.BillDetails = append(data.BillDetails, detailResource.Make(ctx, detail, !exists))
		}
	}
	if item.Payments != nil && len(item.Payments) > 0 {
		resource := BillPaymentResource{}
		data.Payments = resource.Collection(ctx, item.Payments)
	}
	if item.RefundBatches != nil && len(item.RefundBatches) > 0 {
		resource := BillRefundBatchResource{}
		data.RefundBatches = resource.Collection(ctx, item.RefundBatches, &data.BillDetails)
	}

	// 对 data.BillDetails 进行排序
	sort.Slice(data.BillDetails, func(i, j int) bool {
		return data.BillDetails[i].FoodID < data.BillDetails[j].FoodID && data.BillDetails[i].State < data.BillDetails[j].State
	})
	return &data
}

type BillDetailResource struct {
	ID           int64   `json:"id" gorm:"size:20;primaryKey;"` // ID
	OrderID      int64   `json:"order_id"`                      // 订单编号
	FoodID       int64   `json:"food_id"`                       // 美食编号
	FoodName     string  `json:"food_name"`                     // 美食名称
	FoodsCount   float64 `json:"foods_count"`                   // 美食数量
	FoodFormatID int     `json:"food_format_id"`                // 美食规格ID
	UnitPrice    float64 `json:"unit_price"`                    // 出售价
	TotalPrice   float64 `json:"total_price"`                   // 总价格
	State        int     `json:"state"`                         // 订单状态
	Remarks      string  `json:"remarks"`                       // 备注
	Line         bool    `json:"line"`                          // 是否显示分割线
	CreatedAt    *string `json:"created_at"`                    // 创建时间
	UpdatedAt    *string `json:"updated_at"`                    // 更新时间
}

func (rc *BillDetailResource) Make(ctx *context.Context, item *model.OrderDetailModel, hasLine bool) *BillDetailResource {
	if item == nil {
		return nil
	}
	data := BillDetailResource{
		ID:         item.ID,
		OrderID:    item.OrderID,
		FoodID:     item.FoodID,
		FoodsCount: item.FoodsCount,
		UnitPrice:  item.Price,
		TotalPrice: item.TotalPrice,
		Remarks:    *item.Remarks,
		State:      item.State,
		Line:       hasLine, // 有详情才显示分割线
		CreatedAt:  util.FormatDateTime(item.CreatedAt),
		UpdatedAt:  util.FormatDateTime(item.UpdatedAt),
	}
	if item.Food != nil {
		if i18n.IsZh(ctx) {
			data.FoodName = item.Food.NameZh
		} else {
			data.FoodName = item.Food.NameUg
		}
		data.FoodFormatID = item.Food.FormatID
	}
	return &data
}

func (rc *BillDetailResource) MakeFromRefund(ctx *context.Context, item *model.RefundOrderLogModel, hasLine bool) *BillDetailResource {
	if item == nil {
		return nil
	}
	data := BillDetailResource{
		ID:         item.ID,
		OrderID:    item.OrderID,
		FoodID:     item.FoodID,
		FoodsCount: item.RefundCount,
		UnitPrice:  util.Round(item.RefundAmount / item.RefundCount),
		TotalPrice: item.RefundAmount,
		State:      consts.ORDER_DETAIL_STATE_REFUND,
		Line:       hasLine, // 有详情才显示分割线
		Remarks:    item.Remarks,
		CreatedAt:  util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:  util.FormatDateTime(&item.UpdatedAt),
	}
	if item.Food != nil {
		if i18n.IsZh(ctx) {
			data.FoodName = item.Food.NameZh
		} else {
			data.FoodName = item.Food.NameUg
		}
		data.FoodFormatID = item.Food.FormatID
	}
	return &data
}

type BillPaymentResource struct {
	ID            int64   `gorm:"primaryKey;autoIncrement" json:"id"` // 主键，自增
	OrderNo       string  `json:"order_no"`                           // 订单编号
	Icon          string  `json:"icon"`                               // 付款图标
	Name          string  `json:"name"`                               // 付款类型名称
	PaymentTypeID int64   `json:"payment_type_id"`                    // 付款类型ID
	PaymentNo     string  `json:"payment_no"`                         // 付款编号
	Amount        float64 `json:"amount"`                             // 付款金额
	PayType       string  `json:"pay_type"`                           // 付款类型
	Remark        *string `json:"remark"`                             // 备注
	Status        int64   `json:"status"`                             // 付款状态
	CashierID     int64   `json:"cashier_id"`                         // 收银员ID
	PaidAt        *string `json:"paid_at"`                            // 付款时间
	RefundAmount  float64 `json:"refund_amount"`                      // 退款金额
	CreatedAt     *string `json:"created_at"`                         // 创建时间
}

func (rc *BillPaymentResource) Make(ctx *context.Context, item *model.MerchantPaymentModel) *BillPaymentResource {
	if item == nil {
		return nil
	}
	data := BillPaymentResource{
		ID:            item.ID,
		OrderNo:       item.OrderNo,
		PaymentTypeID: item.PaymentTypeID,
		PaymentNo:     item.PaymentNo,
		Amount:        util.DivideFloat(float64(item.Amount), 100),       // 订单金额单位为分，需要除以100
		RefundAmount:  util.DivideFloat(float64(item.RefundAmount), 100), // 订单金额单位为分，需要除以100
		PayType:       item.PayType,
		Status:        item.Status,
		CashierID:     item.CashierID,
		PaidAt:        util.FormatDateTime(item.PaidAt),
		CreatedAt:     util.FormatDateTime(&item.CreatedAt),
	}
	if item.PaymentType != nil {
		if i18n.IsZh(ctx) {
			data.Name = item.PaymentType.NameZh
		} else {
			data.Name = item.PaymentType.NameUg
		}
		data.Icon = config.C.Storage.OSS.Endpoint + item.PaymentType.Icon
	}
	return &data
}

func (rc *BillPaymentResource) Collection(ctx *context.Context, items []*model.MerchantPaymentModel) []*BillPaymentResource {

	if items == nil || len(items) == 0 {
		return []*BillPaymentResource{}
	}
	data := make([]*BillPaymentResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

type BillRefundBatchResource struct {
	ID           int64                 `json:"id"`            // 主键
	BatchNo      string                `json:"batch_no"`      // 批次号
	OrderID      int64                 `json:"order_id"`      // 订单ID
	TotalRefunds int64                 `json:"total_refunds"` // 退款总数
	TotalAmount  float64               `json:"total_amount"`  // 总订单金额
	Status       int64                 `json:"status"`        // 状态
	CashierID    int64                 `json:"cashier_id"`    // 收银员ID
	CreatedAt    *string               `json:"created_at"`    // 创建时间
	UpdatedAt    *string               `json:"updated_at"`    // 更新时间
	RefundFoods  []*BillDetailResource `json:"refund_foods"`  // 退款菜品
	Refunds      []*BillRefundResource `json:"refunds"`       // 退款详情
}

func (rc *BillRefundBatchResource) Make(ctx *context.Context, item *model.RefundBatchModel, billDetails *[]*BillDetailResource) *BillRefundBatchResource {
	if item == nil {
		return nil
	}
	data := BillRefundBatchResource{
		ID:           item.ID,
		BatchNo:      item.BatchNo,
		OrderID:      item.OrderID,
		TotalRefunds: item.TotalRefunds,
		TotalAmount:  util.Round(util.DivideFloat(float64(item.TotalAmount), 100)),
		Status:       item.Status,
		CashierID:    item.CashierID,
		CreatedAt:    util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:    util.FormatDateTime(&item.UpdatedAt),
	}

	if item.OrderRefundLogs != nil && len(item.OrderRefundLogs) > 0 {
		billDetailResource := BillDetailResource{}
		data.RefundFoods = make([]*BillDetailResource, 0, len(item.OrderRefundLogs))
		for _, orderRefundLog := range item.OrderRefundLogs {
			exists := collect.Exists(*billDetails, func(i *BillDetailResource) bool {
				return i.FoodID == orderRefundLog.FoodID
			})
			detailResource := billDetailResource.MakeFromRefund(ctx, orderRefundLog, !exists)
			data.RefundFoods = append(data.RefundFoods, detailResource)
			*billDetails = append(*billDetails, detailResource)
		}
	}
	if item.RefundLogs != nil && len(item.RefundLogs) > 0 {
		resource := BillRefundResource{}
		data.Refunds = resource.Collection(ctx, item.RefundLogs)
	}
	return &data
}

func (rc *BillRefundBatchResource) Collection(ctx *context.Context, items []*model.RefundBatchModel, billDetails *[]*BillDetailResource) []*BillRefundBatchResource {

	if items == nil || len(items) == 0 {
		return []*BillRefundBatchResource{}
	}
	data := make([]*BillRefundBatchResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item, billDetails)
	}
	return data
}

type BillRefundResource struct {
	ID            int64   `json:"id"`              // ID
	PaymentNo     string  `json:"payment_no"`      // 支付编号
	PaymentTypeID int64   `json:"payment_type_id"` // 支付类型ID
	Name          string  `json:"name"`            // 美食名称
	Amount        float64 `json:"amount"`          // 退款数量
	RefundAt      *string `json:"refund_at"`       // 退款时间
}

func (rc *BillRefundResource) Make(ctx *context.Context, item *model.MerchantRefundLogModel) *BillRefundResource {
	if item == nil {
		return nil
	}
	data := BillRefundResource{
		ID:            item.ID,
		PaymentNo:     item.PaymentNo,
		PaymentTypeID: item.PaymentTypeID,
		Amount:        util.Round(util.DivideFloat(float64(item.Amount), 100)),
		RefundAt:      util.FormatDateTime(item.RefundAt),
	}
	if item.PaymentType != nil {
		if i18n.IsZh(ctx) {
			data.Name = item.PaymentType.NameZh
		} else {
			data.Name = item.PaymentType.NameUg
		}
	}
	return &data
}

func (rc *BillRefundResource) Collection(ctx *context.Context, items []*model.MerchantRefundLogModel) []*BillRefundResource {

	if items == nil || len(items) == 0 {
		return []*BillRefundResource{}
	}
	data := make([]*BillRefundResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

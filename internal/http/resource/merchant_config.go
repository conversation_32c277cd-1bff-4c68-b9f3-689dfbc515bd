package resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type MerchantConfigResource struct {
	ID         int64      `gorm:"primaryKey;autoIncrement" json:"id"` // 主键，自增
	MerchantNo *string    `json:"merchant_no"`                        // 商家编号
	Type       string     `json:"type"`                               // 设置配置类型（ignore price）
	Value      *util.JSON `json:"value"`                              // 值
	State      *int64     `json:"state"`                              // 状态
	CreatedAt  *string    `json:"created_at"`                         // Create time
	UpdatedAt  *string    `json:"updated_at"`                         // Update time
	DeletedAt  *string    `json:"deleted_at"`                         // Delete time
}

func (rc *MerchantConfigResource) Make(item *model.MerchantConfigModel) *MerchantConfigResource {
	if item == nil {
		return nil
	}
	var data = MerchantConfigResource{
		ID:         item.ID,
		MerchantNo: item.MerchantNo,
		Type:       item.Type,
		Value:      item.Value,
		State:      item.State,
		CreatedAt:  util.FormatDateTime(item.CreatedAt),
		UpdatedAt:  util.FormatDateTime(item.UpdatedAt),
		DeletedAt:  util.FormatDateTime(item.DeletedAt),
	}
	return &data
}

func (rc *MerchantConfigResource) Collection(items []*model.MerchantConfigModel) []*MerchantConfigResource {

	if items == nil || len(items) == 0 {
		return []*MerchantConfigResource{}
	}
	data := make([]*MerchantConfigResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

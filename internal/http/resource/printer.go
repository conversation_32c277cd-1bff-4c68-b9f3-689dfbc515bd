package resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type PrinterResource struct {
	ID             string               `json:"id"`              // 打印机ID
	NameZh         string               `json:"name_zh"`         // 中文名称
	NameUg         string               `json:"name_ug"`         // 维语名称
	ConnectionType string               `json:"connection_type"` // 连接方式 network/usb/inner(内置打印机)/cloud
	IpAddress      string               `json:"ip_address"`      // IP地址(网络打印机使用)
	UsbPort        string               `json:"usb_port"`        // USB端口号(USB打印机使用)
	Cloud          string               `json:"cloud"`           // 云打印机号
	PaperWidth     int                  `json:"paper_width"`     // 纸张宽度(58mm或80mm)
	PrintMode      string               `json:"print_mode"`      // 打印方式 text/image
	Status         int                  `json:"status"`          // 打印机状态 0:禁用 1:启用 -1:删除
	Buzzer         bool                 `json:"buzzer"`          // 蜂鸣器	false:不提醒 true:提醒
	CreatedAt      *string              `json:"created_at"`      // 创建时间
	UpdatedAt      *string              `json:"updated_at"`      // 更新时间
	DeletedAt      *string              `json:"deleted_at"`      // 删除时间
	CashierConfig  *model.CashierConfig `json:"cashier_config"`  // 收银打印机配置
	KitchenConfig  *model.KitchenConfig `json:"kitchen_config"`  // 后厨打印机配置
}

func (rc *PrinterResource) Make(item *model.PrinterModel) *PrinterResource {
	if item == nil {
		return nil
	}
	data := PrinterResource{
		ID:             item.ID,
		NameUg:         item.NameUg,
		NameZh:         item.NameZh,
		ConnectionType: item.ConnectionType,
		IpAddress:      item.IpAddress,
		UsbPort:        item.UsbPort,
		Cloud:          item.Cloud,
		PaperWidth:     item.PaperWidth,
		PrintMode:      item.PrintMode,
		Status:         item.Status,
		Buzzer:         item.Buzzer,
		CreatedAt:      util.FormatDateTime(item.CreatedAt),
		UpdatedAt:      util.FormatDateTime(item.UpdatedAt),
		DeletedAt:      util.FormatDateTime(item.DeletedAt),
		CashierConfig: &model.CashierConfig{
			BusinessTicket:  item.CashierConfig.BusinessTicket,
			DeliveryTicket:  item.CashierConfig.DeliveryTicket,
			PreTicket:       item.CashierConfig.PreTicket,
			RechargeTicket:  item.CashierConfig.RechargeTicket,
			StatementTicket: item.CashierConfig.StatementTicket,
		},
		KitchenConfig: &model.KitchenConfig{
			BackTicket:        item.KitchenConfig.BackTicket,
			OrderMergeTicket:  item.KitchenConfig.OrderMergeTicket,
			SplitTicket:       item.KitchenConfig.SplitTicket,
			TableChangeTicket: item.KitchenConfig.TableChangeTicket,
		},
	}
	return &data
}

func (rc *PrinterResource) Collection(items []*model.PrinterModel) []*PrinterResource {

	if items == nil || len(items) == 0 {
		return []*PrinterResource{}
	}
	data := make([]*PrinterResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

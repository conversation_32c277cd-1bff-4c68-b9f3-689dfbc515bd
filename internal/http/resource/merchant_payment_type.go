package resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type MerchantPaymentTypeResource struct {
	ID            int64   `gorm:"primaryKey;autoIncrement" json:"id"` // 主键，自增
	MerchantID    int64   `json:"merchant_id"`                        // 商家编号
	MerchantNo    string  `json:"merchant_no"`                        // 商家唯一编号
	PaymentTypeID int64   `json:"payment_type_id"`                    // 支付类型编号
	PayMethod     *int64  `json:"pay_method"`                         // 支付产品类型（微信支付1：刷卡支付、2：扫码支付）
	Sort          *int64  `json:"sort"`                               // 排序
	State         *int64  `json:"state"`                              // 状态
	CreatedAt     *string `json:"created_at"`                         // Create time
	UpdatedAt     *string `json:"updated_at"`                         // Update time
	DeletedAt     *string `json:"deleted_at"`                         // Delete time
}

func (rc *MerchantPaymentTypeResource) Make(item *model.MerchantPaymentTypeModel) *MerchantPaymentTypeResource {
	if item == nil {
		return nil
	}
	data := MerchantPaymentTypeResource{
		ID:            item.ID,
		MerchantID:    item.MerchantID,
		MerchantNo:    item.MerchantNo,
		PaymentTypeID: item.PaymentTypeID,
		PayMethod:     item.PayMethod,
		Sort:          item.Sort,
		State:         item.State,
		CreatedAt:     util.FormatDateTime(item.CreatedAt),
		UpdatedAt:     util.FormatDateTime(item.UpdatedAt),
		DeletedAt:     util.FormatDateTime(item.DeletedAt),
	}
	return &data
}

func (rc *MerchantPaymentTypeResource) Collection(items []*model.MerchantPaymentTypeModel) []*MerchantPaymentTypeResource {

	if items == nil || len(items) == 0 {
		return []*MerchantPaymentTypeResource{}
	}
	data := make([]*MerchantPaymentTypeResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

package resource

import (
	"ros-api-go/internal/config"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type PaymentTypeResource struct {
	ID        int64   `json:"id" gorm:"size:20;primaryKey;"` // Unique ID
	Tag       string  `json:"tag" gorm:"size:20;"`           // 标签
	NameUg    string  `json:"name_ug" gorm:"size:50;"`       // 名称(维语)
	NameZh    string  `json:"name_zh" gorm:"size:50;"`       // 名称(中文)
	Icon      string  `json:"icon"`                          // 图标
	Type      int64   `json:"type" gorm:"size:10;"`          // 类型
	PayMethod int64   `json:"pay_method" gorm:"size:10;"`    // 支付方式
	Online    int64   `json:"online" gorm:"size:10;"`        // 在线支付
	Sort      int64   `json:"sort"`                          // 排序
	State     int64   `json:"state" gorm:"size:10;"`         // 状态
	CreatedAt *string `json:"created_at"`                    // Create time
	UpdatedAt *string `json:"updated_at"`                    // Update time
	DeletedAt *string `json:"deleted_at"`                    // Delete time
}

func (rc *PaymentTypeResource) Make(item *model.PaymentTypeModel) *PaymentTypeResource {
	if item == nil {
		return nil
	}
	data := PaymentTypeResource{
		ID:        item.ID,
		Tag:       item.Tag,
		NameUg:    item.NameUg,
		NameZh:    item.NameZh,
		Icon:      config.C.Storage.OSS.Endpoint + item.Icon,
		Type:      item.Type,
		PayMethod: item.PayMethod,
		Online:    item.Online,
		Sort:      item.Sort,
		State:     item.State,
		CreatedAt: util.FormatDateTime(&item.CreatedAt),
		UpdatedAt: util.FormatDateTime(&item.UpdatedAt),
		DeletedAt: util.FormatDateTime(item.DeletedAt),
	}
	return &data
}

func (rc *PaymentTypeResource) Collection(items []*model.PaymentTypeModel) []*PaymentTypeResource {

	if items == nil || len(items) == 0 {
		return []*PaymentTypeResource{}
	}
	data := make([]*PaymentTypeResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

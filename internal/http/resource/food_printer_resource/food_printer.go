package food_printer_resource

import "ros-api-go/internal/model"

type CategoryResource struct {
	ID     int64           `json:"id"`
	NameZh string          `json:"name_zh"`
	NameUg string          `json:"name_ug"`
	Foods  []*FoodResource `json:"foods"`
}

func (rc *CategoryResource) Make(item *model.FoodCategoryModel) *CategoryResource {
	if item == nil || item.Foods == nil || len(item.Foods) == 0 {
		return nil
	}
	data := CategoryResource{
		ID:     item.ID,
		NameZh: item.NameZh,
		NameUg: item.NameUg,
		Foods:  []*FoodResource{},
	}
	if item.Foods != nil {
		foodResource := FoodResource{}
		data.Foods = foodResource.Collection(item.Foods)
	}
	return &data
}

func (rc *CategoryResource) Collection(items []*model.FoodCategoryModel) []*CategoryResource {

	if items == nil || len(items) == 0 {
		return []*CategoryResource{}
	}
	data := make([]*CategoryResource, 0, len(items))
	for _, food := range items {
		item := rc.Make(food)
		if item != nil {
			data = append(data, item)
		}
	}
	return data
}

type FoodResource struct {
	ID             int64              `json:"id"`
	NameZh         string             `json:"name_zh"`
	NameUg         string             `json:"name_ug"`
	FoodCategoryID int64              `json:"food_category_id"`
	Printers       []*PrinterResource `json:"printers"`
}

func (rc *FoodResource) Make(item *model.FoodModel) *FoodResource {
	if item == nil {
		return nil
	}
	data := FoodResource{
		ID:             item.ID,
		NameZh:         item.NameZh,
		NameUg:         item.NameUg,
		FoodCategoryID: item.FoodCategoryID,
		Printers:       []*PrinterResource{},
	}
	if item.Printers != nil {
		printerResource := PrinterResource{}
		data.Printers = printerResource.Collection(item.Printers)
	}
	return &data
}

func (rc *FoodResource) Collection(items []*model.FoodModel) []*FoodResource {

	if items == nil || len(items) == 0 {
		return []*FoodResource{}
	}
	data := make([]*FoodResource, len(items))
	for i, food := range items {
		data[i] = rc.Make(food)
	}
	return data
}

type PrinterResource struct {
	Id     string `json:"id"`
	NameZh string `json:"name_zh"`
	NameUg string `json:"name_ug"`
}

func (rc *PrinterResource) Make(item *model.PrinterModel) *PrinterResource {
	if item == nil {
		return nil
	}
	data := PrinterResource{
		Id:     item.ID,
		NameZh: item.NameZh,
		NameUg: item.NameUg,
	}
	return &data
}

func (rc *PrinterResource) Collection(items []*model.PrinterModel) []*PrinterResource {

	if items == nil || len(items) == 0 {
		return []*PrinterResource{}
	}
	data := make([]*PrinterResource, len(items))
	for i, printer := range items {
		data[i] = rc.Make(printer)
	}
	return data
}

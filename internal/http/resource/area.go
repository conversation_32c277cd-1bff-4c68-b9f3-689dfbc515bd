package resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// AreaResource is the resource for area
type AreaResource struct {
	ID         int64   `json:"id"`          // Unique ID
	MerchantNo string  `json:"merchant_no"` // 商家编号
	NameUg     string  `json:"name_ug"`     // 名称(维语)
	NameZh     string  `json:"name_zh"`     // 名称(中文)
	Sort       int64   `json:"sort"`        // 排序
	State      int64   `json:"state"`       // 状态
	CreatedAt  *string `json:"created_at"`  // Create time
	UpdatedAt  *string `json:"updated_at"`  // Update time
	DeletedAt  *string `json:"deleted_at"`  // Delete time
}

func (rc *AreaResource) Make(item *model.AreaModel) *AreaResource {
	if item == nil {
		return nil
	}
	data := AreaResource{
		ID:         item.ID,
		MerchantNo: item.MerchantNo,
		NameUg:     item.NameUg,
		NameZh:     item.NameZh,
		Sort:       item.Sort,
		State:      item.State,
		CreatedAt:  util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:  util.FormatDateTime(&item.UpdatedAt),
		DeletedAt:  util.FormatDateTime(item.DeletedAt),
	}
	return &data
}

func (rc *AreaResource) Collection(items []*model.AreaModel) []*AreaResource {

	if items == nil || len(items) == 0 {
		return []*AreaResource{}
	}
	data := make([]*AreaResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

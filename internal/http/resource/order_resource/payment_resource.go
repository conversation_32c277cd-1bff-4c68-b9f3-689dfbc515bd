package order_resource

import (
	"context"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
)

type OrderPaymentResource struct {
	Id        int64  `json:"id"`
	PaymentId int64  `json:"payment_id"`
	Tag       string `json:"tag"`
	NameUg    string `json:"name_ug"`
	NameZh    string `json:"name_zh"`
	Type      int64  `json:"type"`
	PayMethod int64  `json:"pay_method"`
	Online    int64  `json:"online"`
	State     int64  `json:"state"` //0:付款中 1:已付款 2:付款失败 3:撤销付款

	PaymentNo string  `json:"payment_no"`
	Amount    float64 `json:"amount"`
	OrderId   int64   `json:"order_id"`
	OrderNo   string  `json:"order_no"`

	User   string `json:"user"`
	UserId int64  `json:"user_id"`
}

func (rc *OrderPaymentResource) Make(ctx *context.Context, item *model.MerchantPaymentModel) *OrderPaymentResource {
	if item == nil {
		return nil
	}
	isZh := i18n.IsZh(ctx)
	data := OrderPaymentResource{
		Id:        item.ID,
		PaymentId: item.PaymentType.ID,
		Tag:       item.PaymentType.Tag,
		NameUg:    item.PaymentType.NameUg,
		NameZh:    item.PaymentType.NameZh,
		Type:      item.PaymentType.Type,
		PayMethod: item.PaymentType.PayMethod,
		Online:    item.PaymentType.Online,
		State:     item.Status,

		PaymentNo: item.PaymentNo,
		Amount:    util.DivideFloat(float64(item.Amount), 100),
		OrderId:   item.OrderID,
		OrderNo:   item.OrderNo,
		UserId:    item.CashierID,
	}
	if item.Cashier != nil {
		data.User = item.Cashier.NameUg
		if isZh {
			data.User = item.Cashier.NameZh
		}
	}
	return &data
}

func (rc *OrderPaymentResource) Collection(ctx *context.Context, items []*model.MerchantPaymentModel) []*OrderPaymentResource {

	if items == nil || len(items) == 0 {
		return []*OrderPaymentResource{}
	}
	data := make([]*OrderPaymentResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

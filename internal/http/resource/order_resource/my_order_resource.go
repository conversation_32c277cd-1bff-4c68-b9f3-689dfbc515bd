package order_resource

import (
	"context"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
)

// MyOrderListItemResource 我的订单列表
type MyOrderListItemResource struct {
	ID             int64   `json:"id"`
	OrderNo        string  `json:"order_no"`
	TableNo        string  `json:"table_no"`
	TableName      string  `json:"table_name"`
	CustomersCount int     `json:"customers_count"`
	Price          float64 `json:"price"`
	RefundPrice    float64 `json:"refund_price"`
	CreatedAt      string  `json:"created_at"`
	PaidAt         string  `json:"paid_at"`
}

func (rc *MyOrderListItemResource) Make(ctx *context.Context, item *model.OrderModel) *MyOrderListItemResource {
	if item == nil {
		return nil
	}
	isZh := i18n.IsZh(ctx)
	data := MyOrderListItemResource{
		ID:             item.ID,
		OrderNo:        item.No,
		TableNo:        "",
		CustomersCount: item.CustomersCount,
		Price:          item.Price,
		RefundPrice:    item.RefundPrice,
		CreatedAt:      item.CreatedAt.Format("2006-01-02 15:04:05"),
		PaidAt:         item.PaidAt.Format("2006-01-02 15:04:05"),
	}

	if item.Table != nil {
		data.TableNo = item.Table.No
		data.TableName = item.Table.NameUg
		if isZh {
			data.TableName = item.Table.NameZh
		}
	}

	return &data
}

func (rc *MyOrderListItemResource) Collection(ctx *context.Context, items []*model.OrderModel) []*MyOrderListItemResource {

	if items == nil || len(items) == 0 {
		return []*MyOrderListItemResource{}
	}
	data := make([]*MyOrderListItemResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

type MyOrderResource struct {
	ID              int64   `json:"id"`
	No              string  `json:"order_no" gorm:"no"`
	TableID         int64   `json:"table_id"`
	CustomersCount  int     `json:"customers_count"`
	FoodsCount      float64 `json:"foods_count"`
	OriginalPrice   float64 `json:"original_price"`
	VipPrice        float64 `json:"vip_price"`
	Price           float64 `json:"price"`
	IgnorePrice     float64 `json:"ignore_price"`
	RefundPrice     float64 `json:"refund_price"`
	TotalDiscount   float64 `json:"total_discount"`
	User            string  `json:"user"`
	UserID          int64   `json:"user_id"`
	Remarks         string  `json:"remarks"`
	CreatedAt       *string `json:"created_at"`
	PaidAt          *string `json:"paid_at"`
	CashierID       int64   `json:"cashier_id"`       //结账人id
	WechatUserID    int64   `json:"wechat_user_id"`   //微信用户id
	CashierName     string  `json:"cashier_name"`     //结账人
	GiveChange      float64 `json:"give_change"`      //找零金额
	CollectedAmount float64 `json:"collected_amount"` //收款金额

	TableName string `json:"table_name"`
	TableNo   string `json:"table_no"`

	OrderDetails  []*MyOrderDetailResource  `json:"order_details" gorm:"-"`
	CanceledFoods []*MyOrderDetailResource  `json:"canceled_foods" gorm:"-"`
	Payments      []*MyOrderPaymentResource `json:"payments" gorm:"-"`
	Refunds       []*MyOrderDetailResource  `json:"refunds" gorm:"-"`
}

func (rc *MyOrderResource) Make(ctx context.Context, item *model.OrderModel) *MyOrderResource {
	if item == nil {
		return nil
	}
	isZh := i18n.IsZh(&ctx)
	data := MyOrderResource{
		ID:              item.ID,
		No:              item.No,
		TableID:         item.TableID,
		CustomersCount:  item.CustomersCount,
		FoodsCount:      item.FoodsCount,
		OriginalPrice:   item.OriginalPrice,
		VipPrice:        item.VipPrice,
		Price:           item.Price,
		IgnorePrice:     item.IgnorePrice,
		RefundPrice:     item.RefundPrice,
		TotalDiscount:   util.Round(util.SubtractFloat(item.OriginalPrice, item.Price)),
		UserID:          item.UserID,
		Remarks:         *item.Remarks,
		CreatedAt:       util.FormatDateTime(item.CreatedAt),
		PaidAt:          util.FormatDateTime(item.PaidAt),
		CashierID:       *item.CashierID,
		WechatUserID:    *item.WechatUserID,
		GiveChange:      *item.GiveChange,
		CollectedAmount: *item.CollectedAmount,
		User:            "",
		OrderDetails:    []*MyOrderDetailResource{},
		CanceledFoods:   []*MyOrderDetailResource{},
		Payments:        []*MyOrderPaymentResource{},
		Refunds:         []*MyOrderDetailResource{},
	}
	if item.Creator != nil {
		data.User = item.Creator.NameUg
		if isZh {
			data.User = item.Creator.NameZh
		}
	}
	if item.Table != nil {
		data.TableNo = item.Table.No
		data.TableName = item.Table.NameUg
		if isZh {
			data.TableName = item.Table.NameZh
		}
	}
	orderDetailResource := MyOrderDetailResource{}
	if item.Details != nil {
		for _, detail := range item.Details {
			if detail.State == 4 {
				data.CanceledFoods = append(data.CanceledFoods, orderDetailResource.Make(ctx, detail))
			} else {
				data.OrderDetails = append(data.OrderDetails, orderDetailResource.Make(ctx, detail))
			}
		}
	}
	paymentResource := MyOrderPaymentResource{}
	if item.Payments != nil {
		data.Payments = paymentResource.Collection(&ctx, item.Payments)
	}

	if item.RefundLogs != nil {
		for _, log := range item.RefundLogs {
			data.Refunds = append(data.Refunds, orderDetailResource.MakeFromRefund(&ctx, log))
		}
	}

	return &data
}

func (rc *MyOrderResource) Collection(ctx context.Context, items []*model.OrderModel) []*MyOrderResource {

	if items == nil || len(items) == 0 {
		return []*MyOrderResource{}
	}
	data := make([]*MyOrderResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

// MyOrderDetailResource 我的订单详情
type MyOrderDetailResource struct {
	ID             int64                        `json:"id"`
	OrderDetailsID int64                        `json:"order_details_id"` //退菜订单的原始订单id
	MerchantNo     string                       `json:"merchant_no"`
	OrderID        int64                        `json:"order_id"`
	FoodID         int64                        `json:"food_id"`
	FoodsCount     float64                      `json:"foods_count"`
	CostPrice      float64                      `json:"cost_price"`
	OriginalPrice  float64                      `json:"original_price"`
	VipPrice       float64                      `json:"vip_price"`
	Price          float64                      `json:"price"`
	TotalPrice     float64                      `json:"total_price"`
	IsPrint        bool                         `json:"is_print"`
	User           string                       `json:"user"`
	UserID         int64                        `json:"user_id"`
	State          int                          `json:"state"` //订单状态 - 1 新订单（人数确定，未点菜）、2 未支付订单（已点菜，订单已提交）、3 已结账订单（已结账）、4 退菜、5 取消退菜 、6 被并单
	Remarks        *string                      `json:"remarks"`
	IsCombo        bool                         `json:"is_combo"`
	ComboInfo      util.TArray[model.ComboInfo] `json:"combo_info" gorm:"type:text"`
	IsSync         bool                         `json:"is_sync"`
	CreatedAt      *string                      `json:"created_at"`
	UpdatedAt      *string                      `json:"updated_at"`

	FoodName     string `json:"food_name"`
	FoodFormatID int    `json:"food_format_id"`
}

func (rc *MyOrderDetailResource) Make(ctx context.Context, item *model.OrderDetailModel) *MyOrderDetailResource {
	if item == nil {
		return nil
	}
	isZh := i18n.IsZh(&ctx)
	data := MyOrderDetailResource{
		ID:             item.ID,
		OrderDetailsID: 0,
		MerchantNo:     item.MerchantNo,
		OrderID:        item.OrderID,
		FoodID:         item.FoodID,
		FoodsCount:     item.FoodsCount,
		CostPrice:      item.CostPrice,
		OriginalPrice:  item.OriginalPrice,
		VipPrice:       item.VipPrice,
		Price:          item.Price,
		TotalPrice:     item.TotalPrice,
		IsPrint:        item.IsPrint,
		User:           "",
		UserID:         item.UserID,
		State:          item.State,
		Remarks:        item.Remarks,
		IsCombo:        item.IsCombo,
		IsSync:         false,
		CreatedAt:      util.FormatDateTime(item.CreatedAt),
		UpdatedAt:      util.FormatDateTime(item.UpdatedAt),

		FoodName:     "",
		FoodFormatID: 0,
	}
	if item.Creator != nil {
		data.User = item.Creator.NameUg
		if isZh {
			data.User = item.Creator.NameZh
		}
	}

	if item.Food != nil {
		data.FoodFormatID = item.Food.FormatID
		data.FoodName = item.Food.NameUg
		if isZh {
			data.FoodName = item.Food.NameZh
		}
	}
	return &data
}

func (rc *MyOrderDetailResource) MakeFromRefund(ctx *context.Context, item *model.RefundOrderLogModel) *MyOrderDetailResource {
	if item == nil {
		return nil
	}
	isZh := i18n.IsZh(ctx)
	data := MyOrderDetailResource{
		ID:             item.ID,
		OrderDetailsID: 0,
		MerchantNo:     item.MerchantNo,
		OrderID:        item.OrderID,
		FoodID:         item.FoodID,
		FoodsCount:     item.RefundCount,
		Price:          util.Round(item.RefundAmount / item.RefundCount),
		TotalPrice:     item.RefundAmount,
		User:           "",
		UserID:         item.CashierID,
		State:          consts.ORDER_DETAIL_STATE_REFUND,
		Remarks:        &item.Remarks,
		IsSync:         false,
		CreatedAt:      util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:      util.FormatDateTime(&item.UpdatedAt),

		FoodName:     "",
		FoodFormatID: 0,
	}
	if item.Cashier != nil {
		data.User = item.Cashier.NameUg
		if isZh {
			data.User = item.Cashier.NameZh
		}
	}
	if item.Food != nil {
		if i18n.IsZh(ctx) {
			data.FoodName = item.Food.NameZh
		} else {
			data.FoodName = item.Food.NameUg
		}
		data.FoodFormatID = item.Food.FormatID
	}
	return &data
}

func (rc *MyOrderDetailResource) Collection(ctx context.Context, items []*model.OrderDetailModel) []*MyOrderDetailResource {

	if items == nil || len(items) == 0 {
		return []*MyOrderDetailResource{}
	}
	data := make([]*MyOrderDetailResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

type MyOrderPaymentResource struct {
	ID            int64   `gorm:"primaryKey;autoIncrement" json:"id"` // 主键，自增
	OrderNo       string  `json:"order_no"`                           // 订单编号
	Icon          string  `json:"icon"`                               // 付款图标
	Name          string  `json:"name"`                               // 付款类型名称
	PaymentTypeID int64   `json:"payment_type_id"`                    // 付款类型ID
	PaymentNo     string  `json:"payment_no"`                         // 付款编号
	Amount        float64 `json:"amount"`                             // 付款金额
	PayType       string  `json:"pay_type"`                           // 付款类型
	Remark        *string `json:"remark"`                             // 备注
	Status        int64   `json:"status"`                             // 付款状态
	CashierID     int64   `json:"cashier_id"`                         // 收银员ID
	PaidAt        *string `json:"paid_at"`                            // 付款时间
	RefundAmount  float64 `json:"refund_amount"`                      // 退款金额
	CreatedAt     *string `json:"created_at"`                         // 创建时间
}

func (rc *MyOrderPaymentResource) Make(ctx *context.Context, item *model.MerchantPaymentModel) *MyOrderPaymentResource {
	if item == nil {
		return nil
	}
	data := MyOrderPaymentResource{
		ID:            item.ID,
		OrderNo:       item.OrderNo,
		PaymentTypeID: item.PaymentTypeID,
		PaymentNo:     item.PaymentNo,
		Amount:        util.DivideFloat(float64(item.Amount), 100),       // 订单金额单位为分，需要除以100
		RefundAmount:  util.DivideFloat(float64(item.RefundAmount), 100), // 订单金额单位为分，需要除以100
		PayType:       item.PayType,
		Status:        item.Status,
		CashierID:     item.CashierID,
		PaidAt:        util.FormatDateTime(item.PaidAt),
		CreatedAt:     util.FormatDateTime(&item.CreatedAt),
	}
	if item.PaymentType != nil {
		if i18n.IsZh(ctx) {
			data.Name = item.PaymentType.NameZh
		} else {
			data.Name = item.PaymentType.NameUg
		}
		data.Icon = config.C.Storage.OSS.Endpoint + item.PaymentType.Icon
	}
	return &data
}

func (rc *MyOrderPaymentResource) Collection(ctx *context.Context, items []*model.MerchantPaymentModel) []*MyOrderPaymentResource {

	if items == nil || len(items) == 0 {
		return []*MyOrderPaymentResource{}
	}
	data := make([]*MyOrderPaymentResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

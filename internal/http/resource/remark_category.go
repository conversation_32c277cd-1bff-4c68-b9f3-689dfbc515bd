package resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type RemarkCategoryResource struct {
	ID        int64   `json:"id"`         // Unique ID
	Tag       string  `json:"tag"`        // 标签
	NameUg    string  `json:"name_ug"`    // 名称(维语)
	NameZh    string  `json:"name_zh"`    // 名称(中文)
	CreatedAt *string `json:"created_at"` // Create time
	UpdatedAt *string `json:"updated_at"` // Update time
}

func (rc *RemarkCategoryResource) Make(item *model.RemarkCategoryModel) *RemarkCategoryResource {
	if item == nil {
		return nil
	}
	data := RemarkCategoryResource{
		ID:        item.ID,
		Tag:       item.Tag,
		NameUg:    item.NameUg,
		NameZh:    item.NameZh,
		CreatedAt: util.FormatDateTime(&item.CreatedAt),
		UpdatedAt: util.FormatDateTime(&item.UpdatedAt),
	}
	return &data
}

func (rc *RemarkCategoryResource) Collection(items []*model.RemarkCategoryModel) []*RemarkCategoryResource {

	if items == nil {
		return []*RemarkCategoryResource{}
	}
	data := make([]*RemarkCategoryResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

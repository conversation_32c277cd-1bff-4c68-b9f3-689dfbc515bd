package resource

import (
	"ros-api-go/internal/config"
	"ros-api-go/internal/model"
)

// FoodForDualScreenResource 双屏显示食品列表资源转换器
type FoodForDualScreenResource struct {
	ID       int64   `json:"id"`        // 食品ID
	NameUg   string  `json:"name_ug"`   // 名称(维语)
	NameZh   string  `json:"name_zh"`   // 名称(中文)
	Image    string  `json:"image"`     // 图片
	VipPrice float64 `json:"vip_price"` // 会员价
	Price    float64 `json:"price"`     // 现价
}

// Make 将食品模型转换为资源
func (rc *FoodForDualScreenResource) Make(item *model.FoodModel) *FoodForDualScreenResource {
	if item == nil {
		return nil
	}

	return &FoodForDualScreenResource{
		ID:       item.ID,
		NameUg:   item.NameUg,
		NameZh:   item.NameZh,
		Image:    config.C.Storage.OSS.Endpoint + item.Image,
		VipPrice: item.VipPrice,
		Price:    item.Price,
	}
}

// Collection 将食品模型集合转换为资源集合
func (rc *FoodForDualScreenResource) Collection(items []*model.FoodModel) []*FoodForDualScreenResource {
	if items == nil || len(items) == 0 {
		return []*FoodForDualScreenResource{}
	}

	data := make([]*FoodForDualScreenResource, len(items))
	for i, food := range items {
		data[i] = rc.Make(food)
	}
	return data
}

// DefaultImageResource 双屏显示食品列表资源转换器
type DefaultImageResource struct {
	ID       int64  `json:"id"`        // 食品ID
	ImageURL string `json:"image_url"` // 图片
}

// Make 将食品模型转换为资源
func (rc *DefaultImageResource) Make(item *model.FoodsImageModel) *DefaultImageResource {
	if item == nil {
		return nil
	}

	return &DefaultImageResource{
		ID:       item.ID,
		ImageURL: config.C.Storage.OSS.Endpoint + item.ImageURL,
	}
}

// Collection 将食品模型集合转换为资源集合
func (rc *DefaultImageResource) Collection(items []*model.FoodsImageModel) []*DefaultImageResource {
	if items == nil || len(items) == 0 {
		return []*DefaultImageResource{}
	}

	data := make([]*DefaultImageResource, len(items))
	for i, food := range items {
		data[i] = rc.Make(food)
	}
	return data
}

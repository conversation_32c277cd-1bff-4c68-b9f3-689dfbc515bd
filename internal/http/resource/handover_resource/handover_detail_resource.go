package handover_resource

import (
	"context"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
)

// HandoverDetailResource is the resource for area
type HandoverDetailResource struct {
	ID                      int64   `json:"id"`                                   // Unique ID
	MerchantNo              string  `json:"merchant_no"`                          // 商家编号
	UserID                  int64   `json:"user_id"`                              // 交班用户编号
	HandoverLogID           int64   `json:"handover_log_id"`                      // 交班记录编号
	PaymentTypeID           int64   `json:"payment_type_id"`                      // 支付类型编号
	PaymentType             string  `json:"payment_type"`                         // 支付类型名称
	OrderCount              int     `json:"order_count"`                          // 订单数量
	OrderAmount             float64 `json:"order_amount"`                         // 订单金额
	RefundAmount            float64 `json:"refund_amount"`                        // 退款金额
	VipCount                int     `json:"vip_count"`                            // 新增会员数量
	VipRechargeAmount       float64 `json:"vip_recharge_amount"`                  // 会员充值金额
	DebtRepaymentAmount     float64 `json:"debt_repayment_amount"`                // 赊账还款金额
	Total                   float64 `json:"total"`                                // 总金额
	OrderCashAmount         float64 `json:"order_cash_amount,omitempty"`          // 订单现金金额
	VipCashRechargeAmount   float64 `json:"vip_cash_recharge_amount,omitempty"`   // 会员现金充值金额
	RefundCashAmount        float64 `json:"refund_cash_amount,omitempty"`         // 退款金额
	DebtRepaymentCashAmount float64 `json:"debt_repayment_cash_amount,omitempty"` // 退款金额
}

func (rc *HandoverDetailResource) Make(ctx *context.Context, item *model.HandoverDetailModel) *HandoverDetailResource {
	if item == nil {
		return nil
	}

	data := HandoverDetailResource{
		ID:                      item.ID,
		MerchantNo:              item.MerchantNo,
		UserID:                  item.UserID,
		HandoverLogID:           item.HandoverLogID,
		PaymentTypeID:           item.PaymentTypeID,
		OrderCount:              item.OrderCount,
		OrderAmount:             item.OrderAmount,
		RefundAmount:            item.RefundAmount,
		VipCount:                item.VipCount,
		VipRechargeAmount:       item.VipRechargeAmount,
		DebtRepaymentAmount:     item.DebtRepaymentAmount,
		Total:                   util.SubtractFloat(util.AddFloatMore(item.OrderAmount, item.VipRechargeAmount, item.DebtRepaymentAmount), item.RefundAmount),
		OrderCashAmount:         item.OrderCashAmount,
		VipCashRechargeAmount:   item.VipCashRechargeAmount,
		RefundCashAmount:        item.RefundCashAmount,
		DebtRepaymentCashAmount: item.DebtRepaymentCashAmount,
	}
	if item.PaymentType != nil {
		if i18n.IsZh(ctx) {
			data.PaymentType = item.PaymentType.NameZh
		} else {
			data.PaymentType = item.PaymentType.NameUg
		}
	}
	return &data
}

func (rc *HandoverDetailResource) Collection(ctx *context.Context, items []*model.HandoverDetailModel) []*HandoverDetailResource {

	if items == nil || len(items) == 0 {
		return []*HandoverDetailResource{}
	}
	data := make([]*HandoverDetailResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

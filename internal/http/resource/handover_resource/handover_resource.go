package handover_resource

import "ros-api-go/internal/common/schema"

type HandoverResource struct {
	BusinessSituation *BusinessSituationResource `json:"business_situation"`
	Overview          *schema.HandoverOverview   `json:"overview"`
	Vip               *VipResource               `json:"vip"`
	Debt              *DebtHolderResource        `json:"debt"`
	CashInfo          []*HandoverDetailResource  `json:"cashInfo"`
}

type BusinessSituationResource struct {
	OrderCount       int     `json:"order_count"`
	CustomerCount    int     `json:"customer_count"`
	ReceivableAmount float64 `json:"receivable_amount"`
	RealPaidAmount   float64 `json:"real_paid_amount"`
	TotalDiscount    float64 `json:"total_discount"`
	OrderAvg         float64 `json:"order_avg"`
	CustomerAvg      float64 `json:"customer_avg"`
}

type VipResource struct {
	VipCount              int64   `json:"vip_count"`
	RechargeAmount        float64 `json:"recharge_amount"`
	PresentRechargeAmount float64 `json:"present_recharge_amount"`
}

type DebtHolderResource struct {
	HolderCount     int64   `json:"holder_count"`
	RepaymentAmount float64 `json:"repayment_amount"`
}

package resource

import (
	"ros-api-go/internal/config"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// TerminalResource represents the terminal resource for API responses
type TerminalResource struct {
	ID                  uint    `json:"id"`
	TerminalType        uint8   `json:"terminal_type"`         // 终端类型（1 收银端、 2 点菜端）
	Name                string  `json:"name"`                  // 终端名称
	NameUg              string  `json:"name_ug"`               // 终端名称
	NameZh              string  `json:"name_zh"`               // 终端名称
	OsType              uint8   `json:"os_type"`               // 操作系统类型(1表示Android、2表示iOS、3表示windows)
	Icon                string  `json:"icon"`                  // 终端图标
	Version             string  `json:"version"`               // 终端版本
	VersionCode         uint    `json:"version_code"`          // 终端版本号
	URL                 string  `json:"url"`                   // 终端下载地址
	Sha512              *string `json:"sha512,omitempty"`      // 客户端sha512值
	Size                *int    `json:"size,omitempty"`        // 大小
	HtmlURL             string  `json:"html_url"`              // 终端下载地址
	Description         string  `json:"description"`           // 终端说明
	MinSupportedVersion int     `json:"min_supported_version"` // 最低支持版本号
	State               uint8   `json:"state"`                 // 状态（0关闭，1开通）
	CreatedAt           *string `json:"created_at,omitempty"`
	UpdatedAt           *string `json:"updated_at,omitempty"`
}

// Make creates a TerminalResource from a TerminalModel
func (rc *TerminalResource) Make(item *model.TerminalModel) *TerminalResource {
	if item == nil {
		return nil
	}

	return &TerminalResource{
		ID:                  item.ID,
		TerminalType:        item.TerminalType,
		Name:                item.Name,
		NameUg:              item.NameUg,
		NameZh:              item.NameZh,
		OsType:              item.OsType,
		Icon:                item.Icon,
		Version:             item.Version,
		VersionCode:         item.VersionCode,
		URL:                 config.C.Storage.OSS.Endpoint + item.URL,
		Sha512:              item.Sha512,
		Size:                item.Size,
		HtmlURL:             item.HtmlURL,
		Description:         item.Description,
		MinSupportedVersion: item.MinSupportedVersion,
		State:               item.State,
		CreatedAt:           util.FormatDateTime(item.CreatedAt),
		UpdatedAt:           util.FormatDateTime(item.UpdatedAt),
	}
}

// Collection creates a slice of TerminalResource from a slice of TerminalModel
func (rc *TerminalResource) Collection(items []*model.TerminalModel) []*TerminalResource {
	if items == nil || len(items) == 0 {
		return []*TerminalResource{}
	}

	result := make([]*TerminalResource, len(items))
	for i, item := range items {
		result[i] = rc.Make(item)
	}

	return result
}

package scan_resource

import (
	"fmt"
	"ros-api-go/internal/config"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type ScanOrderResource struct {
	ID          int64   `json:"id"`            // 订单ID
	OrderNo     string  `json:"order_no"`      // 订单号
	OpenID      *string `json:"open_id"`       // 用户OpenID
	Price       string  `json:"price"`         // 价格
	State       int64   `json:"state"`         // 订单状态
	PaidAt      *string `json:"paid_at"`       // 支付时间
	CreatedAt   *string `json:"created_at"`    // 创建时间
	UpdatedAt   *string `json:"updated_at"`    // 更新时间
	No          string  `json:"no"`            // 商家编号
	NameZh      string  `json:"name_zh"`       // 商家名称中文
	NameUg      string  `json:"name_ug"`       // 商家名称维文
	Logo        string  `json:"logo"`          // 商家Logo
	AddressZh   string  `json:"address_zh"`    // 地址中文
	AddressUg   string  `json:"address_ug"`    // 地址维文
	TableNameZh string  `json:"table_name_zh"` // 餐桌名称中文
	TableNameUg string  `json:"table_name_ug"` // 餐桌名称维文
}

func (rc *ScanOrderResource) Make(model *model.OrderModel) *ScanOrderResource {
	paidAt := util.FormatDateTime(model.PaidAt)
	createdAt := util.FormatDateTime(model.CreatedAt)
	updatedAt := util.FormatDateTime(model.UpdatedAt)

	data := ScanOrderResource{
		ID:        model.ID,
		OrderNo:   model.No,
		OpenID:    model.OpenID,
		Price:     fmt.Sprintf("%.2f", model.Price),
		State:     model.State,
		PaidAt:    paidAt,
		CreatedAt: createdAt,
		UpdatedAt: updatedAt,
	}
	if model.Merchant != nil {
		data.No = model.Merchant.No
		data.NameZh = model.Merchant.NameZh
		data.NameUg = model.Merchant.NameUg
		data.Logo = config.C.Storage.OSS.Endpoint + model.Merchant.Logo
		data.AddressZh = model.Merchant.AddressZh
		data.AddressUg = model.Merchant.AddressUg
	}
	if model.Table != nil {
		data.TableNameZh = model.Table.NameZh
		data.TableNameUg = model.Table.NameUg
	}
	return &data
}

func (rc *ScanOrderResource) Collection(items []*model.OrderModel) []*ScanOrderResource {

	if items == nil || len(items) == 0 {
		return []*ScanOrderResource{}
	}
	data := make([]*ScanOrderResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

type ScanOrderDetailResource struct {
	ID           int64   `json:"id"`             // 订单ID
	OrderID      int64   `json:"order_id"`       // 订单号
	Price        string  `json:"price"`          // 价格
	FoodID       string  `json:"food_id"`        // 菜品ID
	FoodNameZh   string  `json:"food_name_zh"`   // 菜品名称中文
	FoodNameUg   string  `json:"food_name_ug"`   // 菜品名称维文
	FoodImage    string  `json:"food_image"`     // 菜品图片
	FoodsCount   float64 `json:"foods_count"`    // 菜品数量
	FoodFormatID int64   `json:"food_format_id"` // 菜品规格ID
	Remarks      *string `json:"remarks"`        // 备注
}

func (rc *ScanOrderDetailResource) Make(model *model.OrderDetailModel) *ScanOrderDetailResource {
	data := ScanOrderDetailResource{
		ID:         model.ID,
		OrderID:    model.OrderID,
		Price:      fmt.Sprintf("%.2f", model.Price),
		FoodsCount: model.FoodsCount,
		Remarks:    model.Remarks,
	}
	if model.Food != nil {
		data.FoodID = fmt.Sprintf("%d", model.Food.ID)
		data.FoodNameZh = model.Food.NameZh
		data.FoodNameUg = model.Food.NameUg
		data.FoodImage = config.C.Storage.OSS.Endpoint + model.Food.Image
	}
	return &data
}

func (rc *ScanOrderDetailResource) Collection(items []*model.OrderDetailModel) []*ScanOrderDetailResource {

	if items == nil || len(items) == 0 {
		return []*ScanOrderDetailResource{}
	}
	data := make([]*ScanOrderDetailResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

type ScanPaymentRes struct {
	OrderNo       string `json:"order_no"`
	PaymentNo     string `json:"payment_no"`
	PaymentTypeId int    `json:"payment_type_id"`
	Amount        int    `json:"amount"`
}

package resource

import (
	"ros-api-go/internal/model"
)

// FoodSpecFoodResource 规格关联美食资源
type FoodSpecFoodResource struct {
	ID               int64   `json:"id"`                  // 美食ID
	MerchantNo       string  `json:"merchant_no"`         // 商家编号
	Pid              int64   `json:"pid"`                 // 上级美食ID
	ParentFoodNameZh string  `json:"parent_food_name_zh"` // 上级美食名称(中文)
	ParentFoodNameUg string  `json:"parent_food_name_ug"` // 上级美食名称(维语)
	SpecID           int64   `json:"spec_id"`             // 规格ID
	FoodCategoryID   int64   `json:"food_category_id"`    // 分类ID
	CategoryNameZh   string  `json:"category_name_zh"`    // 分类名称(中文)
	CategoryNameUg   string  `json:"category_name_ug"`    // 分类名称(维语)
	Image            string  `json:"image"`               // 图片
	ShortcutCode     string  `json:"shortcut_code"`       // 快捷码
	NameUg           string  `json:"name_ug"`             // 名称(维语)
	NameZh           string  `json:"name_zh"`             // 名称(中文)
	CostPrice        float64 `json:"cost_price"`          // 定价
	VipPrice         float64 `json:"vip_price"`           // 会员价格
	Price            float64 `json:"price"`               // 现价
	FormatID         int     `json:"format_id"`           // 计费方式
	IsSpecialFood    bool    `json:"is_special_food"`     // 是否特色菜
	SupportScanOrder bool    `json:"support_scan_order"`  // 是否支持扫码点单
	Sort             int64   `json:"sort"`                // 排序
	State            int64   `json:"state"`               // 状态
	CreatedAt        string  `json:"created_at"`          // 创建时间
	UpdatedAt        string  `json:"updated_at"`          // 更新时间
}

// Make 转换单个美食模型为资源
func (rc *FoodSpecFoodResource) Make(item *model.FoodModel) *FoodSpecFoodResource {
	if item == nil {
		return nil
	}

	resource := &FoodSpecFoodResource{
		ID:             item.ID,
		MerchantNo:     item.MerchantNo,
		Pid:            item.Pid,
		SpecID:         item.SpecID,
		FoodCategoryID: item.FoodCategoryID,
		ShortcutCode:   item.ShortcutCode,
		NameUg:         item.NameUg,
		NameZh:         item.NameZh,
		CostPrice:      item.CostPrice,
		VipPrice:       item.VipPrice,
		Price:          item.Price,
		FormatID:       item.FormatID,
		Sort:           item.Sort,
		State:          item.State,
	}

	// 设置上级美食信息
	if item.ParentFood != nil {
		resource.ParentFoodNameZh = item.ParentFood.NameZh
		resource.ParentFoodNameUg = item.ParentFood.NameUg
	}

	// 设置分类信息
	if item.FoodCategory != nil {
		resource.CategoryNameZh = item.FoodCategory.NameZh
		resource.CategoryNameUg = item.FoodCategory.NameUg
	}

	return resource
}

// Collection 转换美食模型集合为资源集合
func (rc *FoodSpecFoodResource) Collection(items []*model.FoodModel) []*FoodSpecFoodResource {
	if items == nil || len(items) == 0 {
		return []*FoodSpecFoodResource{}
	}

	data := make([]*FoodSpecFoodResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

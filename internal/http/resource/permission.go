package resource

import (
	"ros-api-go/internal/model"
)

type PermissionResource struct {
	V0 string `json:"v0"`
	V1 string `json:"v1"`
	V2 string `json:"v2"`
}

func (rc *PermissionResource) Make(item *model.Permission) *PermissionResource {
	if item == nil {
		return nil
	}
	data := PermissionResource{
		V0: item.V0,
		V1: item.V1,
		V2: item.V2,
	}
	return &data
}

func (rc *PermissionResource) Collection(items []*model.Permission) []*PermissionResource {

	if items == nil || len(items) == 0 {
		return []*PermissionResource{}
	}
	data := make([]*PermissionResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

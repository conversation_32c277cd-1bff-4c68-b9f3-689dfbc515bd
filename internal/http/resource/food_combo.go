package resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type FoodComboResource struct {
	ID          int64   `json:"id" gorm:"size:20;primaryKey;"` // Unique ID
	MerchantNo  string  `json:"merchant_no" gorm:"size:20;"`   // 商家编号
	ComboId     int64   `json:"combo_id" gorm:"size:20;"`      // 套餐 ID
	Type        string  `json:"type" gorm:"size:20;"`          // 类型
	ParentId    int64   `json:"parent_id" gorm:"size:20;"`     // 父级 ID
	NameUg      string  `json:"name_ug" gorm:"size:50;index;"` // 名称(维语)
	NameZh      string  `json:"name_zh" gorm:"size:50;index;"` // 名称(中文)
	FoodId      int64   `json:"food_id" gorm:"size:20;"`       // 菜品 ID
	OriginPrice float64 `json:"origin_price" gorm:"index;"`    // 原价
	ComboPrice  float64 `json:"combo·price" gorm:"index;"`     // 套餐价
	Count       int     `json:"count" gorm:"index;"`           // 数量
	CreatedAt   *string `json:"created_at" gorm:"index;"`      // Create time
	UpdatedAt   *string `json:"updated_at" gorm:"index;"`      // Update time
	DeletedAt   *string `json:"deleted_at" gorm:"index;"`      // Delete time
}

func (rc *FoodComboResource) Make(item *model.FoodComboModel) *FoodComboResource {
	if item == nil {
		return nil
	}
	data := FoodComboResource{
		ID:          item.ID,
		MerchantNo:  item.MerchantNo,
		ComboId:     item.ComboID,
		Type:        item.Type,
		ParentId:    item.ParentID,
		NameUg:      item.NameUg,
		NameZh:      item.NameZh,
		FoodId:      item.FoodID,
		OriginPrice: item.OriginPrice,
		ComboPrice:  item.ComboPrice,
		Count:       item.Count,
		CreatedAt:   util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:   util.FormatDateTime(&item.UpdatedAt),
		DeletedAt:   util.FormatDateTime(item.DeletedAt),
	}
	return &data
}

func (rc *FoodComboResource) Collection(items []*model.FoodComboModel) []*FoodComboResource {

	data := make([]*FoodComboResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

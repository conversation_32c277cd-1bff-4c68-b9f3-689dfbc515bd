package resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type MerchantRoleResource struct {
	ID          int64               `json:"id"`          // Unique ID
	MerchantNo  string              `json:"merchant_no"` // 商家编号
	NameUg      string              `json:"name_ug"`     // 名称(维语)
	NameZh      string              `json:"name_zh"`     // 名称(中文)
	State       int8                `json:"state"`       // 状态
	Permissions util.TArray[string] `json:"permissions"` // 权限
	CreatedAt   *string             `json:"created_at"`  // Create time
	UpdatedAt   *string             `json:"updated_at"`  // Update time
	DeletedAt   *string             `json:"deleted_at"`  // Delete time
}

func (rc *MerchantRoleResource) Make(item *model.MerchantRoleModel) *MerchantRoleResource {
	if item == nil {
		return nil
	}
	data := MerchantRoleResource{
		ID:          item.ID,
		MerchantNo:  item.MerchantNo,
		NameUg:      item.NameUg,
		NameZh:      item.NameZh,
		Permissions: item.Permissions,
		State:       item.State,
		CreatedAt:   util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:   util.FormatDateTime(&item.UpdatedAt),
		DeletedAt:   util.FormatDateTime(item.DeletedAt),
	}
	return &data
}

func (rc *MerchantRoleResource) Collection(items []*model.MerchantRoleModel) []*MerchantRoleResource {

	if items == nil || len(items) == 0 {
		return []*MerchantRoleResource{}
	}
	data := make([]*MerchantRoleResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

type MerchantRoleItemResource struct {
	ID         int64   `json:"id"`          // Unique ID
	MerchantNo string  `json:"merchant_no"` // 商家编号
	NameUg     string  `json:"name_ug"`     // 名称(维语)
	NameZh     string  `json:"name_zh"`     // 名称(中文)
	State      int8    `json:"state"`       // 状态
	CreatedAt  *string `json:"created_at"`  // Create time
	UpdatedAt  *string `json:"updated_at"`  // Update time
	DeletedAt  *string `json:"deleted_at"`  // Delete time
}

func (rc *MerchantRoleItemResource) Make(item *model.MerchantRoleModel) *MerchantRoleItemResource {
	if item == nil {
		return nil
	}
	data := MerchantRoleItemResource{
		ID:         item.ID,
		MerchantNo: item.MerchantNo,
		NameUg:     item.NameUg,
		NameZh:     item.NameZh,
		State:      item.State,
		CreatedAt:  util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:  util.FormatDateTime(&item.UpdatedAt),
		DeletedAt:  util.FormatDateTime(item.DeletedAt),
	}
	return &data
}

func (rc *MerchantRoleItemResource) Collection(items []*model.MerchantRoleModel) []*MerchantRoleItemResource {

	if items == nil || len(items) == 0 {
		return []*MerchantRoleItemResource{}
	}
	data := make([]*MerchantRoleItemResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

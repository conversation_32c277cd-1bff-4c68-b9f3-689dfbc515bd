package resource

import (
	"context"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
)

type LoginUserResource struct {
	ID                int64    `json:"id"`                 // Unique ID
	MerchantNo        string   `json:"merchant_no"`        // 商家编号
	Avatar            string   `json:"avatar"`             // 头像
	JobNo             string   `json:"job_no"`             // 员工编号
	Name              string   `json:"name"`               // 姓名
	Phone             string   `json:"phone"`              // 手机号
	Password          string   `json:"password"`           // 密码
	RoleID            int64    `json:"role_id"`            // 角色名称
	ApiToken          string   `json:"api_token"`          // 登录token
	State             int64    `json:"state"`              // 状态 - 1 开启 0 关闭
	IsOwner           bool     `json:"is_owner"`           // 是否为店长
	IsHandover        bool     `json:"is_handover"`        // 是否转交
	Permissions       []string `json:"permissions"`        // 权限列表
	LeavedAt          *string  `json:"leaved_at"`          // 离职时间
	LastLogin         *string  `json:"last_login"`         // 最后登录
	CreatedAt         *string  `json:"created_at"`         // 创建时间
	UpdatedAt         *string  `json:"updated_at"`         // 更新时间
	DeletedAt         *string  `json:"deleted_at"`         // 删除时间
	OperationPassword string   `json:"operation_password"` // 操作密码
}

func (rc *LoginUserResource) Make(ctx context.Context, item *model.UserModel) *LoginUserResource {
	if item == nil {
		return nil
	}

	data := LoginUserResource{
		ID:                item.ID,
		MerchantNo:        item.MerchantNo,
		Avatar:            item.Avatar,
		JobNo:             item.JobNo,
		Name:              item.NameUg,
		Phone:             item.Phone,
		Password:          item.Password,
		RoleID:            item.RoleID,
		ApiToken:          item.ApiToken,
		State:             item.State,
		IsOwner:           item.IsOwner,
		OperationPassword: item.OperationPassword,
		IsHandover:        rc.IsHandover,
		LeavedAt:          util.FormatDateTime(&item.LeavedAt),
		LastLogin:         util.FormatDateTime(&item.LastLogin),
		CreatedAt:         util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:         util.FormatDateTime(&item.UpdatedAt),
		DeletedAt:         util.FormatDateTime(item.DeletedAt),
	}
	isZh := i18n.IsZh(&ctx)
	if isZh && item.NameZh != "" {
		data.Name = item.NameZh
	}

	if item.Employee != nil && item.Employee.Roles != nil {
		// 店长直接返回空权限
		if item.IsOwner {
			data.Permissions = []string{consts.AdminPermission}
		} else {
			permissionSet := make(map[string]bool)
			for _, role := range item.Employee.Roles {
				for _, permission := range role.Permissions {
					permissionSet[permission] = true
				}
			}

			// 将 map 转换为切片
			uniquePermissions := make([]string, 0, len(permissionSet))
			for permission := range permissionSet {
				uniquePermissions = append(uniquePermissions, permission)
			}

			data.Permissions = uniquePermissions
		}
	}

	return &data
}

func (rc *LoginUserResource) Collection(ctx context.Context, items []*model.UserModel) []*LoginUserResource {

	if items == nil || len(items) == 0 {
		return []*LoginUserResource{}
	}
	data := make([]*LoginUserResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

type UserResource struct {
	ID         int64   `json:"id"`          // Unique ID
	MerchantNo string  `json:"merchant_no"` // 商家编号
	Avatar     string  `json:"avatar"`      // 头像
	JobNo      string  `json:"job_no"`      // 员工编号
	Name       string  `json:"name"`        // 姓名
	Phone      string  `json:"phone"`       // 手机号
	Password   string  `json:"password"`    // 密码
	RoleID     int64   `json:"role_id"`     // 角色名称
	ApiToken   string  `json:"api_token"`   // 登录token
	State      int64   `json:"state"`       // 状态 - 1 开启 0 关闭
	LeavedAt   *string `json:"leaved_at"`   // 离职时间
	LastLogin  *string `json:"last_login"`  // 最后登录
	CreatedAt  *string `json:"created_at"`  // 创建时间
	UpdatedAt  *string `json:"updated_at"`  // 更新时间
	DeletedAt  *string `json:"deleted_at"`  // 删除时间
}

func (rc *UserResource) Make(item *model.UserModel) *UserResource {
	if item == nil {
		return nil
	}

	data := UserResource{
		ID:        item.ID,
		Avatar:    item.Avatar,
		JobNo:     item.JobNo,
		Name:      item.Name,
		Phone:     item.Phone,
		Password:  item.Password,
		RoleID:    item.RoleID,
		ApiToken:  item.ApiToken,
		State:     item.State,
		LeavedAt:  util.FormatDateTime(&item.LeavedAt),
		LastLogin: util.FormatDateTime(&item.LastLogin),
		CreatedAt: util.FormatDateTime(&item.CreatedAt),
		UpdatedAt: util.FormatDateTime(&item.UpdatedAt),
		DeletedAt: util.FormatDateTime(item.DeletedAt),
	}
	return &data
}

func (rc *UserResource) Collection(items []*model.UserModel) []*UserResource {

	if items == nil || len(items) == 0 {
		return []*UserResource{}
	}
	data := make([]*UserResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

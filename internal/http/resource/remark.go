package resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type RemarkResource struct {
	ID         int64   `json:"id" gorm:"size:20;primaryKey;"` // Unique ID
	MerchantNo string  `json:"merchant_no" gorm:"size:20;"`   // 商家编号
	Tag        string  `json:"tag" gorm:"size:20;"`           // 标签
	NameUg     string  `json:"name_ug" gorm:"size:50;index;"` // 名称(维语)
	NameZh     string  `json:"name_zh" gorm:"size:50;index;"` // 名称(中文)
	Sort       int64   `json:"sort" gorm:"index;"`            // 排序
	State      int64   `json:"state" gorm:"size:10;index;"`   // 状态
	CreatedAt  *string `json:"created_at" gorm:"index;"`      // Create time
	UpdatedAt  *string `json:"updated_at" gorm:"index;"`      // Update time
	DeletedAt  *string `json:"deleted_at" gorm:"index;"`      // Delete time
}

func (rc *RemarkResource) Make(item *model.RemarkModel) *RemarkResource {
	if item == nil {
		return nil
	}
	data := RemarkResource{
		ID:         item.ID,
		MerchantNo: item.MerchantNo,
		Tag:        item.Tag,
		NameUg:     item.NameUg,
		NameZh:     item.NameZh,
		Sort:       item.Sort,
		State:      item.State,
		CreatedAt:  util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:  util.FormatDateTime(&item.UpdatedAt),
		DeletedAt:  util.FormatDateTime(item.DeletedAt),
	}
	return &data
}

func (rc *RemarkResource) Collection(items []*model.RemarkModel) []*RemarkResource {

	if items == nil {
		return []*RemarkResource{}
	}
	data := make([]*RemarkResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

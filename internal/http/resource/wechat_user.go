package resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type WechatUserBaseResource struct {
	ID        int64   `json:"id"`
	Mobile    string  `json:"mobile"`
	OpenID    string  `json:"open_id"`
	Points    int     `json:"points"`
	AreaID    int     `json:"area_id"`
	APIToken  string  `json:"api_token"`
	LastLogin *string `json:"last_login"`
}

func (rc *WechatUserBaseResource) Make(item *model.WechatUserModel) *WechatUserBaseResource {
	if item == nil {
		return nil
	}
	data := WechatUserBaseResource{
		ID:        item.ID,
		Mobile:    item.Mobile,
		OpenID:    item.OpenID,
		Points:    item.Points,
		AreaID:    item.AreaID,
		APIToken:  item.APIToken,
		LastLogin: util.FormatDateTime(item.LastLogin),
	}
	return &data
}

func (rc *WechatUserBaseResource) Collection(items []*model.WechatUserModel) []*WechatUserBaseResource {

	if items == nil || len(items) == 0 {
		return []*WechatUserBaseResource{}
	}
	data := make([]*WechatUserBaseResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

type WechatUserResource struct {
	ID             int64   `gorm:"column:id;primary_key"`
	OpenID         string  `gorm:"column:open_id"`
	Mobile         string  `gorm:"column:mobile"`
	Points         int     `gorm:"column:points"`
	AreaID         int     `gorm:"column:area_id"`
	APIToken       string  `gorm:"column:api_token"`
	LastLogin      *string `gorm:"column:last_login"`
	YinlianOpenID  string  `gorm:"column:yinlian_open_id"`
	YinlianUnionID string  `gorm:"column:yinlian_union_id"`
	AlipayOpenID   string  `gorm:"column:alipay_open_id"`
	CreatedAt      *string `gorm:"column:created_at"`
	UpdatedAt      *string `gorm:"column:updated_at"`
	DeletedAt      *string `gorm:"column:deleted_at"`
}

func (rc *WechatUserResource) Make(item *model.WechatUserModel) *WechatUserResource {
	if item == nil {
		return nil
	}
	data := WechatUserResource{
		ID:             item.ID,
		Mobile:         item.Mobile,
		OpenID:         item.OpenID,
		Points:         item.Points,
		AreaID:         item.AreaID,
		APIToken:       item.APIToken,
		YinlianOpenID:  item.YinlianOpenID,
		YinlianUnionID: item.YinlianUnionID,
		AlipayOpenID:   item.AlipayOpenID,
		LastLogin:      util.FormatDateTime(item.LastLogin),
		CreatedAt:      util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:      util.FormatDateTime(&item.UpdatedAt),
		DeletedAt:      util.FormatDateTime(item.DeletedAt),
	}
	return &data
}

func (rc *WechatUserResource) Collection(items []*model.WechatUserModel) []*WechatUserResource {

	if items == nil || len(items) == 0 {
		return []*WechatUserResource{}
	}
	data := make([]*WechatUserResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

package resource

import (
	"context"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/model"
)

// LoginResource 登录返回数据
type LoginResource struct {
	User        *LoginUserResource        `json:"user"`
	Permissions []string                  `json:"permissions"`
	Merchant    *MerchantResource         `json:"merchant"`
	ServerInfo  *LocalServerBasicResource `json:"server_info"`
	ServerToken string                    `json:"server_token,omitempty"`
	Token       *schema.LoginToken        `json:"token,omitempty"`
}

func (rc *LoginResource) Make(ctx context.Context, user *model.UserModel, permissions []string, merchant *model.MerchantModel,
	localServer *model.LocalServerModel, token *schema.LoginToken) *LoginResource {

	loginUserResource := LoginUserResource{}
	merchantResource := MerchantResource{}
	localServerBasicResource := LocalServerBasicResource{}

	return &LoginResource{
		User:        loginUserResource.Make(ctx, user),
		Permissions: permissions,
		Merchant:    merchantResource.Make(merchant),
		ServerInfo:  localServerBasicResource.Make(localServer),
		ServerToken: localServer.Token,
		Token:       token,
	}
}

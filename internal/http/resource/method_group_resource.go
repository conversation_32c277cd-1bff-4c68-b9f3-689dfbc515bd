package resource

import (
	"ros-api-go/internal/model"
	"time"
)

// MethodGroupResource 做法分组资源
type MethodGroupResource struct {
	ID         int64     `json:"id"`          // ID
	MerchantNo string    `json:"merchant_no"` // 商家编号
	Name       string    `json:"name"`        // 分组名称
	Sort       int64     `json:"sort"`        // 排序
	CreatedAt  time.Time `json:"created_at"`  // 创建时间
	UpdatedAt  time.Time `json:"updated_at"`  // 更新时间
}

// Transform 转换单个模型为资源
func (r *MethodGroupResource) Transform(methodGroup *model.MethodGroupModel) *MethodGroupResource {
	return &MethodGroupResource{
		ID:         methodGroup.ID,
		MerchantNo: methodGroup.MerchantNo,
		Name:       methodGroup.Name,
		Sort:       methodGroup.Sort,
		CreatedAt:  methodGroup.CreatedAt,
		UpdatedAt:  methodGroup.UpdatedAt,
	}
}

// Collection 转换模型集合为资源集合
func (r *MethodGroupResource) Collection(methodGroups []*model.MethodGroupModel) []*MethodGroupResource {
	resources := make([]*MethodGroupResource, 0, len(methodGroups))
	for _, methodGroup := range methodGroups {
		resources = append(resources, r.Transform(methodGroup))
	}
	return resources
}

package permission_response

import "ros-api-go/pkg/i18n"

type PermissionResource struct {
	Key     string                `json:"key"`
	Name_ug string                `json:"name_ug"`
	Name_zh string                `json:"name_zh"`
	Type    string                `json:"type"`
	Items   []*PermissionResource `json:"items,omitempty"`
}

func Make(resources []*PermissionResource) []*PermissionResource {
	for _, resource := range resources {
		if resource.Type == "group" {
			resource.Items = Make(resource.Items)
		}
		resource.Name_ug = i18n.MsgByLang("ug-CN", resource.Key)
		resource.Name_zh = i18n.MsgByLang("zh-CN", resource.Key)
	}
	return resources
}

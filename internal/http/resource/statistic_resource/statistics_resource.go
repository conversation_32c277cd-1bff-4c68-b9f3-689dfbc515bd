package statistic_resource

import (
	"context"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
)

type GraphStatisticsResource struct {
	Type               int                              `json:"type"`
	CustomerStatistics []schema.GraphCustomerStatistics `json:"customer_statistics"`
	BusinessStatistics []schema.GraphBusinessStatistics `json:"business_statistics"`
	PayTypeStatistics  []schema.GraphPayTypeStatistics  `json:"pay_type_statistics"`
}

type HandoverStatisticResource struct {
	List           []HandoverStatisticItemResource `json:"list"`
	SmallAggregate *schema.HandoverSmallAggregate  `json:"small_aggregate"`
}

type HandoverStatisticItemResource struct {
	ID                int64                              `json:"id"`
	StartAt           string                             `json:"start_at"`
	LeaveAt           string                             `json:"leave_at"`
	Name              string                             `json:"name"`
	AlternateAmount   float64                            `json:"alternate_amount"`
	WorkingBalance    float64                            `json:"working_balance"`
	PaidAmount        float64                            `json:"paid_amount"`
	SubmittedAmount   float64                            `json:"submitted_amount"`
	PaymentProportion []schema.HandoverPaymentProportion `json:"payment_proportion"`
}

func (r *HandoverStatisticResource) Make(ctx *context.Context, list []*model.HandoverLogModel) HandoverStatisticResource {
	data := HandoverStatisticResource{}
	data.List = make([]HandoverStatisticItemResource, 0)
	workingHours := 0.0
	totalAmount := 0.0
	for _, item := range list {

		workingHours += item.LeaveAt.Sub(item.StartAt).Hours()
		totalAmount = util.AddFloat(totalAmount, item.PaidAmount)

		shiftName := item.ShiftModel.NameZh
		if !i18n.IsZh(ctx) {
			shiftName = item.ShiftModel.NameUg
		}
		itemResource := HandoverStatisticItemResource{
			ID:                item.ID,
			StartAt:           item.StartAt.Format("2006-01-02 15:04:05"),
			LeaveAt:           item.LeaveAt.Format("2006-01-02 15:04:05"),
			Name:              shiftName,
			AlternateAmount:   item.AlternateAmount,
			WorkingBalance:    item.WorkingBalance,
			PaidAmount:        item.PaidAmount,
			SubmittedAmount:   item.SubmittedAmount,
			PaymentProportion: make([]schema.HandoverPaymentProportion, 0),
		}
		for _, detail := range item.HandoverDetails {
			paymentName := detail.PaymentType.NameZh
			if !i18n.IsZh(ctx) {
				paymentName = detail.PaymentType.NameUg
			}
			itemResource.PaymentProportion = append(itemResource.PaymentProportion, schema.HandoverPaymentProportion{
				ID:                detail.ID,
				MerchantNo:        detail.MerchantNo,
				UserID:            detail.UserID,
				HandoverLogID:     detail.HandoverLogID,
				PaymentTypeID:     detail.PaymentTypeID,
				PaymentType:       paymentName,
				OrderCount:        detail.OrderCount,
				OrderAmount:       detail.OrderAmount,
				RefundAmount:      detail.RefundAmount,
				VipCount:          detail.VipCount,
				VipRechargeAmount: detail.VipRechargeAmount,
				Total:             util.Round(util.AddFloat(util.SubtractFloat(detail.OrderAmount, detail.RefundAmount), detail.VipRechargeAmount)),
			})
		}
		data.List = append(data.List, itemResource)
	}
	cashierName := list[0].Cashier.NameZh
	if !i18n.IsZh(ctx) {
		cashierName = list[0].Cashier.NameUg
	}
	data.SmallAggregate = &schema.HandoverSmallAggregate{
		Name:         cashierName,
		ShiftCount:   len(list),
		TotalAmount:  util.Round(totalAmount),
		WorkingHours: int64(workingHours),
	}
	return data
}

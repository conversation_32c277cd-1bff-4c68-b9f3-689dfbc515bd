package resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type TableResource struct {
	ID              int64   `json:"id"`               // Unique ID
	MerchantNo      string  `json:"merchant_no"`      // 商家编号
	AreaId          int64   `json:"area_id"`          // 区域ID
	No              string  `json:"no"`               // 编号
	NameUg          string  `json:"name_ug"`          // 名称(维语)
	NameZh          string  `json:"name_zh"`          // 名称(中文)
	SeatingCapacity int     `json:"seating_capacity"` // 座位数
	Sort            int64   `json:"sort" `            // 排序
	State           int64   `json:"state"`            // 状态
	CreatedAt       *string `json:"created_at" `      // Create time
	UpdatedAt       *string `json:"updated_at" `      // Update time
	DeletedAt       *string `json:"deleted_at" `      // Delete time
}

func (rc *TableResource) Make(item *model.TableModel) *TableResource {
	if item == nil {
		return nil
	}
	data := TableResource{
		ID:              item.ID,
		MerchantNo:      item.MerchantNo,
		AreaId:          item.AreaID,
		No:              item.No,
		NameUg:          item.NameUg,
		NameZh:          item.NameZh,
		SeatingCapacity: item.SeatingCapacity,
		Sort:            item.Sort,
		State:           item.State,
		CreatedAt:       util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:       util.FormatDateTime(&item.UpdatedAt),
		DeletedAt:       util.FormatDateTime(item.DeletedAt),
	}
	return &data
}

func (rc *TableResource) Collection(items []*model.TableModel) []*TableResource {

	if items == nil || len(items) == 0 {
		return []*TableResource{}
	}
	data := make([]*TableResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

// TableDataResource 餐台详情数据
type TableDataResource struct {
	TableData []model.TableDataModel `json:"data"`       //餐桌
	TableInfo TableInfo              `json:"table_info"` //餐桌信息
	OrderInfo OrderInfo              `json:"order_info"` //订单信息
}

type TableInfo struct {
	EmptyTableCount        int `json:"emptyTableCount"`        //未开台餐桌数量
	HasCustomersTableCount int `json:"hasCustomersTableCount"` //开台餐桌数量
	HasOrderTableCount     int `json:"hasOrderTableCount"`     //已下订单餐桌数量
}
type OrderInfo struct {
	OrderCount  int     `json:"order_count"`  //订单数量
	OrderAmount float64 `json:"order_amount"` //订单价格
}

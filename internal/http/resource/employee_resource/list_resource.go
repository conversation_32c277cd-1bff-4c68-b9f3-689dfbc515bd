package employee_resource

import (
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// RoleResource 角色资源
type RoleResource struct {
	Id     int64  `json:"id"`
	NameUg string `json:"name_ug"`
	NameCn string `json:"name_cn"`
}

// NewRoleResource 创建角色资源
func NewRoleResource(roles []model.MerchantRoleModel) []RoleResource {
	var roleResources []RoleResource = make([]RoleResource, 0)
	for _, role := range roles {
		roleResource := RoleResource{
			Id:     role.ID,
			NameUg: role.NameUg,
			NameCn: role.NameZh,
		}
		roleResources = append(roleResources, roleResource)
	}
	return roleResources
}

// ListItemResource 商户员工列表资源
type ListItemResource struct {
	Id         int64          `json:"id"`
	MerchantNo string         `json:"merchant_no"`
	No         string         `json:"no"`
	Phone      string         `json:"phone"`
	UserID     int64          `json:"user_id"`
	NameUg     string         `json:"name_ug"`
	NameZh     string         `json:"name_zh"`
	IsOwner    bool           `json:"is_owner"`
	State      int8           `json:"state"`
	CreatedAt  util.Datetime  `json:"created_at" format:"2006-01-02 15:04:05"`
	UpdatedAt  util.Datetime  `json:"updated_at" format:"2006-01-02 15:04:05"`
	DeletedAt  *util.Datetime `json:"deleted_at" format:"2006-01-02 15:04:05"`
	Roles      []RoleResource `json:"roles"`
}

// NewListItemResource 创建商户员工列表资源
func NewListItemResource(employee model.MerchantEmployeeModel) ListItemResource {
	var deletedAt *util.Datetime
	if employee.DeletedAt != nil {
		t := util.Datetime(*employee.DeletedAt)
		deletedAt = &t
	}

	return ListItemResource{
		Id:         employee.ID,
		MerchantNo: employee.MerchantNo,
		No:         employee.No,
		Phone:      employee.User.Phone,
		UserID:     employee.UserID,
		NameUg:     employee.NameUg,
		NameZh:     employee.NameZh,
		IsOwner:    employee.IsOwner,
		State:      employee.State,
		CreatedAt:  util.Datetime(employee.CreatedAt),
		UpdatedAt:  util.Datetime(employee.UpdatedAt),
		DeletedAt:  deletedAt,
		Roles:      NewRoleResource(employee.Roles),
	}
}

// EmployeeListResource 商户员工列表资源
type EmployeeListResource struct {
	Items    []ListItemResource `json:"items"`
	Total    int64              `json:"total"`
	Page     int                `json:"page"`
	PageSize int                `json:"page_size"`
}

// NewEmployeeListResource 创建商户员工列表资源
func NewEmployeeListResource(items []model.MerchantEmployeeModel, total int64, page int, pageSize int) resource.PageResult {
	if items == nil {
		return resource.NewPageResult("", make([]ListItemResource, 0), int(total), page, pageSize)
	}
	var listItemResources []ListItemResource
	for _, item := range items {
		listItemResources = append(listItemResources, NewListItemResource(item))
	}
	return resource.NewPageResult("", listItemResources, int(total), page, pageSize)
}

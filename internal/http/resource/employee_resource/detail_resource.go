package employee_resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type EmployeeDetailResource struct {
	ListItemResource
	Phone string `json:"phone"`
}

// NewUserDetailResource 员工详情
// 员工详情
// 参数：
//   - employee: 商户员工信息
//
// 返回：
//   - UserDetailResource: 员工详情资源

func NewEmployeeDetailResource(employee model.MerchantEmployeeModel) EmployeeDetailResource {
	res := EmployeeDetailResource{
		Phone: employee.User.Phone,
	}
	res.Id = employee.ID

	res.Roles = NewRoleResource(employee.Roles)
	res.MerchantNo = employee.MerchantNo
	res.No = employee.No
	res.UserID = employee.UserID
	res.NameUg = employee.NameUg
	res.NameZh = employee.NameZh
	res.IsOwner = employee.IsOwner
	res.State = employee.State
	res.CreatedAt = util.Datetime(employee.CreatedAt)
	res.UpdatedAt = util.Datetime(employee.UpdatedAt)

	return res
}

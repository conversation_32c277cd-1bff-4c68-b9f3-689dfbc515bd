package resource

type Meta struct {
	CurrentPage int `json:"current_page"`
	Total       int `json:"total"`
	PerPage     int `json:"per_page"`
}
type PageResult struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
	Meta    Meta        `json:"meta"`
}

func NewPageResult(message string, data interface{}, total int, page int, limit int) PageResult {
	return PageResult{
		Message: message,
		Data:    data,
		Meta: Meta{
			CurrentPage: page,
			Total:       total,
			PerPage:     limit,
		},
	}
}

package resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type MerchantInfoUpdateResource struct {
	MerchantNo           string  `json:"merchant_no"`             // 商家编号
	UserUpdatedAt        *string `json:"user_updated_at"`         // 用户信息更新时间
	AreaUpdatedAt        *string `json:"area_updated_at"`         // 餐厅区域更新时间
	TableUpdatedAt       *string `json:"table_updated_at"`        // 餐桌信息更新时间
	FoodUpdatedAt        *string `json:"food_updated_at"`         // 菜品信息更新时间
	RemarkUpdatedAt      *string `json:"remark_updated_at"`       // 备注信息更新时间
	PaymentTypeUpdatedAt *string `json:"payment_type_updated_at"` // 支付方式更新时间
	PrinterUpdatedAt     *string `json:"printer_updated_at"`      // 打印机信息更新时间
	FoodPrinterUpdatedAt *string `json:"food_printer_updated_at"` // 菜品打印机信息更新时间
	PermissionUpdatedAt  *string `json:"permission_updated_at"`   // 权限信息更新时间
	CreatedAt            *string `json:"created_at"`              // 创建时间
	UpdatedAt            *string `json:"updated_at"`              // 更新时间
}

func (rc *MerchantInfoUpdateResource) Make(item *model.MerchantInfoUpdateModel) *MerchantInfoUpdateResource {
	if item == nil {
		return nil
	}
	data := MerchantInfoUpdateResource{
		MerchantNo:           item.MerchantNo,
		UserUpdatedAt:        util.FormatDateTime(&item.UserUpdatedAt),
		AreaUpdatedAt:        util.FormatDateTime(&item.AreaUpdatedAt),
		TableUpdatedAt:       util.FormatDateTime(&item.TableUpdatedAt),
		FoodUpdatedAt:        util.FormatDateTime(&item.FoodUpdatedAt),
		RemarkUpdatedAt:      util.FormatDateTime(&item.RemarkUpdatedAt),
		PaymentTypeUpdatedAt: util.FormatDateTime(&item.PaymentTypeUpdatedAt),
		PrinterUpdatedAt:     util.FormatDateTime(&item.PrinterUpdatedAt),
		FoodPrinterUpdatedAt: util.FormatDateTime(&item.FoodPrinterUpdatedAt),
		PermissionUpdatedAt:  util.FormatDateTime(&item.PermissionUpdatedAt),
		CreatedAt:            util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:            util.FormatDateTime(&item.UpdatedAt),
	}
	return &data
}

func (rc *MerchantInfoUpdateResource) Collection(items []*model.MerchantInfoUpdateModel) []*MerchantInfoUpdateResource {
	if items == nil || len(items) == 0 {
		return []*MerchantInfoUpdateResource{}
	}

	data := make([]*MerchantInfoUpdateResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

package resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/websocket"
)

type LocalServerBasicResource struct {
	ID    int64  `json:"client_id"` // id
	Ipv4  string `json:"ipv4"`      // ipv4
	Ipv6  string `json:"ipv6"`      // ipv6
	Token string `json:"-"`         // 授权码
	WsUrl string `json:"ws_url"`    // websocket uri
}

func (rc *LocalServerBasicResource) Make(item *model.LocalServerModel) *LocalServerBasicResource {
	if item == nil {
		return nil
	}
	return &LocalServerBasicResource{
		ID:    item.ID,
		Ipv4:  item.Ipv4,
		Ipv6:  item.Ipv6,
		Token: item.Token,
		WsUrl: websocket.GetServerUrl(),
	}
}

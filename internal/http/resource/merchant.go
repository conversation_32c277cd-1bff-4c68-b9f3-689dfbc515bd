package resource

import (
	"ros-api-go/internal/config"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type MerchantResource struct {
	ID                int64    `json:"id" gorm:"size:20;primaryKey;"` // 自增编号
	AddressUg         string   `json:"address_ug"`                    // 维文地址
	AddressZh         string   `json:"address_zh"`                    // 中文地址
	BusinessLicense   string   `json:"business_license"`              // 营业执照
	DiancaiPay        int      `json:"diancai_pay"`                   // 点菜端是否可以现金结账，是否支持收银端收现金
	ExpiredAt         *string  `json:"expired_at"`                    // 过期时间，时间戳
	Lat               *float64 `json:"lat"`                           // 纬度
	Lng               *float64 `json:"lng"`                           // 经度
	Logo              string   `json:"logo"`                          // 餐厅Logo
	NameUg            string   `json:"name_ug"`                       // 维文名称
	NameZh            string   `json:"name_zh"`                       // 中文名称
	No                string   `json:"no"`                            // 商家唯一编号
	Phone             string   `json:"phone"`                         // 电话号码
	RefundPassword    string   `json:"refund_password"`               // 退单密码
	CancelPassword    string   `json:"cancel_password"`               // 取消美食密码
	SMSCount          int64    `json:"sms_count"`                     // 剩余的短信数
	SubMerchantNo     string   `json:"sub_merchant_no"`               // 微信特约商户号
	WechatPaymentType int64    `json:"wechat_payment_type"`           // 是否特约商户, 0 普通转账模式、 1 特约商户模式
	Mode              int      `json:"mode"`                          // 商家模式：1 本地模式(默认) 2 线上模式
	CreatedAt         *string  `json:"created_at" gorm:"index;"`      // Create time
	UpdatedAt         *string  `json:"updated_at" gorm:"index;"`      // Update time
}

func (rc *MerchantResource) Make(item *model.MerchantModel) *MerchantResource {
	if item == nil {
		return nil
	}
	data := MerchantResource{
		ID:                item.ID,
		AddressUg:         item.AddressUg,
		AddressZh:         item.AddressZh,
		BusinessLicense:   item.BusinessLicense,
		DiancaiPay:        item.DiancaiPay,
		ExpiredAt:         util.FormatDateTime(item.ExpiredAt),
		Lat:               item.Lat,
		Lng:               item.Lng,
		Logo:              config.C.Storage.OSS.Endpoint + item.Logo,
		NameUg:            item.NameUg,
		NameZh:            item.NameZh,
		No:                item.No,
		Phone:             item.Phone,
		RefundPassword:    item.RefundPassword,
		CancelPassword:    item.CancelPassword,
		SMSCount:          item.SMSCount,
		SubMerchantNo:     item.SubMerchantNo,
		WechatPaymentType: item.GetWechatPaymentType(),
		Mode:              item.Mode,
		CreatedAt:         util.FormatDateTime(item.CreatedAt),
		UpdatedAt:         util.FormatDateTime(item.UpdatedAt),
	}
	return &data
}

func (rc *MerchantResource) Collection(items []*model.MerchantModel) []*MerchantResource {

	if items == nil || len(items) == 0 {
		return []*MerchantResource{}
	}

	data := make([]*MerchantResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

type MerchantBasicResource struct {
	ID         int64                     `json:"id" gorm:"size:20;primaryKey;"` // 自增编号
	Logo       string                    `json:"logo"`                          // 餐厅Logo
	NameUg     string                    `json:"name_ug"`                       // 维文名称
	NameZh     string                    `json:"name_zh"`                       // 中文名称
	No         string                    `json:"no"`                            // 商家唯一编号
	State      int64                     `json:"state"`                         // 商家状态
	Mode       int                       `json:"mode"`                          // 商家模式：1 本地模式(默认) 2 线上模式
	ServerInfo *LocalServerBasicResource `json:"server_info,omitempty"`         // 本地服务器信息
}

func (rc *MerchantBasicResource) Make(item *model.MerchantModel) *MerchantBasicResource {
	if item == nil {
		return nil
	}
	localServerBasicResource := LocalServerBasicResource{}
	data := MerchantBasicResource{
		ID:         item.ID,
		Logo:       config.C.Storage.OSS.Endpoint + item.Logo,
		NameUg:     item.NameUg,
		NameZh:     item.NameZh,
		No:         item.No,
		State:      item.State,
		Mode:       item.Mode,
		ServerInfo: localServerBasicResource.Make(item.LocalServer),
	}
	return &data
}

func (rc *MerchantBasicResource) Collection(items []*model.MerchantModel) []*MerchantBasicResource {

	if items == nil || len(items) == 0 {
		return []*MerchantBasicResource{}
	}

	data := make([]*MerchantBasicResource, len(items))
	// 异步操作
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

package middleware

import (
	"context"
	"fmt"
	"ros-api-go/internal/config"
	"ros-api-go/internal/http"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"strings"
	"time"

	"github.com/casbin/casbin/v2"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var ErrCasbinDenied = errors.Forbidden("com.casbin.denied", "PermissionDenied")

type CasbinConfig struct {
	Disabled    bool
	DB          *gorm.DB
	GetEnforcer func(c *gin.Context, merchantNo string) (*casbin.Enforcer, error)
	GetUserID   func(c *gin.Context) int64
}

func CasbinWithConfig(config CasbinConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		if config.Disabled {
			c.Next()
			return
		}
		ctx := c.Request.Context()

		// 获取商户号
		merchantNo := c.Request.Header.Get("Merchantno")

		if merchantNo == "" || merchantNo == "undefined" {
			util.ResError(c, ErrCasbinDenied)
			return
		}

		userID := config.GetUserID(c)

		merchantEmployee := &model.MerchantEmployeeModel{}
		ok, err := util.FindOne(ctx, util.GetDB(ctx, config.DB).
			Where("merchant_no = ?", merchantNo).
			Where("user_id = ?", userID), merchantEmployee)

		if !ok || err != nil {
			util.ResError(c, ErrCasbinDenied)
			return
		}

		// 账号被禁用
		if merchantEmployee.State != 1 {
			util.ResError(c, ErrCasbinDenied)
			return
		}

		// 店长直接允许
		if merchantEmployee.IsOwner {
			c.Next()
			logging.Context(ctx).Info("casbin middleware passed:IsOwner", zap.Duration("cost", time.Since(start)))
			return
		}

		// 获取 casbin 实例
		enforcer, err := config.GetEnforcer(c, merchantNo)
		if err != nil {
			util.ResError(c, err)
			return
		}

		if enforcer == nil {
			util.ResError(c, ErrCasbinDenied)
			return
		}

		// 判断是否有权限
		url := strings.Trim(c.Request.URL.Path, "/")
		if b, err := enforcer.Enforce(fmt.Sprintf("u:%d", userID), url, c.Request.Method); err != nil {
			util.ResError(c, err)
			return
		} else if b {
			c.Next()
			logging.Context(ctx).Info("casbin middleware passed", zap.Duration("cost", time.Since(start)))
			return
		}
		util.ResError(c, ErrCasbinDenied)
	}
}

// CasbinMiddleware 中间件实例
var casbinMiddleware gin.HandlerFunc

type CasbinProvider struct {
	Casbinx *http.Casbinx
}

// RegisterMiddleware 注册中间件
func (a *CasbinProvider) RegisterMiddleware(ctx context.Context, db *gorm.DB) {
	casbinMiddleware = CasbinWithConfig(CasbinConfig{
		DB:       db,
		Disabled: config.C.Middleware.Casbin.Disable,
		GetEnforcer: func(c *gin.Context, merchantNo string) (*casbin.Enforcer, error) {
			return a.Casbinx.GetEnforcer(c.Request.Context(), merchantNo)
		},
		GetUserID: func(c *gin.Context) int64 {
			return util.FromUserID(c.Request.Context())
		},
	})
}

// CasbinMiddleware 使用中间件
func CasbinMiddleware() gin.HandlerFunc {
	return casbinMiddleware
}

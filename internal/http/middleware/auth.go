package middleware

import (
	"context"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/cachex"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/jwtx"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
)

type AuthConfig struct {
	Skipper     func(c *gin.Context) bool
	ParseUserID func(c *gin.Context) (int64, error)
}

// AuthWithConfig 配置中间件
func AuthWithConfig(config AuthConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, err := config.ParseUserID(c)
		if err != nil {
			util.ResError(c, err)
			return
		}

		ctx := util.NewUserID(c.Request.Context(), userID)
		ctx = logging.NewUserID(ctx, userID)
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

// authMiddleware 中间件实例
var authMiddleware gin.HandlerFunc

// AuthProvider 认证提供者
type AuthProvider struct {
	Auth        jwtx.Auther
	Cache       cachex.Cacher
	UserService *service.UserService
}

// ParseUserID 解析用户ID
func (provider *AuthProvider) ParseUserID(c *gin.Context) (int64, error) {
	token := util.GetToken(c)
	if token == "" {
		return 0, errors.Unauthorized("EmptyToken", "InvalidToken")
	}

	ctx := c.Request.Context()
	ctx = util.NewUserToken(ctx, token)

	claims, err := provider.Auth.ParseToken(ctx, token)
	if err != nil {
		if errors.Is(err, jwtx.ErrInvalidToken) {
			return 0, errors.Unauthorized("InvalidTokenID", "InvalidToken")
		}
		return 0, err
	}

	// 检查用户状态
	merchantNo := c.GetHeader("Merchantno")
	userID := claims.Subject

	var employee *model.MerchantEmployeeModel
	var tokenStr string

	// 新模式token验证
	if claims.ClientType != "" {
		userToken, err := provider.UserService.GetUserTokenWithEmployeeByUserID(ctx, merchantNo, claims.ClientType, userID)
		if err != nil {
			return 0, err
		}
		if userToken != nil {
			employee = userToken.Employee
			tokenStr = userToken.UserToken[:16]
		}
	} else {
		// 适配旧模式token
		user, err := provider.UserService.GetUserWithEmployeeByUserID(ctx, merchantNo, userID)
		if err != nil {
			return 0, err
		}
		if user != nil {
			employee = user.Employee
			tokenStr = user.ApiToken[:16]
		}
	}

	if employee == nil || employee.State != model.EmployeeStateActive {
		return 0, errors.Unauthorized("UserNotActive", "InvalidToken")
	}

	// 检查token 唯一性
	if tokenStr != claims.Token {
		return 0, errors.Unauthorized("UserTokenNotMatch", "InvalidToken")
	}

	// 设置用户信息
	isZh := i18n.IsZh(&ctx)

	if isZh {
		ctx = util.NewUserName(ctx, employee.NameZh)
	} else {
		ctx = util.NewUserName(ctx, employee.NameUg)
	}

	c.Request = c.Request.WithContext(ctx)
	return userID, nil
}

// RegisterMiddleware 注册中间件
func (provider *AuthProvider) RegisterMiddleware(ctx context.Context, db *gorm.DB) {
	authMiddleware = AuthWithConfig(AuthConfig{
		ParseUserID: provider.ParseUserID,
	})
}

// AuthMiddleware 使用中间件
func AuthMiddleware() gin.HandlerFunc {
	return authMiddleware
}

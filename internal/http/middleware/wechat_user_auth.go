package middleware

import (
	"context"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

type WechatUserAuthConfig struct {
	ParseWechatUser func(c *gin.Context) (*model.WechatUserModel, error)
}

// 中间件实例
var wechatUserAuthMiddleware gin.HandlerFunc

type WechatUserProvider struct {
	WechatUserService service.WechatUserService
	MerchantService   service.MerchantService
}

// ParseWechatUser 解析本地服务ServerToken
func (provider *WechatUserProvider) ParseWechatUser(c *gin.Context) (*model.WechatUserModel, error) {
	ctx := c.Request.Context()
	invalidToken := errors.Unauthorized(consts.ErrInvalidTokenID, "InvalidToken")
	token := util.GetToken(c)
	if token == "" {
		return nil, invalidToken
	}
	wechatUser, err := provider.WechatUserService.GetByToken(ctx, token)
	if err != nil {
		return nil, err
	}

	if wechatUser == nil {
		return nil, invalidToken
	}

	return wechatUser, nil
}

// WechatUserWithConfig 生成WechatUser中间件配置
func WechatUserWithConfig(config WechatUserAuthConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		wechatUser, err := config.ParseWechatUser(c)
		if err != nil {
			util.ResError(c, err)
			return
		}
		if wechatUser == nil {
			util.ResError(c, errors.Unauthorized(consts.ErrInvalidTokenID, "InvalidToken"))
			return
		}
		ctx := c.Request.Context()
		ctx = util.NewWechatUserID(ctx, wechatUser.ID)
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

// RegisterMiddleware 注册WechatUser中间件
func (provider *WechatUserProvider) RegisterMiddleware(ctx context.Context, db *gorm.DB) {
	wechatUserAuthMiddleware = WechatUserWithConfig(WechatUserAuthConfig{
		ParseWechatUser: provider.ParseWechatUser,
	})
}

// WechatUserMiddleware 使用WechatUser中间件
func WechatUserMiddleware() gin.HandlerFunc {
	return wechatUserAuthMiddleware
}

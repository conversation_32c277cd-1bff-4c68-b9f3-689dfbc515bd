package middleware

import (
	"context"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/cachex"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
)

type ServerAuthConfig struct {
	ParseLocalServer func(c *gin.Context) (*model.LocalServerModel, error)
}

// 中间件实例
var serverAuthMiddleware gin.HandlerFunc

type ServerAuthProvider struct {
	LocalServerService service.LocalServerService
	MerchantService    service.MerchantService
	Cache              cachex.Cacher
}

// ParseLocalServer 解析本地服务ServerToken
func (provider *ServerAuthProvider) ParseLocalServer(c *gin.Context) (*model.LocalServerModel, error) {
	ctx := c.Request.Context()
	invalidToken := errors.Unauthorized(consts.ErrInvalidTokenID, "InvalidToken")
	token := util.GetToken(c)
	if token == "" {
		return nil, invalidToken
	}
	localServer, err := provider.LocalServerService.GetByToken(ctx, token)
	if err != nil {
		return nil, err
	} else if localServer == nil || localServer.Status != model.UserStatusActivated {
		return nil, invalidToken
	}

	// 查询商户信息
	merchant, err := provider.MerchantService.GetByMerchantNo(ctx, localServer.MerchantNo)
	if err != nil {
		return nil, err
	} else if merchant == nil || merchant.State != model.MerchantStateActive {
		return nil, invalidToken
	}

	return localServer, nil
}

// ServerAuthWithConfig 生成ServerAuth中间件配置
func ServerAuthWithConfig(config ServerAuthConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		localServer, err := config.ParseLocalServer(c)
		if err != nil {
			util.ResError(c, err)
			return
		}
		ctx := c.Request.Context()
		ctx = util.NewServerMerchantNo(ctx, localServer.MerchantNo)
		ctx = logging.NewServerMerchantNo(ctx, localServer.MerchantNo)
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

// RegisterMiddleware 注册ServerAuth中间件
func (provider *ServerAuthProvider) RegisterMiddleware(ctx context.Context, db *gorm.DB) {
	serverAuthMiddleware = ServerAuthWithConfig(ServerAuthConfig{
		ParseLocalServer: provider.ParseLocalServer,
	})
}

// ServerAuthMiddleware 使用ServerAuth中间件
func ServerAuthMiddleware() gin.HandlerFunc {
	return serverAuthMiddleware
}

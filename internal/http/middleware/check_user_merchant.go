package middleware

import (
	"fmt"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type CheckUserMerchantProvider struct {
	MerchantEmployeeService *service.MerchantEmployeeService
}

// Check 检查用户是否属于商户
func (provider *CheckUserMerchantProvider) Check() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		fmt.Println("检查用户商户")
		userId := util.FromUserID(ctx.Request.Context())
		merchantNo := ctx.Request.Header.Get("Merchantno")
		stuff, err := provider.MerchantEmployeeService.FindByMerchantIdAndUserId(ctx, merchantNo, userId)
		if err != nil || stuff == nil || stuff.MerchantNo != merchantNo {
			util.ResError(ctx, errors.NotFound("", "UserNotFound"))
			return
		}
		ctx.Next()
	}
}

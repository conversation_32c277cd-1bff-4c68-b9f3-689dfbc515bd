package request

import (
	"ros-api-go/internal/model"
	"time"
)

type PrinterSyncRequest struct {
	ID             string              `json:"id" binding:"required"`                                            // 打印机ID
	MerchantNo     string              `json:"-"`                                                                // 商户号 (不用传, 自动填充)
	NameZh         string              `json:"name_zh" binding:"required"`                                       // 中文名称
	NameUg         string              `json:"name_ug" binding:"required"`                                       // 维语名称
	ConnectionType string              `json:"connection_type" binding:"required,oneof=network usb inner cloud"` // 连接方式 network/usb/inner(内置打印机)/cloud
	IpAddress      string              `json:"ip_address"`                                                       // IP地址(网络打印机使用)
	UsbPort        string              `json:"usb_port"`                                                         // USB端口号(USB打印机使用)
	Cloud          string              `json:"cloud"`                                                            // 云打印机号
	PaperWidth     int                 `json:"paper_width" binding:"required,max=999"`                           // 纸张宽度(58mm或80mm)
	PrintMode      string              `json:"print_mode" binding:"required,oneof=text image"`                   // 打印方式 text/image
	Status         int                 `json:"status"`                                                           // 打印机状态 0:禁用 1:启用 -1:已删除
	Buzzer         bool                `json:"buzzer"`                                                           // 蜂鸣器	false:不提醒 true:提醒
	CreatedAt      *time.Time          `json:"created_at" binding:"required"`                                    // 创建时间
	UpdatedAt      *time.Time          `json:"updated_at" binding:"required"`                                    // 更新时间
	DeletedAt      *time.Time          `json:"deleted_at"`                                                       // 删除时间
	CashierConfig  model.CashierConfig `json:"cashier_config" binding:"required,dive"`                           // 收银打印机配置
	KitchenConfig  model.KitchenConfig `json:"kitchen_config" binding:"required,dive"`                           // 后厨打印机配置
}

func (req *PrinterSyncRequest) FillTo(printer *model.PrinterModel) {

	printer.MerchantNo = req.MerchantNo
	printer.ID = req.ID
	printer.NameZh = req.NameZh
	printer.NameUg = req.NameUg
	printer.ConnectionType = req.ConnectionType
	printer.IpAddress = req.IpAddress
	printer.UsbPort = req.UsbPort
	printer.Cloud = req.Cloud
	printer.PaperWidth = req.PaperWidth
	printer.PrintMode = req.PrintMode
	printer.Status = req.Status
	printer.Buzzer = req.Buzzer
	printer.CreatedAt = req.CreatedAt
	printer.UpdatedAt = req.UpdatedAt
	printer.DeletedAt = req.DeletedAt

	printer.CashierConfig = req.CashierConfig
	printer.KitchenConfig = req.KitchenConfig
}

type PrinterFoodsSyncRequest struct {
	PrinterId string  `json:"printer_id"`
	FoodIds   []int64 `json:"food_ids"`
}

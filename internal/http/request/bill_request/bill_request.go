package bill_request

import (
	"context"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"
)

type BillListRequest struct {
	util.PaginationParam            // 分页参数
	MerchantNo           string     // 商户号
	BeginAt              *time.Time `form:"begin_at" time_format:"2006-01-02 15:04:05"` // 开始时间
	EndAt                *time.Time `form:"end_at" time_format:"2006-01-02 15:04:05"`   // 结束时间
	CashierID            int64      `form:"cashier_id"`                                 // 收银员ID
	PaymentTypeID        int64      `form:"payment_type_id"`                            // 支付方式ID
	TableName            string     `form:"table_name"`                                 // 订单号
}

func (req *BillListRequest) Validate(ctx context.Context) error {
	// 开始时间和结束时间不能超过30天
	if req.BeginAt != nil && req.EndAt != nil && req.EndAt.Sub(*req.BeginAt) > 31*24*time.Hour {
		return errors.ValidationError("", "TimeRangeExceedsDays", 31)
	}
	return nil
}

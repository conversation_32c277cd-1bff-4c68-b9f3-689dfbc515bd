package request

// HandoverOpenForm 开班表单
type HandoverOpenForm struct {
	UserID          int64   `json:"-"`                                // 当前用户(系统填充)
	MerchantNo      string  `json:"-"`                                // 商户号 (系统填充)
	Shift           int64   `json:"shift" binding:"required"`         // 班次ID
	WorkingBalance  float64 `json:"working_balance" binding:"min=0"`  // 工作余额
	AlternateAmount float64 `json:"alternate_amount" binding:"min=0"` // 代替金额
}

type HandoverForm struct {
	UserID     int64  `json:"user_id"` // 当前用户
	MerchantNo string `json:"-"`       // 商户号 (系统填充)
}

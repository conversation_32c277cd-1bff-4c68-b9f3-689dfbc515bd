package request

import "ros-api-go/pkg/util"

// CreateDebtHolderRequest represents the request to create a debt holder
type CreateDebtHolderRequest struct {
	MerchantNo  string ``                                             // 商户号 (系统填充)
	NameZh      string `json:"name_zh" binding:"required"`            // 中文姓名
	NameUg      string `json:"name_ug" binding:"required"`            // 维语姓名
	Phone       string `json:"phone" binding:"required,mobile"`       // 手机号
	CreditLimit int64  `json:"credit_limit" binding:"required,min=1"` // 信用额度(分)
	Status      uint8  `json:"status" binding:"omitempty,oneof=0 1"`  // 状态：0 禁用 1 启用
}
type UpdateDebtHolderRequest struct {
	MerchantNo  string ``                                                    // 商户号 (系统填充)
	NameZh      string `json:"name_zh" binding:"required"`                   // 中文姓名
	NameUg      string `json:"name_ug" binding:"required"`                   // 维语姓名
	CreditLimit int64  `json:"credit_limit" binding:"required,number,min=1"` // 信用额度(元)
	Status      uint8  `json:"status" binding:"omitempty,oneof=0 1"`         // 状态：0 禁用 1 启用
}

// DebtHolderListRequest 赊账人列表请求参数
type DebtHolderListRequest struct {
	util.PaginationParam
	Keyword string         `form:"keyword"`
	Status  string         `form:"status" binding:"omitempty,oneof=0 1"`
	Sort    util.SortParam `form:"sort"`
}

// DebtHolderStatusRequest 更新打印机状态请求
type DebtHolderStatusRequest struct {
	Status int `json:"status" binding:"oneof=0 1"` // 状态 0:禁用 1:启用
}

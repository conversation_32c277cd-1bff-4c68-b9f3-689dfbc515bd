package request

import (
	"ros-api-go/internal/model"
)

// LocalServerForm 表单数据
type LocalServerForm struct {
	MerchantNo string `json:"merchant_no" binding:"required,max=16;"` // 商家唯一编号
	UserID     int64  `json:"user_id" binding:"required,min=1;"`      // 用户ID
	UUID       string `json:"uuid" binding:"required,unique,max=32;"` // 本地服务识别码
	Ipv4       string `json:"ipv4" binding:"required,max=32;"`        // ipv4
	Ipv6       string `json:"ipv6" binding:"max=64;"`                 // ipv6
	Token      string `json:"token" binding:"max=32;"`                // 授权码
	Status     int64  `json:"status"`                                 // 状态
}

// Validate 表单验证
func (a *LocalServerForm) Validate() error {
	return nil
}

// FillTo 表单数据填充
func (a *LocalServerForm) FillTo(localServer *model.LocalServerModel) error {
	localServer.Ipv4 = a.Ipv4
	localServer.Ipv6 = a.Ipv6
	localServer.Token = a.Token
	localServer.Status = a.Status
	return nil
}

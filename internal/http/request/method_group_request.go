package request

import "ros-api-go/pkg/util"

// MethodGroupListRequest 获取做法分组列表请求
type MethodGroupListRequest struct {
	util.PaginationParam
	Keyword string `form:"keyword"` // 搜索关键词
}

// CreateMethodGroupRequest 创建做法分组请求
type CreateMethodGroupRequest struct {
	Name string `json:"name" binding:"required,max=32"` // 分组名称
}

// UpdateMethodGroupRequest 更新做法分组请求
type UpdateMethodGroupRequest struct {
	Name string `json:"name" binding:"required,max=32"` // 分组名称
}

// MethodGroupSortItem 做法分组排序项
type MethodGroupSortItem struct {
	ID   int64 `json:"id" binding:"required"`   // 分组ID
	Sort int64 `json:"sort" binding:"required"` // 排序值
}

// SaveMethodGroupSortRequest 保存做法分组排序请求
type SaveMethodGroupSortRequest struct {
	Items []MethodGroupSortItem `json:"items" binding:"required,dive"` // 排序项列表
}

package merchant_employee_request

type UpdateEmployeeRequest struct {
	No      string  `json:"no" binding:"required,max=9"`
	NameUg  string  `json:"name_ug" binding:"required,max=64"`
	NameZh  string  `json:"name_zh" binding:"required,max=64"`
	State   int8    `json:"state" binding:"oneof=0 1"`
	RoleIds []int64 `json:"role_ids" binding:"dive,gt=0"`
}

type UpdateStateRequest struct {
	State int8 `json:"state" binding:"oneof=0 1"`
}

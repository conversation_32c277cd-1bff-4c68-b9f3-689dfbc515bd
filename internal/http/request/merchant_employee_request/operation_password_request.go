package merchant_employee_request

import (
	"context"
	"ros-api-go/pkg/errors"
)

type SetOperationPasswordRequest struct {
	BatchID         string `json:"batch_id" binding:"required,number,len=10"`
	Code            string `json:"code" binding:"required,number,len=4"`
	Password        string `json:"password" binding:"required,number,len=6"`
	ConfirmPassword string `json:"confirm_password" binding:"required,number,len=6"`
}

func (req *SetOperationPasswordRequest) Validate(ctx context.Context) error {
	if req.Password != req.ConfirmPassword {
		return errors.ValidationError("", "PasswordNotMatch")
	}
	return nil
}

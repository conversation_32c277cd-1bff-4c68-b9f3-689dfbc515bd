package rpcInvokeRequest

// Header 请求头信息
type Header struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}
type RpcInvokeRequest struct {
	Method      string   `json:"method" binding:"required"`
	Path        string   `json:"path" binding:"required"`
	ContentType string   `json:"Content-Type" binding:"required"`
	Headers     []Header `json:"headers" binding:"required"`
	Body        string   `json:"body" binding:"required"`
	MerchantNo  string   `json:"MerchantNo" binding:"required"`
}

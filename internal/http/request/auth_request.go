package request

import (
	"ros-api-go/internal/model"
	"strings"
)

// UserMerchantsRequest 账号密码获取商户列表请求
type UserMerchantsRequest struct {
	Username string `json:"username" binding:"required"` // 用户名
	Password string `json:"password" binding:"required"` // 密码
}

const (
	LoginConfirmUnconfirmed = 0 // 未确认
	LoginConfirmAsMainLogin = 1 // 确认作为主收银台登录
	LoginConfirmAsSubLogin  = 2 // 确认作为副收银台登录
)

// LoginForm 登录表单
type LoginForm struct {
	Username   string           `json:"username" binding:"required"`    // 用户名
	Password   string           `json:"password" binding:"required"`    // 密码
	MerchantNo string           `json:"merchant_no" binding:"required"` // 商户编号
	UserID     int64            `json:"-"`                              // 用户ID （系统填充）
	ClientType model.ClientType `json:"-"`                              // 客户端类型 (系统填充)
	UUID       string           `json:"uuid"`                           // 设备识别码
	Ipv4       string           `json:"ipv4"`                           // IPv4地址
	Ipv6       string           `json:"ipv6"`                           // IPv6地址
	Confirm    int              `json:"confirm"`                        // 是否作为主收银台登录: 0暂未确认, 1确认作为主收银台登录 2作为副收银台登录
}

// Trim 去除空格
func (a *LoginForm) Trim() *LoginForm {
	a.MerchantNo = strings.TrimSpace(a.MerchantNo)
	a.Username = strings.TrimSpace(a.Username)
	a.UUID = strings.TrimSpace(a.UUID)
	a.Ipv4 = strings.TrimSpace(a.Ipv4)
	a.Ipv6 = strings.TrimSpace(a.Ipv6)
	return a
}

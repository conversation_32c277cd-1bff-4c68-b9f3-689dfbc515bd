package request

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/crypto/hash"
	"ros-api-go/pkg/errors"
)

// UserForm 注册参数
type UserForm struct {
	MerchantNo string `json:"merchant_no" binding:"required,max=64"` // Username for login
	Name       string `json:"name" binding:"required,max=64"`        // Name of user
	Password   string `json:"password" binding:"max=64"`             // Password for login (md5 hash)
	Phone      string `json:"phone" binding:"max=32"`                // Phone number of user
	State      int64  `json:"state" binding:"required,oneof=0,1"`    // Status of user (activated, freezed)
}

// Validate 数据校验
func (a *UserForm) Validate() error {
	return nil
}

// FillTo 填充到 UserModel
func (a *UserForm) FillTo(user *model.UserModel) error {
	if pass := a.Password; pass != "" {
		hashPass, err := hash.GeneratePassword(pass)
		if err != nil {
			return errors.BadRequest("", "Failed to generate hash password: %s", err.Error())
		}
		user.Password = hashPass
	}

	return nil
}

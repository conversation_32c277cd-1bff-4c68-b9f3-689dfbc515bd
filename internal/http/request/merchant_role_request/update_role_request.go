package merchant_role_request

import (
	"ros-api-go/pkg/util"
)

type UpdateRoleRequest struct {
	Name_zh     string              `json:"name_zh" binding:"required,min=1,max=64"`
	Name_ug     string              `json:"name_ug" binding:"required,min=1,max=64"`
	Permissions util.TArray[string] `json:"permissions" binding:"required"`
	State       int8                `json:"state" binding:"oneof=0 1"`
}

type UpdateRoleStateRequest struct {
	State int8 `json:"state" binding:"oneof=0 1"`
}

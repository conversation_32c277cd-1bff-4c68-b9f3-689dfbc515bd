package request

import (
	"ros-api-go/pkg/util"
	"time"
)

// DebtTransactionListRequest 赊账交易记录列表请求参数
type DebtTransactionListRequest struct {
	util.PaginationParam
	HolderID *int64     `form:"holder_id"`                      // 赊账人ID
	Type     *uint8     `form:"type" binding:"omitempty,min=1"` // 交易类型 1 赊账 2 结账
	BeginAt  *time.Time `form:"begin_at"`                       // 开始时间
	EndAt    *time.Time `form:"end_at"`                         // 结束时间
}

package order_request

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// OrderDetail 代表订单详情
type OrderDetail struct {
	FoodID     int64                         `json:"food_id"`
	FoodsCount float64                       `json:"foods_count"`
	Remarks    string                        `json:"remarks"`
	ComboInfo  *util.TArray[model.ComboInfo] `json:"combo_info"`
}

// OrderAddFoodRequest 代表整个请求体
type OrderAddFoodRequest struct {
	OrderDetails []*OrderDetail `json:"order_details"`
	OrderRemark  string         `json:"order_remark"`
}

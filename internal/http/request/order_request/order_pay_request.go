package order_request

type PaymentRequest struct {
	OrderId   int64   `json:"order_id" binding:"required"`
	OrderNo   string  `json:"order_no" binding:"required,min=13,max=32"`
	PaymentId int64   `json:"payment_id" binding:"required"`
	PaymentNo string  `json:"payment_no" binding:"required,min=13,max=32"`
	Amount    float64 `json:"amount" binding:"required,min=0.01"`
}

type MicroPayRequest struct {
	OrderId   int64   `json:"order_id" binding:"required"`
	OrderNo   string  `json:"order_no" binding:"required,min=13,max=32"`
	PaymentId int64   `json:"payment_id" binding:"required"`
	PaymentNo string  `json:"payment_no" binding:"required,min=13,max=32"`
	Amount    float64 `json:"amount" binding:"required,min=0.01"`
	AuthCode  string  `json:"auth_code"`
}

type PaymentReverseRequest struct {
	OrderId   int64  `json:"order_id" binding:"required"`
	OrderNo   string `json:"order_no" binding:"required,min=13,max=32"`
	PaymentNo string `json:"payment_no" binding:"required,min=13,max=32"`
}

type CustomerPayRequest struct {
	OrderId    int64   `json:"order_id" binding:"required"`
	OrderNo    string  `json:"order_no" binding:"required,min=13,max=32"`
	PaymentNo  string  `json:"payment_no" binding:"required,min=13,max=32"`
	Amount     float64 `json:"amount" binding:"required,min=0.01"`
	CustomerID int64   `json:"customer_id"`
	Password   string  `json:"password"`
}

type DebtPayRequest struct {
	OrderId   int64  `json:"order_id" binding:"required"`
	OrderNo   string `json:"order_no" binding:"required,min=13,max=32"`
	PaymentNo string `json:"payment_no" binding:"required,min=13,max=32"`
	Amount    int64  `json:"amount" binding:"required,min=1"`
	HolderID  int64  `json:"holder_id"`
}

package order_request

import (
	"context"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
)

type RefundOrderForm struct {
	OrderID      int64                   `json:"-"`                                      // 订单ID (自动填充)
	OrderDetails []RefundOrderDetailForm `json:"order_details" binding:"required,min=1"` // 订单详情
	Payments     map[int64]float64       `json:"payments" binding:"required"`            // 退款方式
	Password     string                  `json:"password" binding:"required"`            // 操作密码
	MerchantNo   string                  `json:"-"`                                      // 商户号 (自动填充)
	CashierID    int64                   `json:"-"`                                      // 收银员ID
}

// RefundOrderDetailForm 订单退款详情
type RefundOrderDetailForm struct {
	ID            int64   `json:"id" binding:"required,gt=0"`                      // 确保是正整数
	FoodsCount    float64 `json:"foods_count" binding:"required,min=0.01,max=999"` // 确保在范围内
	Price         float64 `json:"-"`                                               // 结算价格 (自动填充)
	VipPrice      float64 `json:"-"`                                               // 会员价格 (自动填充)
	OriginalPrice float64 `json:"-"`                                               // 原价 (自动填充)
	TotalPrice    float64 `json:"-"`                                               // 总价 (自动填充)
	Remarks       string  `json:"remarks" binding:"max=255"`                       // 可选，最大255字符
}

func (form *RefundOrderDetailForm) Validate(ctx context.Context, detail *model.OrderDetailModel) error {
	// 订单详情状态检查
	if detail.State == consts.ORDER_DETAIL_STATE_CANCEL {
		return errors.BadRequest("", "OrderDetailAlreadyCancelled", detail.ID)
	}

	// 数量有问题
	if form.FoodsCount > detail.FoodsCount {
		return errors.BadRequest("", "FoodsCountTooMuch")
	}

	return nil
}

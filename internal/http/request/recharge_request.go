package request

import "ros-api-go/pkg/encoding/json"

type RechargeRequest struct {
	CustomerID     int64  `json:"customer_id" binding:"required"`           // 会员ID (VIP支付时必填)
	RechargeAmount int64  `json:"recharge_amount" binding:"required,min=1"` // 支付金额，单位分
	PresentAmount  int64  `json:"present_amount" binding:"min=0"`           // 赠送金额，单位分
	CashierID      int64  `json:"cashier_id"`                               // 收银员ID
	MerchantNo     string `json:"merchant_no"`                              // 商家编号，由系统自动填充
	PaymentTypeID  int64  `json:"payment_type_id"`                          // 支付方式，1：微信支付， 3 现金支付，10：支付宝支付 .由系统判断填充
	PaymentNo      string `json:"payment_no"`                               // 支付流水号，由系统自动填充
}

func (form *RechargeRequest) String() string {
	return json.MarshalToString(form)
}

type RechargeMicroPayForm struct {
	RechargeRequest
	AuthCode string `json:"auth_code" binding:"required"` // 付款码编号
}

type RechargeOfflineForm struct {
	RechargeRequest
	PaymentTypeID int64 `json:"payment_type_id" binding:"required"` // 付款码编号
}

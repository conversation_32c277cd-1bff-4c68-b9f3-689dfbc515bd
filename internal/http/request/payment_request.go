package request

import (
	"context"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

// MerchantPaymentForm MerchantPayment表单数据
type MerchantPaymentForm struct {
	OrderNo       string `json:"order_no" binding:"required,min=16,max=32"`   // 订单编号
	PaymentNo     string `json:"payment_no" binding:"required,min=16,max=32"` // 支付信息编号
	Amount        int64  `json:"amount" binding:"required,min=1"`             // 支付金额，单位分
	MerchantNo    string `json:"merchant_no"`                                 // 商家编号，由系统自动填充
	PaymentTypeID int64  `json:"payment_type_id"`                             // 支付方式，1：微信支付， 3 现金支付，10：支付宝支付 .由系统判断填充
	CustomerID    int64  `json:"customer_id"`                                 // 会员ID (VIP支付时必填)
	CashierID     int64  `json:"cashier_id"`                                  // 收银员ID
}

// String 表单数据序列化为JSON字符串
func (form *MerchantPaymentForm) String() string {
	return json.MarshalToString(form)
}

// Validate 表单数据验证
func (form *MerchantPaymentForm) Validate() error {
	if form.Amount <= 0 {
		return errors.BadRequest("", "invalid amount")
	}
	return nil
}

// FillTo 表单数据填充
func (form *MerchantPaymentForm) FillTo(payment *model.MerchantPaymentModel) error {
	payment.Amount = form.Amount
	payment.OrderNo = form.OrderNo
	payment.PaymentNo = form.PaymentNo
	payment.MerchantNo = form.MerchantNo
	payment.PaymentTypeID = form.PaymentTypeID
	payment.CustomerID = form.CustomerID
	payment.CashierID = form.CashierID
	return nil
}

// MicroPayForm 付款码支付表单数据
type MicroPayForm struct {
	MerchantPaymentForm
	AuthCode string `json:"auth_code" binding:"required"` // 付款码编号
}

// PaymentQueryForm 支付查询表单数据
type PaymentQueryForm struct {
	PaymentNo string `json:"payment_no" binding:"required"` // 支付信息编号
}

// JsAPIPaymentForm 二维码支付表单数据
type JsAPIPaymentForm struct {
	OrderType int    `json:"order_type" binding:"required"` // 订单类型
	AuthCode  string `json:"auth_code" binding:"required"`  // 付款码编号
}

// CustomerPaymentForm 会员支付表单数据
type CustomerPaymentForm struct {
	MerchantPaymentForm
	CustomerID int64  `json:"customer_id" binding:"required"` // 会员ID
	CashierID  int64  `json:"cashier_id" binding:"required"`  // 收银员ID
	Password   string `json:"password" binding:"required"`    // 支付密码
}

type DebtPaymentForm struct {
	MerchantPaymentForm
	HolderID  int64 `json:"holder_id" binding:"required"`  // 会员ID
	CashierID int64 `json:"cashier_id" binding:"required"` // 收银员ID
}

// PaymentRefundForm 支付退款表单数据
type PaymentRefundForm struct {
	OrderNo    string `json:"order_no" binding:"required"`   // 订单编号
	PaymentNo  string `json:"payment_no" binding:"required"` // 支付信息编号
	CashierID  int64  `json:"cashier_id" binding:"required"` // 收银员ID
	MerchantNo string `json:"-"`                             // 商家编号，由系统自动填充
}

// Validate 表单数据验证
func (form *PaymentRefundForm) Validate(ctx context.Context, paymentLog *model.MerchantPaymentLogModel) error {
	merchantNo := util.FromServerMerchantNo(ctx)
	// 判断商户号是否一致
	if merchantNo != paymentLog.MerchantNo {
		return errors.BadRequest("", "PaymentNotFound")
	}
	// 订单号是否一致
	if form.OrderNo != paymentLog.OrderNo {
		return errors.BadRequest("", "PaymentNotFound")
	}
	// 判断支付状态
	if paymentLog.Status == consts.PAY_STATUS_UNPAID {
		return errors.BadRequest("", "PaymentNotPaid")
	}
	return nil
}

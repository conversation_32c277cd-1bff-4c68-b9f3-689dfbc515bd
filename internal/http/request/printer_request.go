package request

import (
	"ros-api-go/internal/model"
)

// PrinterCreateRequest 创建打印机请求
type PrinterCreateRequest struct {
	MerchantNo     string              `json:"-"`                                                          // 商户号
	NameUg         string              `json:"name_ug" binding:"required"`                                 // 维语名称
	NameZh         string              `json:"name_zh" binding:"required"`                                 // 中文名称
	ConnectionType string              `json:"connection_type" binding:"required,oneof=network usb cloud"` // 连接方式 network/usb/cloud
	IpAddress      string              `json:"ip_address"`                                                 // IP地址
	UsbPort        string              `json:"usb_port"`                                                   // USB端口
	Cloud          string              `json:"cloud"`                                                      // 云打印机ID
	PaperWidth     int                 `json:"paper_width" binding:"required,oneof=58 80"`                 // 纸张宽度
	PrintMode      string              `json:"print_mode" binding:"required,oneof=text image"`             // 打印模式
	Buzzer         bool                `json:"buzzer"`                                                     // 蜂鸣器
	CashierConfig  model.CashierConfig `json:"cashier_config" binding:"required"`                          // 收银配置
	KitchenConfig  model.KitchenConfig `json:"kitchen_config" binding:"required"`                          // 厨房配置
}

// PrinterUpdateRequest 更新打印机请求
type PrinterUpdateRequest struct {
	PrinterCreateRequest
	ID string `json:"id" binding:"required"` // 打印机ID
}

// PrinterStatusRequest 更新打印机状态请求
type PrinterStatusRequest struct {
	Status int `json:"status" binding:"oneof=0 1"` // 状态 0:禁用 1:启用
}

// PrinterFoodsRequest 打印机关联菜品请求
type PrinterFoodsRequest struct {
	PrinterID string  `json:"printer_id" binding:"required"`
	FoodIDs   []int64 `json:"food_ids" binding:"required"`
}

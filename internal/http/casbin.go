package http

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"

	"gorm.io/gorm"

	"ros-api-go/pkg/cachex"
	"ros-api-go/pkg/logging"

	"github.com/casbin/casbin/v2"
	"go.uber.org/zap"
)

// Casbinx rbac permissions to casbin
type Casbinx struct {
	permissions []*model.Permission `wire:"-"`
	ticker      *time.Ticker        `wire:"-"`
	Cache       cachex.Cacher
	DB          *gorm.DB
}

// MerchantCasbinRole 商户角色
type MerchantCasbinRole struct {
	ID          int64
	Permissions util.TArray[string]
}

// MerchantCasbinUserRole 商户用户角色
type MerchantCasbinUserRole struct {
	UserID int64
	RoleID int64
}

// MerchantCasbinCache 商户权限缓存
type MerchantCasbinCache struct {
	MerchantNo    string
	MerchantRoles []*MerchantCasbinRole
	UserRoles     []*MerchantCasbinUserRole
}

// String 缓存对象字符串
func (cache *MerchantCasbinCache) String() string {
	return json.MarshalToString(cache)
}

// InitPermissionsFromFile 从文件初始化权限
func (csbn *Casbinx) InitPermissionsFromFile(ctx context.Context, permissionFile string) ([]*model.Permission, error) {
	f, err := os.ReadFile(permissionFile)
	if err != nil {
		if errors.Is(err, os.ErrNotExist) {
			logging.Context(ctx).Warn("系统权限文件不存在", zap.String("file", permissionFile))
		}
		return nil, err
	}

	var permissions []*model.Permission
	if err := json.Unmarshal(f, &permissions); err != nil {
		return nil, errors.Wrapf(err, "Unmarshal JSON file '%s' failed", permissionFile)
	}
	return permissions, nil
}

// GetEnforcer 获取权限管理器
func (csbn *Casbinx) GetEnforcer(ctx context.Context, merchantNo string) (*casbin.Enforcer, error) {
	modelFile := filepath.Join(config.C.General.WorkDir, "configs", config.C.Middleware.Casbin.ModelFile)
	enfcr, err := casbin.NewEnforcer(modelFile, false)
	if err != nil {
		logging.Context(ctx).Error("Casbin 系统权限加载失败", zap.Error(err))
		return nil, err
	}
	// 写入权限数据
	for _, item := range csbn.permissions {
		_, err := enfcr.AddPolicy(item.V0, item.V1, item.V2)
		if err != nil {
			return nil, err
		}
	}

	enfcr.EnableLog(config.C.IsDebug())
	return enfcr, csbn.loadMerchantPermissions(ctx, enfcr, merchantNo)
}

// LoadSysPermissions 加载权限
func (csbn *Casbinx) LoadSysPermissions(ctx context.Context) error {
	if config.C.Middleware.Casbin.Disable {
		return nil
	}
	if err := csbn.loadSysPermissions(ctx); err != nil {
		return err
	}
	return nil
}

// ClearMerchantCasbinCache 清除商户权限缓存
func (csbn *Casbinx) ClearMerchantCasbinCache(merchantNo string) error {
	log.Printf("ClearMerchantCasbinCache %s:%s", consts.CacheNSForMerchantCasbin, merchantNo)
	return csbn.Cache.Delete(context.Background(), consts.CacheNSForMerchantCasbin, merchantNo)
}

// 加载系统权限
func (csbn *Casbinx) loadSysPermissions(ctx context.Context) error {
	start := time.Now()
	// 读取权限文件
	permissions, err := csbn.InitPermissionsFromFile(ctx, filepath.Join(config.C.General.WorkDir, "configs/permissions.json"))
	if err != nil {
		return err
	}

	csbn.permissions = permissions
	logging.Context(ctx).Info("Casbin 系统权限加载成功",
		zap.Duration("cost", time.Since(start)),
	)
	return nil
}

// loadMerchantPermissions 加载商户权限(商户角色+用户角色)
func (csbn *Casbinx) loadMerchantPermissions(ctx context.Context, enforcer *casbin.Enforcer, merchantNo string) error {
	start := time.Now()
	// 读取缓存数据
	cache, err := csbn.getMerchantCasbinCache(ctx, merchantNo)
	if err != nil {
		return err
	}

	// 写入角色权限
	for _, role := range cache.MerchantRoles {
		_, err := enforcer.AddRolesForUser(fmt.Sprintf("r:%d", role.ID), role.Permissions)
		if err != nil {
			return err
		}
	}
	// 写入用户角色权限
	for _, userRole := range cache.UserRoles {
		_, err := enforcer.AddRoleForUser(fmt.Sprintf("u:%d", userRole.UserID), fmt.Sprintf("r:%d", userRole.RoleID))
		if err != nil {
			return err
		}
	}
	logging.Context(ctx).Info("Casbin loadMerchantPermissions",
		zap.Duration("cost", time.Since(start)),
		zap.String("merchant_no", merchantNo))

	return nil
}

// getMerchantCasbinCache 获取商户权限缓存
func (csbn *Casbinx) getMerchantCasbinCache(ctx context.Context, merchantNo string) (*MerchantCasbinCache, error) {

	start := time.Now()
	// 从缓存中获取
	val, ok, err := csbn.Cache.Get(ctx, consts.CacheNSForMerchantCasbin, merchantNo)
	if err != nil {
		return nil, err
	} else if ok {
		// 缓存命中, 转换成对象
		var cache MerchantCasbinCache
		err := json.Unmarshal([]byte(val), &cache)
		if err != nil {
			return nil, err
		}
		return &cache, nil
	}
	// 缓存未命中, 从数据库获取
	// 获取商户角色
	var merchantRoles []*model.MerchantRoleModel
	db := util.GetDB(ctx, csbn.DB)
	err = db.Select("id, merchant_no, permissions").
		Where("merchant_no = ?", merchantNo).
		Where("state = ?", model.MerchantRoleStateEnabled).
		Find(&merchantRoles).Error
	if err != nil {
		return nil, err
	}
	// 转换成缓存对象
	merchantRolesCache := make([]*MerchantCasbinRole, 0)
	for _, role := range merchantRoles {
		merchantRolesCache = append(merchantRolesCache, &MerchantCasbinRole{
			ID:          role.ID,
			Permissions: role.Permissions,
		})
	}

	// 获取商户角色
	var merchantEmployees []*model.MerchantEmployeeModel

	if err = db.Where("merchant_no = ?", merchantNo).
		Where("state = ?", model.EmployeeStateActive).
		Preload("Roles", "state = ?", model.MerchantRoleStateEnabled).
		Find(&merchantEmployees).Error; err != nil {
		return nil, err
	}
	// 转换成缓存对象
	merchantEmployeesCache := make([]*MerchantCasbinUserRole, 0)
	for _, employee := range merchantEmployees {
		for _, role := range employee.Roles {
			merchantEmployeesCache = append(merchantEmployeesCache, &MerchantCasbinUserRole{
				UserID: employee.UserID,
				RoleID: role.ID,
			})
		}
	}

	// 写入缓存
	cache := MerchantCasbinCache{
		MerchantNo:    merchantNo,
		MerchantRoles: merchantRolesCache,
		UserRoles:     merchantEmployeesCache,
	}
	err = csbn.Cache.Set(ctx, consts.CacheNSForMerchantCasbin, merchantNo, cache.String())
	if err != nil {
		logging.Context(ctx).Error("Failed to set cache", zap.Error(err), zap.String("key", consts.CacheNSForMerchantCasbin+merchantNo))
		return nil, err
	}
	logging.Context(ctx).Info("Casbin getMerchantCasbinCache",
		zap.Duration("cost", time.Since(start)),
		zap.String("merchant_no", merchantNo))
	return &cache, nil
}

func (csbn *Casbinx) Release(ctx context.Context) error {
	return nil
}

// Package swagger Code generated by swaggo/swag. DO NOT EDIT
package swagger

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v2/": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "订单相关接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/4/11 16:25"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/auth/info": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "登录接口V2"
                ],
                "summary": "获取用户信息",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/11/18 17:11"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/auth/login": {
            "post": {
                "tags": [
                    "登录接口V2"
                ],
                "summary": "登录接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.LoginResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/11/18 17:10"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/auth/logout": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "登录接口V2"
                ],
                "summary": "退出登录",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "message": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/11/18 17:19"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/auth/merchants": {
            "post": {
                "tags": [
                    "用户相关接口"
                ],
                "summary": "获取用户商户列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.MerchantBasicResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/17 17:41"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/auth/send-verify-sms": {
            "post": {
                "tags": [
                    "登录接口V2"
                ],
                "summary": "发送忘记密码短信",
                "parameters": [
                    {
                        "description": "发送短信信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.SendSmsForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "message": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2025/01/24 17:19"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/auth/verify-sms": {
            "post": {
                "tags": [
                    "登录接口V2"
                ],
                "summary": "验证忘记密码短信",
                "parameters": [
                    {
                        "description": "验证码信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.VerifySmsForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "message": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2025/01/24 17:19"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/bill/detail/{orderId}": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "账单接口"
                ],
                "summary": "获取订单详情",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/27 13:31"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/bill/list": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "账单接口"
                ],
                "summary": "获取订单列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "Data": {
                                            "$ref": "#/definitions/bill_resource.BillListResource"
                                        },
                                        "Top": {
                                            "$ref": "#/definitions/service.BillTotals"
                                        },
                                        "Total": {
                                            "type": "integer"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/27 13:29"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/debt/holders": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "description": "获取赊账人列表",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "赊账相关接口"
                ],
                "summary": "获取赊账人列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码，默认1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页数量，默认20",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词（手机号、姓名）",
                        "name": "keyword",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "状态（0禁用，1启用）",
                        "name": "state",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.DebtHolderResource"
                                            }
                                        },
                                        "meta": {
                                            "$ref": "#/definitions/util.PaginationResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/05/13 11:10"
                ],
                "X-Version": [
                    "2.0"
                ]
            },
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "description": "创建赊账人",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "赊账相关接口"
                ],
                "summary": "创建赊账人",
                "parameters": [
                    {
                        "description": "赊账人信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DebtHolderRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.DebtHolderResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/05/13 11:10"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/debt/holders/{id}": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "description": "获取赊账人详情",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "赊账相关接口"
                ],
                "summary": "获取赊账人详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "赊账人ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.DebtHolderResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/05/13 11:10"
                ],
                "X-Version": [
                    "2.0"
                ]
            },
            "put": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "description": "更新赊账人",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "赊账相关接口"
                ],
                "summary": "更新赊账人",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "赊账人ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "赊账人信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DebtHolderRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.DebtHolderResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/05/13 11:11"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/debt/holders/{id}/status": {
            "put": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "description": "更新赊账人状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "赊账相关接口"
                ],
                "summary": "更新赊账人状态",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "赊账人ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "赊账人状态",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DebtHolderStatusRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/05/13 11:11"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/debt/repayment/micro-pay": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "赊账还款相关接口"
                ],
                "summary": "微信付款码还款",
                "parameters": [
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DebtRepaymentMicroPayForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/05/14 16:05"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/debt/repayment/native-pay": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "赊账还款相关接口"
                ],
                "summary": "微信扫码支付还款",
                "parameters": [
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DebtRepaymentRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/05/14 16:05"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/debt/repayment/offline-pay": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "赊账还款相关接口"
                ],
                "summary": "线下支付还款 : 现金/个人微信/个人支付宝等线下支付方式都走这个接口",
                "parameters": [
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DebtRepaymentOfflineForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/05/14 16:49"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/debt/repayment/query/{paymentNo}": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "赊账还款相关接口"
                ],
                "summary": "支付结果查询",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/05/14 17:52"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/debt/transactions": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "description": "获取赊账交易记录列表，支持按赊账人ID、交易类型和创建时间筛选",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "赊账相关接口"
                ],
                "summary": "获取赊账交易记录列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码，默认1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页数量，默认20",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "赊账人ID",
                        "name": "holder_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "交易类型 1 赊账 2 结账",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "开始时间 格式：2006-01-02 15:04:05",
                        "name": "begin_at",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "结束时间 格式：2006-01-02 15:04:05",
                        "name": "end_at",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.DebtTransactionResource"
                                            }
                                        },
                                        "meta": {
                                            "$ref": "#/definitions/util.PaginationResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/05/13 18:10"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/debt/transactions/{id}": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "description": "获取赊账交易记录详情",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "赊账相关接口"
                ],
                "summary": "获取赊账交易记录详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "交易记录ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.DebtTransactionResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/05/13 18:10"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/handover/handover": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "交接班接口"
                ],
                "summary": "交班",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/1/22 17:28"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/handover/info": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "交接班接口"
                ],
                "summary": "获取交接班详情",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/handover_resource.HandoverResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/1/22 17:10"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/handover/open": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "交接班接口"
                ],
                "summary": "开班",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/1/22 17:06"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/local/printers": {
            "post": {
                "description": "创建打印机",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "打印机管理"
                ],
                "summary": "创建打印机",
                "parameters": [
                    {
                        "description": "打印机信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.PrinterCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.PrinterResource"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/04/25 17:20"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/local/printers/foods": {
            "get": {
                "description": "获取打印机与美食的关联关系",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "打印机管理"
                ],
                "summary": "获取打印机与美食的关联关系",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/food_printer_resource.CategoryResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/04/25 17:20"
                ],
                "X-Version": [
                    "2.0"
                ]
            },
            "post": {
                "description": "保存打印机与美食的关联关系",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "打印机管理"
                ],
                "summary": "保存打印机与美食的关联关系",
                "parameters": [
                    {
                        "description": "打印机与美食关联关系",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.PrinterFoodsRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/04/25 17:20"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/local/printers/{id}": {
            "get": {
                "description": "获取打印机详情",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "打印机管理"
                ],
                "summary": "获取打印机详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "打印机ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.PrinterResource"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/04/25 17:20"
                ],
                "X-Version": [
                    "2.0"
                ]
            },
            "put": {
                "description": "更新打印机",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "打印机管理"
                ],
                "summary": "更新打印机",
                "parameters": [
                    {
                        "type": "string",
                        "description": "打印机ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "打印机信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.PrinterUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.PrinterResource"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/04/25 17:20"
                ],
                "X-Version": [
                    "2.0"
                ]
            },
            "delete": {
                "description": "删除打印机",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "打印机管理"
                ],
                "summary": "删除打印机",
                "parameters": [
                    {
                        "type": "string",
                        "description": "打印机ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/04/25 17:20"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/local/printers/{id}/status": {
            "put": {
                "description": "更新打印机状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "打印机管理"
                ],
                "summary": "更新打印机状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "打印机ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "状态信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.PrinterStatusRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/04/25 17:20"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/merchant/roles": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "商户管理"
                ],
                "summary": "角色列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "商户号",
                        "name": "Merchantno",
                        "in": "header",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.MerchantRoleItemResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2024/12/19 12:27"
                ],
                "X-Version": [
                    "2.0"
                ]
            },
            "put": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "商户管理"
                ],
                "summary": "更新角色",
                "parameters": [
                    {
                        "description": "更新角色请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/merchant_role_request.UpdateRoleRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2024/12/26 12:27"
                ],
                "X-Version": [
                    "2.0"
                ]
            },
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "商户管理"
                ],
                "summary": "创建角色",
                "parameters": [
                    {
                        "description": "创建角色请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/merchant_role_request.CreateRoleRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2024/12/25 12:27"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/merchant/roles/:id": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "商户管理"
                ],
                "summary": "获取角色",
                "parameters": [
                    {
                        "type": "string",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.MerchantRoleItemResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2024/12/26 12:33"
                ],
                "X-Version": [
                    "2.0"
                ]
            },
            "delete": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "商户管理"
                ],
                "summary": "删除角色",
                "parameters": [
                    {
                        "type": "string",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2024/12/26 12:33"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/merchant/roles/{id}/state": {
            "put": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "商户管理"
                ],
                "summary": "更新角色状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新角色状态请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/merchant_role_request.UpdateRoleStateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2024/12/26 12:33"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/order/:order_id": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "订单详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "订单ID",
                        "name": "order_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Habibulla"
                ],
                "X-Date": [
                    "2025/4/2 12:40"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/order/refund/{orderId}": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "支付相关接口"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "订单ID",
                        "name": "orderId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/1/5 16:25"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/orders": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "创建订单",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "客户人数",
                        "name": "customers_count",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "餐桌ID",
                        "name": "table_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Habibulla"
                ],
                "X-Date": [
                    "2025/4/2 10:37"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/orders/add-foods/:id": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "加菜",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "订单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "加菜数据",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.FoodSellClearDataRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Habibulla"
                ],
                "X-Date": [
                    "2025/4/9 10:50"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/orders/cancel-food/:order_id/:od_id": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "退菜",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "订单ID",
                        "name": "order_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "订单详细ID",
                        "name": "od_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "退菜数据",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.CancelFood"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Habibulla"
                ],
                "X-Date": [
                    "2025/4/9 23:07"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/orders/changeTable/:order_id": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "订单相关接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/4/16 19:45"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/orders/checkout/:id": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "订单结算",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "订单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/4/9 16:16"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/orders/collage/:order_id": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "并单",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "被合并订单ID",
                        "name": "order_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "目标订单ID",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/order_request.CollageOrderRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Habibulla"
                ],
                "X-Date": [
                    "2025/4/17 10:36"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/orders/payment-list/:id": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "订单支付列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "订单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "merdan"
                ],
                "X-Date": [
                    "2025/4/2 10:39"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/orders/urge/:table_id/:order_id/:order_detail_id": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "催单",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "餐桌ID",
                        "name": "table_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "订单ID",
                        "name": "order_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "订单详细ID",
                        "name": "order_detail_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Habibulla"
                ],
                "X-Date": [
                    "2025/4/10 16:11"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/payment/customer-pay": {
            "post": {
                "description": "会员支付",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "支付相关接口"
                ],
                "summary": "会员支付",
                "parameters": [
                    {
                        "description": "支付请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.CustomerPaymentForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "支付响应",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "400": {
                        "description": "错误响应",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "422": {
                        "description": "错误响应",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2025-01-02"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/payment/debt-pay": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "description": "赊账支付",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "支付相关接口"
                ],
                "summary": "赊账支付",
                "parameters": [
                    {
                        "description": "支付请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DebtPaymentForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "支付响应",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "400": {
                        "description": "错误响应",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "422": {
                        "description": "错误响应",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025-05-14 10:00:00"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/payment/micro-pay": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "支付相关接口"
                ],
                "summary": "付款码支付接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/schema.PaymentResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/5 16:32"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/payment/qrcode": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "云端订单支付接口"
                ],
                "summary": "云端订单支付接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/4/17 11:37"
                ],
                "X-Version": [
                    "2.0"
                ]
            },
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "支付相关接口"
                ],
                "summary": "获取统一支付二维码",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/9 16:33"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/payment/query/{paymentNo}": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "支付相关接口"
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/schema.PaymentResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/11/27 16:34"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/payment/reverse": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "支付相关接口"
                ],
                "summary": "撤销支付(全额退款)",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/schema.RefundResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/23 16:30"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/payment/wechat/debt-repayment-notify/{paymentNo}": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "支付相关接口"
                ],
                "summary": "处理赊账还款结果",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/05/14 17:05"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/payment/wechat/jsapi/{paymentNo}": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "支付相关接口"
                ],
                "summary": "微信JSAPI支付(小程序)",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/wechat.JSAPIPayParams"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/9 17:21"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/payment/wechat/payment-notify/{paymentNo}": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "支付相关接口"
                ],
                "summary": "处理微信支付通知",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/23 16:30"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/payment/wechat/recharge-notify/{paymentNo}": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "支付相关接口"
                ],
                "summary": "处理VIP充值通知",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/1/16 17:50"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/payment/wechat/refund-notify/{paymentNo}": {
            "post": {
                "tags": [
                    "支付相关接口"
                ],
                "summary": "处理微信退款通知",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/9 17:22"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/permissions": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "获取权限列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/permission_response.PermissionResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2024/12/25 12:27"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/printers": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步餐厅打印机接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.PrinterResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/04/25 17:20"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/recharge/micro-pay": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "VIP充值相关接口"
                ],
                "summary": "微信付款码充值",
                "parameters": [
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.RechargeMicroPayForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/1/17 16:05"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/recharge/native-pay": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "微信扫码支付充值",
                "parameters": [
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.RechargeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/1/17 16:05"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/recharge/offline-pay": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "VIP充值相关接口"
                ],
                "summary": "线下支付充值 : 现金/个人微信/个人支付宝等线下支付方式都走这个接口",
                "parameters": [
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.RechargeOfflineForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/1/17 16:49"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/recharge/query/{paymentNo}": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "VIP充值相关接口"
                ],
                "summary": "支付结果查询",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/1/17 17:52"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/scan/open_id": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "扫码点菜接口"
                ],
                "summary": "根据code获取微信用户信息",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/scan_resource.OpenIDResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/2/8 11:39"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/scan/order": {
            "post": {
                "security": [
                    {
                        "WechatTokenAuth": []
                    }
                ],
                "tags": [
                    "扫码点菜部分"
                ],
                "summary": "创建订单",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/2/20 16:00"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/scan/order/detail/{id}": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "扫码点菜部分"
                ],
                "summary": "获取订单详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "订单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/2/24 16:03"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/scan/order/list": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "扫码点菜部分"
                ],
                "summary": "获取订单列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "微信用户ID(系统填充)",
                        "name": "-",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "type": "integer",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "name": "state",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/scan_resource.ScanOrderResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/2/24 16:02"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/scan/service/commit": {
            "post": {
                "security": [
                    {
                        "WechatTokenAuth": []
                    }
                ],
                "tags": [
                    "扫码点菜"
                ],
                "summary": "小程序呼叫服务",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/3/12 15:45"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/areas": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步餐厅区域接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.AreaResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": {
                    "name": "merdan"
                },
                "X-Date": [
                    "2024-12-02 12:50:39"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/food-categories": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "获取商户的食品分类列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.FoodCategoryResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/3 16:19"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/food-combos": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步菜品套餐接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.FoodComboResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/2 16:21"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/food-printers": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步美食打印机关联信息接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.FoodPrinterResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/2 16:23"
                ],
                "X-Version": [
                    "2.0"
                ]
            },
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步美食打印机接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/2 16:23"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/foods": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步菜品接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.FoodResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/2 16:04"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/foods/sell-clear-data": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步菜品沽清数据接口",
                "parameters": [
                    {
                        "description": "沽清数据",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.FoodSellClearDataRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/2/19 15:38"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/merchant-configs": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步商户设置接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.MerchantConfigResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/2 16:25"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/merchant-info-updates": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "商家各项信息更新时间接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.MerchantInfoUpdateResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/2 16:27"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/payment-types": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步平台支付方式接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.PaymentTypeResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/9 17:02"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/permissions": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步权限列表接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.PermissionResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/10 16:42"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/printers": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步餐厅打印机接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.PrinterResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/2 16:43"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/remark-categories": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步备注分类",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.RemarkCategoryResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/2 17:06"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/remarks": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "获取商户备注列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.RemarkResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/2 16:44"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/roles": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步商家角色权限列表接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.MerchantRoleResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/2 16:29"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/sync-order": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步本地订单到云端接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "message": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/12 16:30"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/sync-printer": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步打印机信息 (本地  ===》 云端)",
                "parameters": [
                    {
                        "description": "打印机信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.PrinterSyncRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/2 16:43"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/tables": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步餐桌信息接口",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.TableResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/3 17:06"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/sync/users": {
            "get": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "同步用户数据",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.UserResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/3 17:07"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/table": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "餐台信息列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "区域ID",
                        "name": "area_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "状态",
                        "name": "table_status",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "匹配",
                        "name": "keyword",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/resource.TableDataResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Habibulla"
                ],
                "X-Date": [
                    "2025/3/27 16:11"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/table-details/:id": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "餐桌详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "餐桌ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Habibulla"
                ],
                "X-Date": [
                    "2025/4/2 10:38"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/api/v2/terminals": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "description": "获取状态为开通的客户端列表",
                "tags": [
                    "终端管理"
                ],
                "summary": "获取状态为开通的客户端列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/resource.TerminalResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/4/29 12:48"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/cloud/api/v2/orders/cancel-all-foods/:order_id": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "全单退菜",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/4/17 10:35"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/cloud/api/v2/orders/split/:order_id": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "订单相关接口"
                ],
                "summary": "拆分订单",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "订单ID",
                        "name": "order_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "拆弹数据",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/order_request.SplitOrder"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Habibulla"
                ],
                "X-Date": [
                    "2025/4/15 11:50"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/cloud/api/v2/payment/customer-pay": {
            "post": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "云端订单支付接口"
                ],
                "summary": "会员支付",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/4/10 19:35"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/cloud/api/v2/payment/debt-pay": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "description": "赊账支付",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "支付相关接口"
                ],
                "summary": "赊账支付",
                "parameters": [
                    {
                        "description": "支付请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.DebtPaymentForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "支付响应",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "400": {
                        "description": "错误响应",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "422": {
                        "description": "错误响应",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025-05-14 10:00:00"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/cloud/api/v2/payment/query/{paymentNo}": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "云端订单支付接口"
                ],
                "parameters": [
                    {
                        "type": "string",
                        "description": "支付信息编号",
                        "name": "paymentNo",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/schema.PaymentResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/4/10 19:56"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/cloud/api/v2/payment/reverse": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "tags": [
                    "支付相关接口"
                ],
                "summary": "撤销支付(全额退款)",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/schema.RefundResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2024/12/23 16:30"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/cloud/api/v2/user/my-orders": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "点菜端-我的-我的订单",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "Data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/order_resource.MyOrderListItemResource"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/4/15 17:06"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/cloud/api/v2/user/order/{orderID}": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "tags": [
                    "同步接口"
                ],
                "summary": "点菜端-我的-订单详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "订单ID",
                        "name": "orderID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "Data": {
                                            "$ref": "#/definitions/order_resource.MyOrderResource"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/4/17 11:55"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/printer/print": {
            "post": {
                "security": [
                    {
                        "ServerTokenAuth": []
                    }
                ],
                "description": "打印",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "打印"
                ],
                "summary": "打印",
                "parameters": [
                    {
                        "description": "打印数据",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/print_request.PrintRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/util.ResponseResult"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "message": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                },
                "X-Author": {
                    "name": "Alim Kirem"
                },
                "X-Date": [
                    "2025-01-14 12:50:39"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/v2/merchant/employees": {
            "get": {
                "security": [
                    {
                        "ApiTokenAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "商户员工"
                ],
                "summary": "获取商户员工列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "状态",
                        "name": "state",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "手机号",
                        "name": "mobile",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "商户号",
                        "name": "Merchantno",
                        "in": "header",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "商户员工列表",
                        "schema": {
                            "$ref": "#/definitions/employee_resource.EmployeeListResource"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2024-12-31"
                ],
                "X-Version": [
                    "2.0"
                ]
            },
            "post": {
                "description": "创建商户员工",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "商户员工"
                ],
                "summary": "创建商户员工",
                "parameters": [
                    {
                        "description": "商户员工信息",
                        "name": "merchant_employee",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/merchant_employee_request.CreateEmployeeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "商户员工信息",
                        "schema": {
                            "$ref": "#/definitions/employee_resource.ListItemResource"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2025-01-02"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/v2/merchant/employees/{id}": {
            "get": {
                "description": "获取员工信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "商户员工"
                ],
                "summary": "获取员工信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "员工ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "商户员工信息",
                        "schema": {
                            "$ref": "#/definitions/employee_resource.ListItemResource"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2025-01-02"
                ],
                "X-Version": [
                    "2.0"
                ]
            },
            "put": {
                "description": "更新商家员工信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "商户员工"
                ],
                "summary": "更新商家员工信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "员工ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "商户员工信息",
                        "name": "merchant_employee",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/merchant_employee_request.UpdateEmployeeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "商户员工信息",
                        "schema": {
                            "$ref": "#/definitions/employee_resource.ListItemResource"
                        }
                    }
                },
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2025-01-02"
                ],
                "X-Version": [
                    "2.0"
                ]
            },
            "delete": {
                "description": "删除商户员工",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "商户员工"
                ],
                "summary": "删除商户员工",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "员工ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {},
                "X-Author": [
                    "Alim Kirem"
                ],
                "X-Date": [
                    "2025-01-02"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        },
        "/v2/payment/micro": {
            "post": {
                "tags": [
                    "云端订单支付接口"
                ],
                "summary": "微信支付",
                "parameters": [
                    {
                        "description": "body",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/order_request.MicroPayRequest"
                        }
                    },
                    {
                        "type": "integer",
                        "description": "订单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    },
                    "400": {
                        "description": "错误",
                        "schema": {
                            "$ref": "#/definitions/util.ResponseResult"
                        }
                    }
                },
                "X-Author": [
                    "Merdan"
                ],
                "X-Date": [
                    "2025/4/9 12:16"
                ],
                "X-Version": [
                    "2.0"
                ]
            }
        }
    },
    "definitions": {
        "bill_resource.BillListItemResource": {
            "type": "object",
            "properties": {
                "cancel_amount": {
                    "description": "取消金额",
                    "type": "number"
                },
                "cashier_name": {
                    "description": "收银员姓名",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "creator_name": {
                    "description": "下单人姓名",
                    "type": "string"
                },
                "ignore_price": {
                    "description": "优惠金额",
                    "type": "number"
                },
                "is_scan_order": {
                    "description": "是否扫码点单",
                    "type": "boolean"
                },
                "order_id": {
                    "description": "订单ID",
                    "type": "integer"
                },
                "order_no": {
                    "description": "订单号",
                    "type": "string"
                },
                "original_price": {
                    "description": "原价",
                    "type": "number"
                },
                "paid_at": {
                    "description": "支付时间",
                    "type": "string"
                },
                "price": {
                    "description": "实付金额",
                    "type": "number"
                },
                "table_name": {
                    "description": "餐桌名称",
                    "type": "string"
                },
                "table_no": {
                    "description": "餐桌号",
                    "type": "string"
                },
                "tax_ticket": {
                    "description": "是否开票 0-否 1-是",
                    "type": "boolean"
                },
                "total_refund_amount": {
                    "description": "总退款金额",
                    "type": "number"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "bill_resource.BillListResource": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/bill_resource.BillListItemResource"
                    }
                },
                "message": {
                    "type": "string"
                },
                "meta": {
                    "$ref": "#/definitions/util.PaginationResult"
                },
                "top": {}
            }
        },
        "employee_resource.EmployeeListResource": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/employee_resource.ListItemResource"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "employee_resource.ListItemResource": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "format": "2006-01-02 15:04:05"
                },
                "deleted_at": {
                    "type": "string",
                    "format": "2006-01-02 15:04:05"
                },
                "id": {
                    "type": "integer"
                },
                "is_owner": {
                    "type": "boolean"
                },
                "merchant_no": {
                    "type": "string"
                },
                "name_ug": {
                    "type": "string"
                },
                "name_zh": {
                    "type": "string"
                },
                "no": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/employee_resource.RoleResource"
                    }
                },
                "state": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string",
                    "format": "2006-01-02 15:04:05"
                },
                "user_id": {
                    "type": "integer"
                }
            }
        },
        "employee_resource.RoleResource": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "name_cn": {
                    "type": "string"
                },
                "name_ug": {
                    "type": "string"
                }
            }
        },
        "food_printer_resource.CategoryResource": {
            "type": "object",
            "properties": {
                "foods": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/food_printer_resource.FoodResource"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "name_ug": {
                    "type": "string"
                },
                "name_zh": {
                    "type": "string"
                }
            }
        },
        "food_printer_resource.FoodResource": {
            "type": "object",
            "properties": {
                "food_category_id": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "name_ug": {
                    "type": "string"
                },
                "name_zh": {
                    "type": "string"
                },
                "printers": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/food_printer_resource.PrinterResource"
                    }
                }
            }
        },
        "food_printer_resource.PrinterResource": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "name_ug": {
                    "type": "string"
                },
                "name_zh": {
                    "type": "string"
                }
            }
        },
        "handover_resource.BusinessSituationResource": {
            "type": "object",
            "properties": {
                "customer_avg": {
                    "type": "number"
                },
                "customer_count": {
                    "type": "integer"
                },
                "order_avg": {
                    "type": "number"
                },
                "order_count": {
                    "type": "integer"
                },
                "real_paid_amount": {
                    "type": "number"
                },
                "receivable_amount": {
                    "type": "number"
                },
                "total_discount": {
                    "type": "number"
                }
            }
        },
        "handover_resource.HandoverDetailResource": {
            "type": "object",
            "properties": {
                "debt_repayment_amount": {
                    "description": "赊账还款金额",
                    "type": "number"
                },
                "debt_repayment_cash_amount": {
                    "description": "退款金额",
                    "type": "number"
                },
                "handover_log_id": {
                    "description": "交班记录编号",
                    "type": "integer"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "order_amount": {
                    "description": "订单金额",
                    "type": "number"
                },
                "order_cash_amount": {
                    "description": "订单现金金额",
                    "type": "number"
                },
                "order_count": {
                    "description": "订单数量",
                    "type": "integer"
                },
                "payment_type": {
                    "description": "支付类型名称",
                    "type": "string"
                },
                "payment_type_id": {
                    "description": "支付类型编号",
                    "type": "integer"
                },
                "refund_amount": {
                    "description": "退款金额",
                    "type": "number"
                },
                "refund_cash_amount": {
                    "description": "退款金额",
                    "type": "number"
                },
                "total": {
                    "description": "总金额",
                    "type": "number"
                },
                "user_id": {
                    "description": "交班用户编号",
                    "type": "integer"
                },
                "vip_cash_recharge_amount": {
                    "description": "会员现金充值金额",
                    "type": "number"
                },
                "vip_count": {
                    "description": "新增会员数量",
                    "type": "integer"
                },
                "vip_recharge_amount": {
                    "description": "会员充值金额",
                    "type": "number"
                }
            }
        },
        "handover_resource.HandoverResource": {
            "type": "object",
            "properties": {
                "business_situation": {
                    "$ref": "#/definitions/handover_resource.BusinessSituationResource"
                },
                "cashInfo": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handover_resource.HandoverDetailResource"
                    }
                },
                "overview": {
                    "$ref": "#/definitions/schema.HandoverOverview"
                },
                "vip": {
                    "$ref": "#/definitions/handover_resource.VipResource"
                }
            }
        },
        "handover_resource.VipResource": {
            "type": "object",
            "properties": {
                "present_recharge_amount": {
                    "type": "number"
                },
                "recharge_amount": {
                    "type": "number"
                },
                "vip_count": {
                    "type": "integer"
                }
            }
        },
        "merchant_employee_request.CreateEmployeeRequest": {
            "type": "object",
            "required": [
                "name_ug",
                "name_zh",
                "no",
                "phone"
            ],
            "properties": {
                "name_ug": {
                    "type": "string",
                    "maxLength": 64
                },
                "name_zh": {
                    "type": "string",
                    "maxLength": 64
                },
                "no": {
                    "type": "string",
                    "maxLength": 9
                },
                "phone": {
                    "type": "string"
                },
                "role_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "state": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                }
            }
        },
        "merchant_employee_request.UpdateEmployeeRequest": {
            "type": "object",
            "required": [
                "name_ug",
                "name_zh",
                "no"
            ],
            "properties": {
                "name_ug": {
                    "type": "string",
                    "maxLength": 64
                },
                "name_zh": {
                    "type": "string",
                    "maxLength": 64
                },
                "no": {
                    "type": "string",
                    "maxLength": 9
                },
                "role_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "state": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                }
            }
        },
        "merchant_role_request.CreateRoleRequest": {
            "type": "object",
            "required": [
                "name_ug",
                "name_zh",
                "permissions"
            ],
            "properties": {
                "name_ug": {
                    "type": "string",
                    "maxLength": 64,
                    "minLength": 1
                },
                "name_zh": {
                    "type": "string",
                    "maxLength": 64,
                    "minLength": 1
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "state": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                }
            }
        },
        "merchant_role_request.UpdateRoleRequest": {
            "type": "object",
            "required": [
                "name_ug",
                "name_zh",
                "permissions"
            ],
            "properties": {
                "name_ug": {
                    "type": "string",
                    "maxLength": 64,
                    "minLength": 1
                },
                "name_zh": {
                    "type": "string",
                    "maxLength": 64,
                    "minLength": 1
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "state": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                }
            }
        },
        "merchant_role_request.UpdateRoleStateRequest": {
            "type": "object",
            "properties": {
                "state": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                }
            }
        },
        "model.CashierConfig": {
            "type": "object",
            "properties": {
                "business_ticket": {
                    "description": "日营业额",
                    "type": "boolean"
                },
                "delivery_ticket": {
                    "description": "换班",
                    "type": "boolean"
                },
                "pre_ticket": {
                    "description": "预结账",
                    "type": "boolean"
                },
                "recharge_ticket": {
                    "description": "会员充值记录",
                    "type": "boolean"
                },
                "statement_ticket": {
                    "description": "结账单",
                    "type": "boolean"
                }
            }
        },
        "model.ComboInfo": {
            "type": "object",
            "properties": {
                "count": {
                    "description": "美食数量",
                    "type": "number"
                },
                "food_id": {
                    "description": "美食编号",
                    "type": "integer"
                },
                "id": {
                    "description": "套餐detail编号",
                    "type": "integer"
                }
            }
        },
        "model.KitchenConfig": {
            "type": "object",
            "properties": {
                "back_ticket": {
                    "description": "退菜",
                    "type": "boolean"
                },
                "order_merge_ticket": {
                    "description": "合并订单",
                    "type": "boolean"
                },
                "split_ticket": {
                    "description": "一单一个",
                    "type": "boolean"
                },
                "table_change_ticket": {
                    "description": "转台",
                    "type": "boolean"
                }
            }
        },
        "model.TableDataModel": {
            "type": "object",
            "properties": {
                "area_id": {
                    "description": "区域ID",
                    "type": "integer"
                },
                "customers_count": {
                    "type": "integer"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "last_wechat_time": {
                    "type": "string"
                },
                "name_ug": {
                    "description": "名称(维语)",
                    "type": "string"
                },
                "name_zh": {
                    "description": "名称(中文)",
                    "type": "string"
                },
                "no": {
                    "description": "编号",
                    "type": "string"
                },
                "order_details_count": {
                    "type": "integer"
                },
                "orders_count": {
                    "type": "integer"
                },
                "seating_capacity": {
                    "type": "integer"
                },
                "sort": {
                    "type": "integer"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "table_status": {
                    "type": "integer"
                }
            }
        },
        "order_request.CollageOrderRequest": {
            "type": "object",
            "properties": {
                "target_order_id": {
                    "type": "integer"
                }
            }
        },
        "order_request.MicroPayRequest": {
            "type": "object",
            "required": [
                "amount",
                "order_id",
                "order_no",
                "payment_id",
                "payment_no"
            ],
            "properties": {
                "amount": {
                    "type": "number",
                    "minimum": 0.01
                },
                "auth_code": {
                    "type": "string"
                },
                "order_id": {
                    "type": "integer"
                },
                "order_no": {
                    "type": "string",
                    "maxLength": 32,
                    "minLength": 13
                },
                "payment_id": {
                    "type": "integer"
                },
                "payment_no": {
                    "type": "string",
                    "maxLength": 32,
                    "minLength": 13
                }
            }
        },
        "order_request.SplitOrder": {
            "type": "object",
            "properties": {
                "customers_count": {
                    "type": "integer"
                },
                "order_details": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "foods_count": {
                                "type": "number"
                            },
                            "id": {
                                "type": "integer"
                            }
                        }
                    }
                }
            }
        },
        "order_resource.MyOrderDetailResource": {
            "type": "object",
            "properties": {
                "combo_info": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.ComboInfo"
                    }
                },
                "cost_price": {
                    "type": "number"
                },
                "created_at": {
                    "type": "string"
                },
                "food_format_id": {
                    "type": "integer"
                },
                "food_id": {
                    "type": "integer"
                },
                "food_name": {
                    "type": "string"
                },
                "foods_count": {
                    "type": "number"
                },
                "id": {
                    "type": "integer"
                },
                "is_combo": {
                    "type": "boolean"
                },
                "is_print": {
                    "type": "boolean"
                },
                "is_sync": {
                    "type": "boolean"
                },
                "merchant_no": {
                    "type": "string"
                },
                "order_details_id": {
                    "description": "退菜订单的原始订单id",
                    "type": "integer"
                },
                "order_id": {
                    "type": "integer"
                },
                "original_price": {
                    "type": "number"
                },
                "price": {
                    "type": "number"
                },
                "remarks": {
                    "type": "string"
                },
                "state": {
                    "description": "订单状态 - 1 新订单（人数确定，未点菜）、2 未支付订单（已点菜，订单已提交）、3 已结账订单（已结账）、4 退菜、5 取消退菜 、6 被并单",
                    "type": "integer"
                },
                "total_price": {
                    "type": "number"
                },
                "updated_at": {
                    "type": "string"
                },
                "user": {
                    "type": "string"
                },
                "user_id": {
                    "type": "integer"
                },
                "vip_price": {
                    "type": "number"
                }
            }
        },
        "order_resource.MyOrderListItemResource": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "customers_count": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "order_no": {
                    "type": "string"
                },
                "paid_at": {
                    "type": "string"
                },
                "price": {
                    "type": "number"
                },
                "refund_price": {
                    "type": "number"
                },
                "table_name": {
                    "type": "string"
                },
                "table_no": {
                    "type": "string"
                }
            }
        },
        "order_resource.MyOrderPaymentResource": {
            "type": "object",
            "properties": {
                "amount": {
                    "description": "付款金额",
                    "type": "number"
                },
                "cashier_id": {
                    "description": "收银员ID",
                    "type": "integer"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "icon": {
                    "description": "付款图标",
                    "type": "string"
                },
                "id": {
                    "description": "主键，自增",
                    "type": "integer"
                },
                "name": {
                    "description": "付款类型名称",
                    "type": "string"
                },
                "order_no": {
                    "description": "订单编号",
                    "type": "string"
                },
                "paid_at": {
                    "description": "付款时间",
                    "type": "string"
                },
                "pay_type": {
                    "description": "付款类型",
                    "type": "string"
                },
                "payment_no": {
                    "description": "付款编号",
                    "type": "string"
                },
                "payment_type_id": {
                    "description": "付款类型ID",
                    "type": "integer"
                },
                "refund_amount": {
                    "description": "退款金额",
                    "type": "number"
                },
                "remark": {
                    "description": "备注",
                    "type": "string"
                },
                "status": {
                    "description": "付款状态",
                    "type": "integer"
                }
            }
        },
        "order_resource.MyOrderResource": {
            "type": "object",
            "properties": {
                "canceled_foods": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/order_resource.MyOrderDetailResource"
                    }
                },
                "cashier_id": {
                    "description": "结账人id",
                    "type": "integer"
                },
                "cashier_name": {
                    "description": "结账人",
                    "type": "string"
                },
                "collected_amount": {
                    "description": "收款金额",
                    "type": "number"
                },
                "created_at": {
                    "type": "string"
                },
                "customers_count": {
                    "type": "integer"
                },
                "foods_count": {
                    "type": "number"
                },
                "give_change": {
                    "description": "找零金额",
                    "type": "number"
                },
                "id": {
                    "type": "integer"
                },
                "ignore_price": {
                    "type": "number"
                },
                "order_details": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/order_resource.MyOrderDetailResource"
                    }
                },
                "order_no": {
                    "type": "string"
                },
                "original_price": {
                    "type": "number"
                },
                "paid_at": {
                    "type": "string"
                },
                "payments": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/order_resource.MyOrderPaymentResource"
                    }
                },
                "price": {
                    "type": "number"
                },
                "refund_price": {
                    "type": "number"
                },
                "refunds": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/order_resource.MyOrderDetailResource"
                    }
                },
                "remarks": {
                    "type": "string"
                },
                "table_id": {
                    "type": "integer"
                },
                "table_name": {
                    "type": "string"
                },
                "table_no": {
                    "type": "string"
                },
                "total_discount": {
                    "type": "number"
                },
                "user": {
                    "type": "string"
                },
                "user_id": {
                    "type": "integer"
                },
                "vip_price": {
                    "type": "number"
                },
                "wechat_user_id": {
                    "description": "微信用户id",
                    "type": "integer"
                }
            }
        },
        "permission_response.PermissionResource": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/permission_response.PermissionResource"
                    }
                },
                "key": {
                    "type": "string"
                },
                "name_ug": {
                    "type": "string"
                },
                "name_zh": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "print_request.PrintRequest": {
            "type": "object",
            "required": [
                "content",
                "device_id"
            ],
            "properties": {
                "content": {
                    "type": "string"
                },
                "device_id": {
                    "type": "string"
                }
            }
        },
        "request.CancelFood": {
            "type": "object",
            "properties": {
                "foods_count": {
                    "type": "number"
                },
                "remarks": {
                    "type": "string"
                }
            }
        },
        "request.CustomerPaymentForm": {
            "type": "object",
            "required": [
                "amount",
                "cashier_id",
                "customer_id",
                "order_no",
                "password",
                "payment_no"
            ],
            "properties": {
                "amount": {
                    "description": "支付金额，单位分",
                    "type": "integer",
                    "minimum": 1
                },
                "cashier_id": {
                    "description": "收银员ID",
                    "type": "integer"
                },
                "customer_id": {
                    "description": "会员ID",
                    "type": "integer"
                },
                "merchant_no": {
                    "description": "商家编号，由系统自动填充",
                    "type": "string"
                },
                "order_no": {
                    "description": "订单编号",
                    "type": "string",
                    "maxLength": 32,
                    "minLength": 16
                },
                "password": {
                    "description": "支付密码",
                    "type": "string"
                },
                "payment_no": {
                    "description": "支付信息编号",
                    "type": "string",
                    "maxLength": 32,
                    "minLength": 16
                },
                "payment_type_id": {
                    "description": "支付方式，1：微信支付， 3 现金支付，10：支付宝支付 .由系统判断填充",
                    "type": "integer"
                }
            }
        },
        "request.DebtHolderRequest": {
            "type": "object",
            "required": [
                "name_ug",
                "name_zh",
                "phone"
            ],
            "properties": {
                "credit_limit": {
                    "description": "信用额度(分)",
                    "type": "integer"
                },
                "merchantNo": {
                    "description": "商户号 (系统填充)",
                    "type": "string"
                },
                "name_ug": {
                    "description": "维语姓名",
                    "type": "string"
                },
                "name_zh": {
                    "description": "中文姓名",
                    "type": "string"
                },
                "phone": {
                    "description": "手机号",
                    "type": "string"
                },
                "status": {
                    "description": "状态：0 禁用 1 启用",
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                }
            }
        },
        "request.DebtHolderStatusRequest": {
            "type": "object",
            "properties": {
                "status": {
                    "description": "状态 0:禁用 1:启用",
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                }
            }
        },
        "request.DebtPaymentForm": {
            "type": "object",
            "required": [
                "amount",
                "cashier_id",
                "holder_id",
                "order_no",
                "payment_no"
            ],
            "properties": {
                "amount": {
                    "description": "支付金额，单位分",
                    "type": "integer",
                    "minimum": 1
                },
                "cashier_id": {
                    "description": "收银员ID",
                    "type": "integer"
                },
                "customer_id": {
                    "description": "会员ID (VIP支付时必填)",
                    "type": "integer"
                },
                "holder_id": {
                    "description": "会员ID",
                    "type": "integer"
                },
                "merchant_no": {
                    "description": "商家编号，由系统自动填充",
                    "type": "string"
                },
                "order_no": {
                    "description": "订单编号",
                    "type": "string",
                    "maxLength": 32,
                    "minLength": 16
                },
                "payment_no": {
                    "description": "支付信息编号",
                    "type": "string",
                    "maxLength": 32,
                    "minLength": 16
                },
                "payment_type_id": {
                    "description": "支付方式，1：微信支付， 3 现金支付，10：支付宝支付 .由系统判断填充",
                    "type": "integer"
                }
            }
        },
        "request.DebtRepaymentMicroPayForm": {
            "type": "object",
            "required": [
                "amount",
                "auth_code",
                "holder_id"
            ],
            "properties": {
                "amount": {
                    "description": "支付金额，单位分",
                    "type": "integer",
                    "minimum": 1
                },
                "auth_code": {
                    "description": "付款码编号",
                    "type": "string"
                },
                "holder_id": {
                    "description": "赊账人ID",
                    "type": "integer"
                }
            }
        },
        "request.DebtRepaymentOfflineForm": {
            "type": "object",
            "required": [
                "amount",
                "holder_id",
                "payment_type_id"
            ],
            "properties": {
                "amount": {
                    "description": "支付金额，单位分",
                    "type": "integer",
                    "minimum": 1
                },
                "holder_id": {
                    "description": "赊账人ID",
                    "type": "integer"
                },
                "payment_type_id": {
                    "description": "付款码编号",
                    "type": "integer"
                }
            }
        },
        "request.DebtRepaymentRequest": {
            "type": "object",
            "required": [
                "amount",
                "holder_id"
            ],
            "properties": {
                "amount": {
                    "description": "支付金额，单位分",
                    "type": "integer",
                    "minimum": 1
                },
                "holder_id": {
                    "description": "赊账人ID",
                    "type": "integer"
                }
            }
        },
        "request.FoodSellClearDataRequest": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "cell_clear_state": {
                    "description": "沽清状态：1-有库存，0-无库存",
                    "type": "boolean"
                },
                "id": {
                    "description": "菜品ID",
                    "type": "integer",
                    "minimum": 1
                },
                "remaining_count": {
                    "description": "剩余库存数量",
                    "type": "number",
                    "minimum": 0
                },
                "sell_clear_count": {
                    "description": "售清数量",
                    "type": "number",
                    "minimum": 0
                }
            }
        },
        "request.PrinterCreateRequest": {
            "type": "object",
            "required": [
                "cashier_config",
                "connection_type",
                "kitchen_config",
                "name_ug",
                "name_zh",
                "paper_width",
                "print_mode"
            ],
            "properties": {
                "buzzer": {
                    "description": "蜂鸣器",
                    "type": "boolean"
                },
                "cashier_config": {
                    "description": "收银配置",
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.CashierConfig"
                        }
                    ]
                },
                "cloud": {
                    "description": "云打印机ID",
                    "type": "string"
                },
                "connection_type": {
                    "description": "连接方式 network/usb/cloud",
                    "type": "string",
                    "enum": [
                        "network",
                        "usb",
                        "cloud"
                    ]
                },
                "ip_address": {
                    "description": "IP地址",
                    "type": "string"
                },
                "kitchen_config": {
                    "description": "厨房配置",
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.KitchenConfig"
                        }
                    ]
                },
                "name_ug": {
                    "description": "维语名称",
                    "type": "string"
                },
                "name_zh": {
                    "description": "中文名称",
                    "type": "string"
                },
                "paper_width": {
                    "description": "纸张宽度",
                    "type": "integer",
                    "enum": [
                        58,
                        80
                    ]
                },
                "print_mode": {
                    "description": "打印模式",
                    "type": "string",
                    "enum": [
                        "text",
                        "image"
                    ]
                },
                "usb_port": {
                    "description": "USB端口",
                    "type": "string"
                }
            }
        },
        "request.PrinterFoodsRequest": {
            "type": "object",
            "required": [
                "food_ids",
                "printer_id"
            ],
            "properties": {
                "food_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "printer_id": {
                    "type": "string"
                }
            }
        },
        "request.PrinterStatusRequest": {
            "type": "object",
            "properties": {
                "status": {
                    "description": "状态 0:禁用 1:启用",
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                }
            }
        },
        "request.PrinterSyncRequest": {
            "type": "object",
            "required": [
                "cashier_config",
                "connection_type",
                "created_at",
                "id",
                "kitchen_config",
                "name_ug",
                "name_zh",
                "paper_width",
                "print_mode",
                "updated_at"
            ],
            "properties": {
                "buzzer": {
                    "description": "蜂鸣器\tfalse:不提醒 true:提醒",
                    "type": "boolean"
                },
                "cashier_config": {
                    "description": "收银打印机配置",
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.CashierConfig"
                        }
                    ]
                },
                "cloud": {
                    "description": "云打印机号",
                    "type": "string"
                },
                "connection_type": {
                    "description": "连接方式 network/usb/inner(内置打印机)/cloud",
                    "type": "string",
                    "enum": [
                        "network",
                        "usb",
                        "inner",
                        "cloud"
                    ]
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "删除时间",
                    "type": "string"
                },
                "id": {
                    "description": "打印机ID",
                    "type": "string"
                },
                "ip_address": {
                    "description": "IP地址(网络打印机使用)",
                    "type": "string"
                },
                "kitchen_config": {
                    "description": "后厨打印机配置",
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.KitchenConfig"
                        }
                    ]
                },
                "name_ug": {
                    "description": "维语名称",
                    "type": "string"
                },
                "name_zh": {
                    "description": "中文名称",
                    "type": "string"
                },
                "paper_width": {
                    "description": "纸张宽度(58mm或80mm)",
                    "type": "integer",
                    "maximum": 999
                },
                "print_mode": {
                    "description": "打印方式 text/image",
                    "type": "string",
                    "enum": [
                        "text",
                        "image"
                    ]
                },
                "status": {
                    "description": "打印机状态 0:禁用 1:启用 -1:已删除",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "usb_port": {
                    "description": "USB端口号(USB打印机使用)",
                    "type": "string"
                }
            }
        },
        "request.PrinterUpdateRequest": {
            "type": "object",
            "required": [
                "cashier_config",
                "connection_type",
                "id",
                "kitchen_config",
                "name_ug",
                "name_zh",
                "paper_width",
                "print_mode"
            ],
            "properties": {
                "buzzer": {
                    "description": "蜂鸣器",
                    "type": "boolean"
                },
                "cashier_config": {
                    "description": "收银配置",
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.CashierConfig"
                        }
                    ]
                },
                "cloud": {
                    "description": "云打印机ID",
                    "type": "string"
                },
                "connection_type": {
                    "description": "连接方式 network/usb/cloud",
                    "type": "string",
                    "enum": [
                        "network",
                        "usb",
                        "cloud"
                    ]
                },
                "id": {
                    "description": "打印机ID",
                    "type": "string"
                },
                "ip_address": {
                    "description": "IP地址",
                    "type": "string"
                },
                "kitchen_config": {
                    "description": "厨房配置",
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.KitchenConfig"
                        }
                    ]
                },
                "name_ug": {
                    "description": "维语名称",
                    "type": "string"
                },
                "name_zh": {
                    "description": "中文名称",
                    "type": "string"
                },
                "paper_width": {
                    "description": "纸张宽度",
                    "type": "integer",
                    "enum": [
                        58,
                        80
                    ]
                },
                "print_mode": {
                    "description": "打印模式",
                    "type": "string",
                    "enum": [
                        "text",
                        "image"
                    ]
                },
                "usb_port": {
                    "description": "USB端口",
                    "type": "string"
                }
            }
        },
        "request.RechargeMicroPayForm": {
            "type": "object",
            "required": [
                "auth_code",
                "customer_id",
                "recharge_amount"
            ],
            "properties": {
                "auth_code": {
                    "description": "付款码编号",
                    "type": "string"
                },
                "customer_id": {
                    "description": "会员ID (VIP支付时必填)",
                    "type": "integer"
                },
                "present_amount": {
                    "description": "赠送金额，单位分",
                    "type": "integer",
                    "minimum": 0
                },
                "recharge_amount": {
                    "description": "支付金额，单位分",
                    "type": "integer",
                    "minimum": 1
                }
            }
        },
        "request.RechargeOfflineForm": {
            "type": "object",
            "required": [
                "customer_id",
                "payment_type_id",
                "recharge_amount"
            ],
            "properties": {
                "customer_id": {
                    "description": "会员ID (VIP支付时必填)",
                    "type": "integer"
                },
                "payment_type_id": {
                    "description": "付款码编号",
                    "type": "integer"
                },
                "present_amount": {
                    "description": "赠送金额，单位分",
                    "type": "integer",
                    "minimum": 0
                },
                "recharge_amount": {
                    "description": "支付金额，单位分",
                    "type": "integer",
                    "minimum": 1
                }
            }
        },
        "request.RechargeRequest": {
            "type": "object",
            "required": [
                "customer_id",
                "recharge_amount"
            ],
            "properties": {
                "customer_id": {
                    "description": "会员ID (VIP支付时必填)",
                    "type": "integer"
                },
                "present_amount": {
                    "description": "赠送金额，单位分",
                    "type": "integer",
                    "minimum": 0
                },
                "recharge_amount": {
                    "description": "支付金额，单位分",
                    "type": "integer",
                    "minimum": 1
                }
            }
        },
        "request.SendSmsForm": {
            "type": "object",
            "required": [
                "phone"
            ],
            "properties": {
                "phone": {
                    "type": "string"
                }
            }
        },
        "request.VerifySmsForm": {
            "type": "object",
            "required": [
                "batch_id",
                "code",
                "password",
                "phone"
            ],
            "properties": {
                "batch_id": {
                    "type": "string"
                },
                "code": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "maxLength": 32,
                    "minLength": 6
                },
                "phone": {
                    "type": "string"
                }
            }
        },
        "resource.AreaResource": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "Create time",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "Delete time",
                    "type": "string"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "name_ug": {
                    "description": "名称(维语)",
                    "type": "string"
                },
                "name_zh": {
                    "description": "名称(中文)",
                    "type": "string"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "Update time",
                    "type": "string"
                }
            }
        },
        "resource.DebtHolderResource": {
            "type": "object",
            "properties": {
                "balance": {
                    "description": "当前赊账金额",
                    "type": "integer"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "created_by": {
                    "description": "创建人",
                    "type": "integer"
                },
                "credit_limit": {
                    "description": "信用额度(分)",
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "merchant_no": {
                    "description": "商户号",
                    "type": "string"
                },
                "name_ug": {
                    "description": "维语姓名",
                    "type": "string"
                },
                "name_zh": {
                    "description": "中文姓名",
                    "type": "string"
                },
                "phone": {
                    "description": "手机号",
                    "type": "string"
                },
                "status": {
                    "description": "状态：0 禁用 1 启用",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "resource.DebtTransactionResource": {
            "type": "object",
            "properties": {
                "amount": {
                    "description": "金额",
                    "type": "integer"
                },
                "cashier_id": {
                    "description": "创建人",
                    "type": "integer"
                },
                "cashier_name": {
                    "description": "创建人姓名",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "holder_id": {
                    "description": "赊账人ID",
                    "type": "integer"
                },
                "holder_name": {
                    "description": "赊账人姓名",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "order_id": {
                    "description": "订单ID",
                    "type": "integer"
                },
                "order_no": {
                    "description": "订单编号",
                    "type": "string"
                },
                "type": {
                    "description": "交易类型 1 赊账 2 结账",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "resource.FoodCategoryResource": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "Create time",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "Delete time",
                    "type": "string"
                },
                "foods_count": {
                    "description": "菜品数量",
                    "type": "integer"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "name_ug": {
                    "description": "名称(维语)",
                    "type": "string"
                },
                "name_zh": {
                    "description": "名称(中文)",
                    "type": "string"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "Update time",
                    "type": "string"
                }
            }
        },
        "resource.FoodComboResource": {
            "type": "object",
            "properties": {
                "combo_id": {
                    "description": "套餐 ID",
                    "type": "integer"
                },
                "combo·price": {
                    "description": "套餐价",
                    "type": "number"
                },
                "count": {
                    "description": "数量",
                    "type": "integer"
                },
                "created_at": {
                    "description": "Create time",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "Delete time",
                    "type": "string"
                },
                "food_id": {
                    "description": "菜品 ID",
                    "type": "integer"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "name_ug": {
                    "description": "名称(维语)",
                    "type": "string"
                },
                "name_zh": {
                    "description": "名称(中文)",
                    "type": "string"
                },
                "origin_price": {
                    "description": "原价",
                    "type": "number"
                },
                "parent_id": {
                    "description": "父级 ID",
                    "type": "integer"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "type": {
                    "description": "类型",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "Update time",
                    "type": "string"
                }
            }
        },
        "resource.FoodPrinterResource": {
            "type": "object",
            "properties": {
                "food_id": {
                    "type": "integer"
                },
                "printer_id": {
                    "type": "string"
                }
            }
        },
        "resource.FoodResource": {
            "type": "object",
            "properties": {
                "cell_clear_state": {
                    "description": "否设置剩余数量(1表示设置剩余数量、0表示没设置)",
                    "type": "boolean"
                },
                "cost_price": {
                    "description": "定价",
                    "type": "number"
                },
                "created_at": {
                    "description": "Create time",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "Delete time",
                    "type": "string"
                },
                "food_category_id": {
                    "description": "分类ID",
                    "type": "integer"
                },
                "format_id": {
                    "description": "规格ID",
                    "type": "integer"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "image": {
                    "description": "图片",
                    "type": "string"
                },
                "is_combo_food": {
                    "description": "是否套餐菜",
                    "type": "boolean"
                },
                "is_special_food": {
                    "description": "是否特色菜",
                    "type": "boolean"
                },
                "is_sync": {
                    "description": "是否同步",
                    "type": "boolean"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "name_ug": {
                    "description": "名称(维语)",
                    "type": "string"
                },
                "name_zh": {
                    "description": "名称(中文)",
                    "type": "string"
                },
                "price": {
                    "description": "现价",
                    "type": "number"
                },
                "remaining_count": {
                    "description": "美食剩余数量",
                    "type": "number"
                },
                "sell_clear_count": {
                    "description": "美食剩余限量数",
                    "type": "number"
                },
                "shortcut_code": {
                    "description": "快捷码",
                    "type": "string"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "support_scan_order": {
                    "description": "是否支持扫码点单",
                    "type": "boolean"
                },
                "updated_at": {
                    "description": "Update time",
                    "type": "string"
                },
                "vip_price": {
                    "description": "会员价",
                    "type": "number"
                }
            }
        },
        "resource.LocalServerBasicResource": {
            "type": "object",
            "properties": {
                "client_id": {
                    "description": "id",
                    "type": "integer"
                },
                "ipv4": {
                    "description": "ipv4",
                    "type": "string"
                },
                "ipv6": {
                    "description": "ipv6",
                    "type": "string"
                },
                "ws_url": {
                    "description": "websocket uri",
                    "type": "string"
                }
            }
        },
        "resource.LoginResource": {
            "type": "object",
            "properties": {
                "merchant": {
                    "$ref": "#/definitions/resource.MerchantResource"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "server_info": {
                    "$ref": "#/definitions/resource.LocalServerBasicResource"
                },
                "server_token": {
                    "type": "string"
                },
                "token": {
                    "$ref": "#/definitions/schema.LoginToken"
                },
                "user": {
                    "$ref": "#/definitions/resource.LoginUserResource"
                }
            }
        },
        "resource.LoginUserResource": {
            "type": "object",
            "properties": {
                "api_token": {
                    "description": "登录token",
                    "type": "string"
                },
                "avatar": {
                    "description": "头像",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "删除时间",
                    "type": "string"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "is_handover": {
                    "description": "是否转交",
                    "type": "boolean"
                },
                "is_owner": {
                    "description": "是否为店长",
                    "type": "boolean"
                },
                "job_no": {
                    "description": "员工编号",
                    "type": "string"
                },
                "last_login": {
                    "description": "最后登录",
                    "type": "string"
                },
                "leaved_at": {
                    "description": "离职时间",
                    "type": "string"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "name": {
                    "description": "姓名",
                    "type": "string"
                },
                "operation_password": {
                    "description": "操作密码",
                    "type": "string"
                },
                "password": {
                    "description": "密码",
                    "type": "string"
                },
                "permissions": {
                    "description": "权限列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "phone": {
                    "description": "手机号",
                    "type": "string"
                },
                "role_id": {
                    "description": "角色名称",
                    "type": "integer"
                },
                "state": {
                    "description": "状态 - 1 开启 0 关闭",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "resource.MerchantBasicResource": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "自增编号",
                    "type": "integer"
                },
                "logo": {
                    "description": "餐厅Logo",
                    "type": "string"
                },
                "mode": {
                    "description": "商家模式：1 本地模式(默认) 2 线上模式",
                    "type": "integer"
                },
                "name_ug": {
                    "description": "维文名称",
                    "type": "string"
                },
                "name_zh": {
                    "description": "中文名称",
                    "type": "string"
                },
                "no": {
                    "description": "商家唯一编号",
                    "type": "string"
                },
                "server_info": {
                    "description": "本地服务器信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/resource.LocalServerBasicResource"
                        }
                    ]
                },
                "state": {
                    "description": "商家状态",
                    "type": "integer"
                }
            }
        },
        "resource.MerchantConfigResource": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "Create time",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "Delete time",
                    "type": "string"
                },
                "id": {
                    "description": "主键，自增",
                    "type": "integer"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "type": {
                    "description": "设置配置类型（ignore price）",
                    "type": "string"
                },
                "updated_at": {
                    "description": "Update time",
                    "type": "string"
                },
                "value": {
                    "description": "值",
                    "allOf": [
                        {
                            "$ref": "#/definitions/util.JSON"
                        }
                    ]
                }
            }
        },
        "resource.MerchantInfoUpdateResource": {
            "type": "object",
            "properties": {
                "area_updated_at": {
                    "description": "餐厅区域更新时间",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "food_printer_updated_at": {
                    "description": "菜品打印机信息更新时间",
                    "type": "string"
                },
                "food_updated_at": {
                    "description": "菜品信息更新时间",
                    "type": "string"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "payment_type_updated_at": {
                    "description": "支付方式更新时间",
                    "type": "string"
                },
                "permission_updated_at": {
                    "description": "权限信息更新时间",
                    "type": "string"
                },
                "printer_updated_at": {
                    "description": "打印机信息更新时间",
                    "type": "string"
                },
                "remark_updated_at": {
                    "description": "备注信息更新时间",
                    "type": "string"
                },
                "table_updated_at": {
                    "description": "餐桌信息更新时间",
                    "type": "string"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "user_updated_at": {
                    "description": "用户信息更新时间",
                    "type": "string"
                }
            }
        },
        "resource.MerchantResource": {
            "type": "object",
            "properties": {
                "address_ug": {
                    "description": "维文地址",
                    "type": "string"
                },
                "address_zh": {
                    "description": "中文地址",
                    "type": "string"
                },
                "business_license": {
                    "description": "营业执照",
                    "type": "string"
                },
                "cancel_password": {
                    "description": "取消美食密码",
                    "type": "string"
                },
                "created_at": {
                    "description": "Create time",
                    "type": "string"
                },
                "diancai_pay": {
                    "description": "点菜端是否可以现金结账，是否支持收银端收现金",
                    "type": "integer"
                },
                "expired_at": {
                    "description": "过期时间，时间戳",
                    "type": "string"
                },
                "id": {
                    "description": "自增编号",
                    "type": "integer"
                },
                "lat": {
                    "description": "纬度",
                    "type": "number"
                },
                "lng": {
                    "description": "经度",
                    "type": "number"
                },
                "logo": {
                    "description": "餐厅Logo",
                    "type": "string"
                },
                "mode": {
                    "description": "商家模式：1 本地模式(默认) 2 线上模式",
                    "type": "integer"
                },
                "name_ug": {
                    "description": "维文名称",
                    "type": "string"
                },
                "name_zh": {
                    "description": "中文名称",
                    "type": "string"
                },
                "no": {
                    "description": "商家唯一编号",
                    "type": "string"
                },
                "phone": {
                    "description": "电话号码",
                    "type": "string"
                },
                "refund_password": {
                    "description": "退单密码",
                    "type": "string"
                },
                "sms_count": {
                    "description": "剩余的短信数",
                    "type": "integer"
                },
                "sub_merchant_no": {
                    "description": "微信特约商户号",
                    "type": "string"
                },
                "updated_at": {
                    "description": "Update time",
                    "type": "string"
                },
                "wechat_payment_type": {
                    "description": "是否特约商户, 0 普通转账模式、 1 特约商户模式",
                    "type": "integer"
                }
            }
        },
        "resource.MerchantRoleItemResource": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "Create time",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "Delete time",
                    "type": "string"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "name_ug": {
                    "description": "名称(维语)",
                    "type": "string"
                },
                "name_zh": {
                    "description": "名称(中文)",
                    "type": "string"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "Update time",
                    "type": "string"
                }
            }
        },
        "resource.MerchantRoleResource": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "Create time",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "Delete time",
                    "type": "string"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "name_ug": {
                    "description": "名称(维语)",
                    "type": "string"
                },
                "name_zh": {
                    "description": "名称(中文)",
                    "type": "string"
                },
                "permissions": {
                    "description": "权限",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "Update time",
                    "type": "string"
                }
            }
        },
        "resource.OrderInfo": {
            "type": "object",
            "properties": {
                "order_amount": {
                    "description": "订单价格",
                    "type": "number"
                },
                "order_count": {
                    "description": "订单数量",
                    "type": "integer"
                }
            }
        },
        "resource.PaymentTypeResource": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "Create time",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "Delete time",
                    "type": "string"
                },
                "icon": {
                    "description": "图标",
                    "type": "string"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "name_ug": {
                    "description": "名称(维语)",
                    "type": "string"
                },
                "name_zh": {
                    "description": "名称(中文)",
                    "type": "string"
                },
                "online": {
                    "description": "在线支付",
                    "type": "integer"
                },
                "pay_method": {
                    "description": "支付方式",
                    "type": "integer"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "tag": {
                    "description": "标签",
                    "type": "string"
                },
                "type": {
                    "description": "类型",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "Update time",
                    "type": "string"
                }
            }
        },
        "resource.PermissionResource": {
            "type": "object",
            "properties": {
                "v0": {
                    "type": "string"
                },
                "v1": {
                    "type": "string"
                },
                "v2": {
                    "type": "string"
                }
            }
        },
        "resource.PrinterResource": {
            "type": "object",
            "properties": {
                "buzzer": {
                    "description": "蜂鸣器\tfalse:不提醒 true:提醒",
                    "type": "boolean"
                },
                "cashier_config": {
                    "description": "收银打印机配置",
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.CashierConfig"
                        }
                    ]
                },
                "cloud": {
                    "description": "云打印机号",
                    "type": "string"
                },
                "connection_type": {
                    "description": "连接方式 network/usb/inner(内置打印机)/cloud",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "删除时间",
                    "type": "string"
                },
                "id": {
                    "description": "打印机ID",
                    "type": "string"
                },
                "ip_address": {
                    "description": "IP地址(网络打印机使用)",
                    "type": "string"
                },
                "kitchen_config": {
                    "description": "后厨打印机配置",
                    "allOf": [
                        {
                            "$ref": "#/definitions/model.KitchenConfig"
                        }
                    ]
                },
                "name_ug": {
                    "description": "维语名称",
                    "type": "string"
                },
                "name_zh": {
                    "description": "中文名称",
                    "type": "string"
                },
                "paper_width": {
                    "description": "纸张宽度(58mm或80mm)",
                    "type": "integer"
                },
                "print_mode": {
                    "description": "打印方式 text/image",
                    "type": "string"
                },
                "status": {
                    "description": "打印机状态 0:禁用 1:启用 -1:删除",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "usb_port": {
                    "description": "USB端口号(USB打印机使用)",
                    "type": "string"
                }
            }
        },
        "resource.RemarkCategoryResource": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "Create time",
                    "type": "string"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "name_ug": {
                    "description": "名称(维语)",
                    "type": "string"
                },
                "name_zh": {
                    "description": "名称(中文)",
                    "type": "string"
                },
                "tag": {
                    "description": "标签",
                    "type": "string"
                },
                "updated_at": {
                    "description": "Update time",
                    "type": "string"
                }
            }
        },
        "resource.RemarkResource": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "Create time",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "Delete time",
                    "type": "string"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "name_ug": {
                    "description": "名称(维语)",
                    "type": "string"
                },
                "name_zh": {
                    "description": "名称(中文)",
                    "type": "string"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "tag": {
                    "description": "标签",
                    "type": "string"
                },
                "updated_at": {
                    "description": "Update time",
                    "type": "string"
                }
            }
        },
        "resource.TableDataResource": {
            "type": "object",
            "properties": {
                "data": {
                    "description": "餐桌",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.TableDataModel"
                    }
                },
                "order_info": {
                    "description": "订单信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/resource.OrderInfo"
                        }
                    ]
                },
                "table_info": {
                    "description": "餐桌信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/resource.TableInfo"
                        }
                    ]
                }
            }
        },
        "resource.TableInfo": {
            "type": "object",
            "properties": {
                "emptyTableCount": {
                    "description": "未开台餐桌数量",
                    "type": "integer"
                },
                "hasCustomersTableCount": {
                    "description": "开台餐桌数量",
                    "type": "integer"
                },
                "hasOrderTableCount": {
                    "description": "已下订单餐桌数量",
                    "type": "integer"
                }
            }
        },
        "resource.TableResource": {
            "type": "object",
            "properties": {
                "area_id": {
                    "description": "区域ID",
                    "type": "integer"
                },
                "created_at": {
                    "description": "Create time",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "Delete time",
                    "type": "string"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "name_ug": {
                    "description": "名称(维语)",
                    "type": "string"
                },
                "name_zh": {
                    "description": "名称(中文)",
                    "type": "string"
                },
                "no": {
                    "description": "编号",
                    "type": "string"
                },
                "seating_capacity": {
                    "description": "座位数",
                    "type": "integer"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "Update time",
                    "type": "string"
                }
            }
        },
        "resource.TerminalResource": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "description": "终端说明",
                    "type": "string"
                },
                "html_url": {
                    "description": "终端下载地址",
                    "type": "string"
                },
                "icon": {
                    "description": "终端图标",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "min_supported_version": {
                    "description": "最低支持版本号",
                    "type": "integer"
                },
                "name": {
                    "description": "终端名称",
                    "type": "string"
                },
                "name_ug": {
                    "description": "终端名称",
                    "type": "string"
                },
                "name_zh": {
                    "description": "终端名称",
                    "type": "string"
                },
                "os_type": {
                    "description": "操作系统类型(1表示Android、2表示iOS、3表示windows)",
                    "type": "integer"
                },
                "sha512": {
                    "description": "客户端sha512值",
                    "type": "string"
                },
                "size": {
                    "description": "大小",
                    "type": "integer"
                },
                "state": {
                    "description": "状态（0关闭，1开通）",
                    "type": "integer"
                },
                "terminal_type": {
                    "description": "终端类型（1 收银端、 2 点菜端）",
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                },
                "url": {
                    "description": "终端下载地址",
                    "type": "string"
                },
                "version": {
                    "description": "终端版本",
                    "type": "string"
                },
                "version_code": {
                    "description": "终端版本号",
                    "type": "integer"
                }
            }
        },
        "resource.UserResource": {
            "type": "object",
            "properties": {
                "api_token": {
                    "description": "登录token",
                    "type": "string"
                },
                "avatar": {
                    "description": "头像",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "deleted_at": {
                    "description": "删除时间",
                    "type": "string"
                },
                "id": {
                    "description": "Unique ID",
                    "type": "integer"
                },
                "job_no": {
                    "description": "员工编号",
                    "type": "string"
                },
                "last_login": {
                    "description": "最后登录",
                    "type": "string"
                },
                "leaved_at": {
                    "description": "离职时间",
                    "type": "string"
                },
                "merchant_no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "name": {
                    "description": "姓名",
                    "type": "string"
                },
                "password": {
                    "description": "密码",
                    "type": "string"
                },
                "phone": {
                    "description": "手机号",
                    "type": "string"
                },
                "role_id": {
                    "description": "角色名称",
                    "type": "integer"
                },
                "state": {
                    "description": "状态 - 1 开启 0 关闭",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "resource.WechatUserBaseResource": {
            "type": "object",
            "properties": {
                "api_token": {
                    "type": "string"
                },
                "area_id": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "last_login": {
                    "type": "string"
                },
                "mobile": {
                    "type": "string"
                },
                "open_id": {
                    "type": "string"
                },
                "points": {
                    "type": "integer"
                }
            }
        },
        "scan_resource.OpenIDResource": {
            "type": "object",
            "properties": {
                "login_state": {
                    "type": "integer"
                },
                "openid": {
                    "type": "string"
                },
                "session_key": {
                    "type": "string"
                },
                "unionid": {
                    "type": "string"
                },
                "wechat_user": {
                    "$ref": "#/definitions/resource.WechatUserBaseResource"
                }
            }
        },
        "scan_resource.ScanOrderResource": {
            "type": "object",
            "properties": {
                "address_ug": {
                    "description": "地址维文",
                    "type": "string"
                },
                "address_zh": {
                    "description": "地址中文",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "id": {
                    "description": "订单ID",
                    "type": "integer"
                },
                "logo": {
                    "description": "商家Logo",
                    "type": "string"
                },
                "name_ug": {
                    "description": "商家名称维文",
                    "type": "string"
                },
                "name_zh": {
                    "description": "商家名称中文",
                    "type": "string"
                },
                "no": {
                    "description": "商家编号",
                    "type": "string"
                },
                "open_id": {
                    "description": "用户OpenID",
                    "type": "string"
                },
                "order_no": {
                    "description": "订单号",
                    "type": "string"
                },
                "paid_at": {
                    "description": "支付时间",
                    "type": "string"
                },
                "price": {
                    "description": "价格",
                    "type": "string"
                },
                "state": {
                    "description": "订单状态",
                    "type": "integer"
                },
                "table_name_ug": {
                    "description": "餐桌名称维文",
                    "type": "string"
                },
                "table_name_zh": {
                    "description": "餐桌名称中文",
                    "type": "string"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "schema.HandoverOverview": {
            "type": "object",
            "properties": {
                "alternate_amount": {
                    "type": "number"
                },
                "customer_count": {
                    "type": "integer"
                },
                "leave_at": {
                    "type": "string"
                },
                "order_count": {
                    "type": "integer"
                },
                "paid_amount": {
                    "type": "number"
                },
                "receivable_amount": {
                    "type": "number"
                },
                "refund_amount": {
                    "type": "number"
                },
                "start_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "integer"
                },
                "user_name": {
                    "type": "string"
                },
                "working_balance": {
                    "type": "number"
                }
            }
        },
        "schema.LoginToken": {
            "type": "object",
            "properties": {
                "access_token": {
                    "description": "Access token (JWT)",
                    "type": "string"
                },
                "expires_at": {
                    "description": "Expired time (Unit: second)",
                    "type": "integer"
                },
                "refresh_expires_at": {
                    "description": "Refresh expired time (Unit: second)",
                    "type": "integer"
                },
                "refresh_token": {
                    "description": "Refresh token (JWT)",
                    "type": "string"
                },
                "token_type": {
                    "description": "Token type (Usage: Authorization=${token_type} ${access_token})",
                    "type": "string"
                }
            }
        },
        "schema.PaymentResult": {
            "type": "object",
            "properties": {
                "merchant_no": {
                    "description": "商户号",
                    "type": "string"
                },
                "order_no": {
                    "description": "订单号",
                    "type": "string"
                },
                "paid_at": {
                    "description": "支付时间",
                    "type": "string"
                },
                "payment_no": {
                    "description": "支付流水号",
                    "type": "string"
                },
                "payment_type_id": {
                    "description": "支付方式ID",
                    "type": "integer"
                },
                "status": {
                    "description": "0:支付失败 1:支付成功",
                    "type": "integer"
                }
            }
        },
        "schema.RefundResult": {
            "type": "object",
            "properties": {
                "merchant_no": {
                    "description": "商户号",
                    "type": "string"
                },
                "payment_no": {
                    "description": "支付流水号",
                    "type": "string"
                },
                "payment_type_id": {
                    "description": "支付方式ID",
                    "type": "integer"
                },
                "refund_at": {
                    "description": "支付时间",
                    "type": "string"
                },
                "status": {
                    "description": "0:退款失败 1:退款成功",
                    "type": "integer"
                }
            }
        },
        "service.BillTotals": {
            "type": "object",
            "properties": {
                "real_amount": {
                    "type": "number"
                },
                "total_amount": {
                    "type": "number"
                }
            }
        },
        "util.JSON": {
            "type": "object",
            "additionalProperties": true
        },
        "util.PaginationResult": {
            "type": "object",
            "properties": {
                "current_page": {
                    "type": "integer"
                },
                "per_page": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "util.ResponseResult": {
            "type": "object",
            "properties": {
                "data": {},
                "errors": {},
                "message": {
                    "type": "string"
                },
                "meta": {
                    "$ref": "#/definitions/util.PaginationResult"
                }
            }
        },
        "wechat.JSAPIPayParams": {
            "type": "object",
            "properties": {
                "appId": {
                    "type": "string"
                },
                "nonceStr": {
                    "type": "string"
                },
                "package": {
                    "type": "string"
                },
                "paySign": {
                    "type": "string"
                },
                "signType": {
                    "type": "string"
                },
                "timeStamp": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiTokenAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        },
        "ServerTokenAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        },
        "WechatTokenAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "v1.0.0",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "智慧餐厅云端接口",
	Description:      "智慧餐厅GO接口",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}

package order_broadcast

import (
	"ros-api-go/pkg/util"
	"ros-api-go/pkg/websocket"
	"time"
)

const (
	ActionCallWaiter   = "callWaiter"   // 呼叫服务员
	ActionAddOrder     = "addOrder"     // 开台
	ActionAddFood      = "addFood"      // 加菜 （多个菜品一并提交）
	ActionCheckout     = "checkout"     // 已结账
	ActionOrderRefund  = "orderRefund"  // 已结账订单退菜 (线上退菜，无法发送)
	ActionCancelAll    = "cancelAll"    // 全单退菜
	ActionPayment      = "payment"      // 订单支付
	ActionCancelFood   = "cancelFood"   // 退菜
	ActionUrgeOrder    = "urgeOrder"    // 催单
	ActionChangeTable  = "changeTable"  // 订单换台
	ActionCollageTable = "collageTable" // 合并桌子(拼单)
	ActionAddPrint     = "addPrint"     // 合并桌子(拼单)
)

type BroadcastData struct {
	ID        string      `json:"id"`        // 唯一ID
	Action    string      `json:"action"`    // 操作类型
	TableID   int64       `json:"table_id"`  // 餐桌ID
	OrderID   int64       `json:"order_id"`  // 订单ID
	Data      interface{} `json:"data"`      // 其他数据
	Timestamp int64       `json:"timestamp"` // 时间戳
}

// SendOrderBroadcast 发送订单广播
func SendOrderBroadcast(action string, merchantNo string, tableId, orderId int64, data interface{}) {
	broadcast := &BroadcastData{
		ID:        util.NewXID(),
		Action:    action,
		TableID:   tableId,
		OrderID:   orderId,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
	websocket.Broadcast(merchantNo, broadcast)
}

package internal

import (
	"context"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"ros-api-go/internal/http"
	"ros-api-go/internal/http/middleware"
	"ros-api-go/internal/route"
)

const (
	apiPrefix   = "/api/"
	cloudPrefix = "/cloud/api/"
	adminPrefix = "/admin/"
)

func RouterPrefixes() []string {
	return []string{
		apiPrefix,
		adminPrefix,
	}
}

type App struct {
	Routers            *Routers
	Casbinx            *http.Casbinx
	AuthProvider       *middleware.AuthProvider
	ServerAuthProvider *middleware.ServerAuthProvider
	WechatUserProvider *middleware.WechatUserProvider
	CasbinProvider     *middleware.CasbinProvider
}

// Init 初始化
// 一些初始化的操作在这里进行
func (app *App) Init(ctx context.Context, db *gorm.DB) error {
	if err := app.Casbinx.LoadSysPermissions(ctx); err != nil {
		return err
	}
	app.AuthProvider.RegisterMiddleware(ctx, db)
	app.ServerAuthProvider.RegisterMiddleware(ctx, db)
	app.WechatUserProvider.RegisterMiddleware(ctx, db)
	app.CasbinProvider.RegisterMiddleware(ctx, db)
	return nil
}

func (app *App) Release(ctx context.Context) error {
	if err := app.Casbinx.Release(ctx); err != nil {
		return err
	}
	return nil
}

// Routers 路由注册
type Routers struct {
	ApiRouterGroup   *route.ApiRouterGroup
	OrderRouteGroup  *route.CloudRouteGroup
	AdminRouterGroup *route.AdminRouterGroup
}

func (h *Routers) RegisterRouters(ctx context.Context, e *gin.Engine) error {
	h.ApiRouterGroup.RegisterRouters(ctx, e.Group(apiPrefix))
	h.OrderRouteGroup.RegisterRouters(ctx, e.Group(cloudPrefix))
	h.AdminRouterGroup.RegisterRouters(ctx, e.Group(adminPrefix))
	return nil
}

package schema

// CashierPaymentStatistics 收银员支付统计
type CashierPaymentStatistics struct {
	PaymentTypeID int64 `json:"payment_type_id"` // 支付方式ID
	OrderAmount   int64 `json:"order_amount"`    // 支付金额
	OrderCount    int   `json:"order_count"`     // 订单数量
	RefundCount   int   `json:"refund_count"`    // 退款数量
	RefundAmount  int64 `json:"refund_amount"`   // 退款金额
}

// CashierRechargeStatistics 收银员充值统计
type CashierRechargeStatistics struct {
	CashierID      int64 `json:"cashier_id"`      // 收银员ID
	PaymentTypeID  int64 `json:"payment_type_id"` // 支付方式ID
	Count          int   `json:"count"`           // 充值数量
	RechargeAmount int64 `json:"recharge_amount"` // 充值金额
	PresentAmount  int64 `json:"present_amount"`  // 赠送金额
}

type CashierDebtStatistics struct {
	CashierID     int64 `json:"cashier_id"`      // 收银员ID
	PaymentTypeID int64 `json:"payment_type_id"` // 支付方式ID
	Count         int   `json:"count"`           // 还款数量
	Amount        int64 `json:"amount"`          // 还款金额
}

type HandoverOverviewFromOrder struct {
	UserID           int64   `json:"user_id"`
	UserName         string  `json:"user_name"`
	StartAt          *string `json:"start_at"`
	LeaveAt          *string `json:"leave_at"`
	ReceivableAmount float64 `json:"receivable_amount"`
	PaidAmount       float64 `json:"paid_amount"`
	RefundAmount     float64 `json:"refund_amount"`
	OrderCount       int     `json:"order_count"`
	CustomerCount    int     `json:"customer_count"`
}

type HandoverOverview struct {
	HandoverOverviewFromOrder
	WorkingBalance  float64 `json:"working_balance"`
	AlternateAmount float64 `json:"alternate_amount"`
}

type PaymentSummary struct {
	PaidMoney     float64 `gorm:"column:paid_money"`
	Name          string  `gorm:"column:name"`
	PaymentTypeID int     `gorm:"column:payment_type_id"`
	OrderCount    int     `gorm:"column:order_count"`
}

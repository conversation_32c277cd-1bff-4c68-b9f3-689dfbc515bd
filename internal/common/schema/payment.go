package schema

import (
	"time"
)

// PaymentResult 支付结果
type PaymentResult struct {
	OpenID        string `json:"-"`               // 用户标识
	Status        int64  `json:"status"`          // 0:支付失败 1:支付成功
	MerchantNo    string `json:"merchant_no"`     // 商户号
	OrderNo       string `json:"order_no"`        // 订单号
	PaymentNo     string `json:"payment_no"`      // 支付流水号
	PaymentTypeID int64  `json:"payment_type_id"` // 支付方式ID
	NameUg        string `json:"name_ug"`         // 支付方式名称
	NameZh        string `json:"name_zh"`         // 支付方式名称

	OutTradeNo    string     `json:"-"`       // 商户订单号
	TradeType     string     `json:"-"`       // 交易类型
	TradeState    string     `json:"-"`       // 交易状态
	TradeDesc     string     `json:"-"`       // 交易描述
	TransactionID string     `json:"-"`       // 微信支付订单号
	PaidAt        *time.Time `json:"paid_at"` // 支付时间
}

// RefundResult 退款结果
type RefundResult struct {
	Status        int64      `json:"status"`          // 0:退款失败 1:退款成功
	MerchantNo    string     `json:"merchant_no"`     // 商户号
	PaymentNo     string     `json:"payment_no"`      // 支付流水号
	PaymentTypeID int64      `json:"payment_type_id"` // 支付方式ID
	OutTradeNo    string     `json:"-"`               // 商户订单号
	OutRefundNo   string     `json:"-"`               // 商户退款单号
	TradeType     string     `json:"-"`               // 交易类型
	TradeState    string     `json:"-"`               // 交易状态
	TradeDesc     string     `json:"-"`               // 交易描述
	RefundID      string     `json:"-"`               // 微信支付订单号
	RefundAt      *time.Time `json:"refund_at"`       // 支付时间
}

type RechargeResult struct {
	Status         int64   `json:"status"`          // 0:充值失败 1:充值成功
	Mobile         string  `json:"mobile"`          // 充值手机号
	Name           string  `json:"name"`            // 充值姓名
	PaymentType    string  `json:"payment_type"`    // 支付方式名称
	RechargeAmount float64 `json:"recharge_amount"` // 充值金额
	PresentAmount  float64 `json:"present_amount"`  // 赠送金额
	Balance        float64 `json:"balance"`         // 充值后余额
}

type DebtRepaymentResult struct {
	Status      int64   `json:"status"`       // 0:还款失败 1:还款成功
	Mobile      string  `json:"mobile"`       // 还款手机号
	Name        string  `json:"name"`         // 还款姓名
	PaymentType string  `json:"payment_type"` // 支付方式名称
	Amount      float64 `json:"amount"`       // 还款金额
	Balance     float64 `json:"balance"`      // 还款后赊账余额
}

type DebtTransactionType struct {
	ID   any    `json:"id"`
	Name string `json:"name"`
}

package schema

// LoginToken jwt token 结构体
type LoginToken struct {
	AccessToken      string `json:"access_token"`       // Access token (JWT)
	TokenType        string `json:"token_type"`         // Token type (Usage: Authorization=${token_type} ${access_token})
	ExpiresAt        int64  `json:"expires_at"`         // Expired time (Unit: second)
	RefreshToken     string `json:"refresh_token"`      // Refresh token (JWT)
	RefreshExpiresAt int64  `json:"refresh_expires_at"` // Refresh expired time (Unit: second)
}

package schema

type GraphCustomerStatistics struct {
	DayCustomersCount int    `json:"day_customers_count"`
	DayOrderCount     int    `json:"day_order_count"`
	DayPaidAt         string `json:"day_paid_at"`
}

type GraphBusinessStatistics struct {
	Day        string  `json:"day"`
	RealAmount float64 `json:"real_amount"`
}

type GraphPayTypeStatistics struct {
	DailyPrice    float64 `json:"daily_price"`
	Day           string  `json:"day"`
	Name          string  `json:"name"`
	PaymentTypeID int64   `json:"payment_type_id"`
}

type HandoverSmallAggregate struct {
	Name         string  `json:"name"`
	ShiftCount   int     `json:"shift_count"`
	TotalAmount  float64 `json:"total_amount"`
	WorkingHours int64   `json:"working_hours"`
}

type HandoverPaymentProportion struct {
	ID                int64   `json:"id"`
	MerchantNo        string  `json:"merchant_no"`
	UserID            int64   `json:"user_id"`
	HandoverLogID     int64   `json:"handover_log_id"`
	PaymentTypeID     int64   `json:"payment_type_id"`
	PaymentType       string  `json:"payment_type"`
	OrderCount        int     `json:"order_count"`
	OrderAmount       float64 `json:"order_amount"`
	RefundAmount      float64 `json:"refund_amount"`
	VipCount          int     `json:"vip_count"`
	VipRechargeAmount float64 `json:"vip_recharge_amount"`
	Total             float64 `json:"total"`
}

type BusinessStatisticsData struct {
	BeginDate           string                         `json:"begin_date"`
	EndDate             string                         `json:"end_date"`
	HeadCashInfo        []*BusinessHeadCashInfoItem    `json:"headCashInfo"`
	OrderStatisticsInfo *BusinessOrderStatisticsInfo   `json:"orderStatisticsInfo"`
	OrderProportion     []*BusinessOrderProportionItem `json:"order_proportion"`
	Vip                 *BusinessVipStatistics         `json:"vip"`
	Debt                *BusinessDebtStatistics        `json:"debt"`
}

type BusinessHeadCashInfoItem struct {
	Name  string  `json:"name"`
	Value float64 `json:"value"`
}

type BusinessOrderStatisticsInfo struct {
	IgnorePrice    float64 `json:"ignore_price"`    // 忽略优惠金额
	RefundPrice    float64 `json:"refund_price"`    // 退款金额
	OrderCount     int     `json:"order_count"`     // 订单数量
	CustomersCount int     `json:"customers_count"` // 客户数量
	OrderAvg       float64 `json:"order_avg"`       // 订单均价
	CustomersAvg   float64 `json:"customers_avg"`   // 客户均价
	GiveChange     float64 `json:"give_change"`     // 找零金额
}

type BusinessHeadCashInfoData struct {
	TotalAmount float64 `json:"total_amount"` // 总金额（原价计算）
	RealAmount  float64 `json:"real_amount"`  // 付款金额
	TotalCost   float64 `json:"total_cost"`   // 总成本
}

type BusinessOrderProportionItem struct {
	Amount        float64 `json:"amount"`          // 实收金额
	RealReceived  float64 `json:"real_received"`   // 实收金额
	RefundAmount  float64 `json:"refund_amount"`   // 退款金额
	OrderCount    int     `json:"order_count"`     // 订单数量
	PaymentTypeID int     `json:"payment_type_id"` // 支付方式ID
	PayTypeName   string  `json:"pay_type_name"`   // 支付方式名称
	NameZh        string  `json:"name_zh"`         // 中文名称
	NameUg        string  `json:"name_ug"`         // 维语名称
}
type BusinessVipRechargeAmount struct {
	Amount        float64 `json:"amount"`
	PresentAmount float64 `json:"present_amount"`
	PaymentTypeID int64   `json:"payment_type_id"`
	PayTypeName   string  `json:"pay_type_name"`
	NameZh        string  `json:"-"`
	NameUg        string  `json:"-"`
}

type BusinessVipStatistics struct {
	VipCount        int64                        `json:"vip_count"`
	VipRecharge     float64                      `json:"vip_recharge_amount"`
	VipPresent      float64                      `json:"vip_recharge_present_amount"`
	VipTotalBalance float64                      `json:"vip_total_balance"`
	VipProportion   []*BusinessVipRechargeAmount `json:"vip_proportion"`
}

type MasterHeadCashInfoItem struct {
	Name           string      `json:"name"`
	Value          interface{} `json:"value"`
	YesterdayName  string      `json:"yesterday_name"`
	Yesterday      interface{} `json:"yesterday"`
	YesterdayRatio float64     `json:"yesterday_ratio"`
	LastWeekName   string      `json:"last_week_name"`
	LastWeek       interface{} `json:"last_week"`
	LastWeekRatio  float64     `json:"last_week_ratio"`
	IsPrice        bool        `json:"is_price"`
}

type MasterDetailInfoItem struct {
	Name    string  `json:"name"`
	Value   float64 `json:"value"`
	IsPrice bool    `json:"is_price"`
}

type MasterHomeStatisticsData struct {
	HeadCashInfo []*MasterHeadCashInfoItem `json:"head_info"`
	DetailInfo   []*MasterDetailInfoItem   `json:"detail_info"`
}

type BusinessDebtRepaymentAmount struct {
	Amount        float64 `json:"amount"`
	PaymentTypeID int64   `json:"payment_type_id"`
	PayTypeName   string  `json:"pay_type_name"`
	NameZh        string  `json:"-"`
	NameUg        string  `json:"-"`
}

type BusinessDebtStatistics struct {
	HolderCount     int64                          `json:"holder_count"`
	RepaymentAmount float64                        `json:"debt_repayment_amount"`
	DebtProportion  []*BusinessDebtRepaymentAmount `json:"debt_proportion"`
}

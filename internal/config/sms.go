package config

type SMS struct {
	DefaultProvider SmsProviderType `default:"aliyun"`
	Providers       struct {
		Aliyun  SmsProviderConfig
		ZhuTong SmsProviderConfig
	}
}

type SmsProviderType string

// SmsProviderConfig 短信服务商配置
type SmsProviderConfig struct {
	AccessKeyID     string
	AccessKeySecret string
	SignName        string
	EndPoint        string
	ExtraConfig     map[string]interface{}
	Templates       SmsTemplates
}

type SmsTemplates struct {
	ForgotPasswordSMS    string
	UserPasswordSMS      string
	OperationPasswordSMS string
	ResetPassword        string
	CustomerRechargeSMS  string
	CustomerPaySMS       string
}

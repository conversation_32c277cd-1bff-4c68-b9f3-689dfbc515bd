package config

import (
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/logging"
	"time"
)

type Config struct {
	Logger        logging.LoggerConfig
	General       General
	Storage       Storage
	Middleware    Middleware
	Util          Util
	Dictionary    Dictionary
	PaymentConfig PaymentConfig
	Alipay        Alipay
	WechatPayment WechatPayment
	WechatMini    WechatMini
	MQTT          MQTT
	SMS           SMS
}

type General struct {
	AppName            string `default:"ros-api-go"`
	Version            string `default:"v1.0.0"`
	SiteUrl            string `default:"http://localhost:8040"`
	WebsocketRoute     string `default:"/v2/ws"`
	Debug              bool
	PprofAddr          string
	DisableSwagger     bool
	DisablePrintConfig bool
	WorkDir            string // From command arguments
	MenuFile           string // From request.Menus (JSON/YAML)
	DenyDeleteMenu     bool
	HTTP               struct {
		Addr            string `default:":8040"`
		ShutdownTimeout int    `default:"10"` // seconds
		ReadTimeout     int    `default:"60"` // seconds
		WriteTimeout    int    `default:"60"` // seconds
		IdleTimeout     int    `default:"10"` // seconds
		CertFile        string
		KeyFile         string
		TrustedProxies  []string `default:"[\"*\"]"` // trusted proxy IPs
	}
}

type Storage struct {
	Cache struct {
		Type      string `default:"memory"` // memory/badger/redis
		Delimiter string `default:":"`      // delimiter for key
		Memory    struct {
			CleanupInterval int `default:"60"` // seconds
		}
		Badger struct {
			Path string `default:"data/cache"`
		}
		Redis struct {
			Addr     string
			Username string
			Password string
			DB       int
		}
	}
	DB struct {
		Debug        bool
		Type         string `default:"sqlite3"`          // sqlite3/mysql/postgres
		DSN          string `default:"data/rosapigo.db"` // database source name
		MaxLifetime  int    `default:"86400"`            // seconds
		MaxIdleTime  int    `default:"3600"`             // seconds
		MaxOpenConns int    `default:"100"`              // connections
		MaxIdleConns int    `default:"50"`               // connections
		TablePrefix  string `default:""`
		AutoMigrate  bool   `default:"false"`
		PrepareStmt  bool
		Resolver     []struct {
			DBType   string   // sqlite3/mysql/postgres
			Sources  []string // DSN
			Replicas []string // DSN
			Tables   []string
		}
	}
	Redis struct {
		Addr     string
		Username string
		Password string
		DB       int
	}
	OSS struct {
		AccessKey string `default:"accesskey"`
		SecretKey string `default:"secretkey"`
		Endpoint  string `default:"https://ros-files.mulazim.com"`
		Bucket    string `default:"ros-files"`
		IsCNAME   bool   `default:"true"`
	}
}

type Util struct {
	Captcha struct {
		Length    int    `default:"4"`
		Width     int    `default:"400"`
		Height    int    `default:"160"`
		CacheType string `default:"memory"` // memory/redis
		Redis     struct {
			Addr      string
			Username  string
			Password  string
			DB        int
			KeyPrefix string `default:"captcha:"`
		}
	}
	Prometheus struct {
		Enable         bool
		Port           int    `default:"9100"`
		BasicUsername  string `default:"admin"`
		BasicPassword  string `default:"admin"`
		LogApis        []string
		LogMethods     []string
		DefaultCollect bool
	}
}

type Dictionary struct {
	UserCacheExp int `default:"4"` // hours
}

type MQTT struct {
	Host                          string
	LocalHost                     string
	ClientID                      string
	Username                      string
	Password                      string
	KeepAlive                     uint16
	CleanStartOnInitialConnection bool
	JwtKey                        string
	JwtExp                        int
	RequestTimeOut                time.Duration
}

func (c *Config) IsDebug() bool {
	return c.General.Debug
}

func (c *Config) String() string {
	b, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		panic("Failed to marshal config: " + err.Error())
	}
	return string(b)
}

func (c *Config) PreLoad() {
	if addr := c.Storage.Cache.Redis.Addr; addr != "" {
		username := c.Storage.Cache.Redis.Username
		password := c.Storage.Cache.Redis.Password
		if c.Util.Captcha.CacheType == "redis" &&
			c.Util.Captcha.Redis.Addr == "" {
			c.Util.Captcha.Redis.Addr = addr
			c.Util.Captcha.Redis.Username = username
			c.Util.Captcha.Redis.Password = password
		}
		if c.Middleware.RateLimiter.Store.Type == "redis" &&
			c.Middleware.RateLimiter.Store.Redis.Addr == "" {
			c.Middleware.RateLimiter.Store.Redis.Addr = addr
			c.Middleware.RateLimiter.Store.Redis.Username = username
			c.Middleware.RateLimiter.Store.Redis.Password = password
		}
		if c.Middleware.Auth.Store.Type == "redis" &&
			c.Middleware.Auth.Store.Redis.Addr == "" {
			c.Middleware.Auth.Store.Redis.Addr = addr
			c.Middleware.Auth.Store.Redis.Username = username
			c.Middleware.Auth.Store.Redis.Password = password
		}
	}
}

func (c *Config) Print() {
	if c.General.DisablePrintConfig {
		return
	}
	//fmt.Println("// ----------------------- Load configurations start ------------------------")
	//fmt.Println(c.String())
	//fmt.Println("// ----------------------- Load configurations end --------------------------")
}

func (c *Config) FormatTableName(name string) string {
	return c.Storage.DB.TablePrefix + name
}

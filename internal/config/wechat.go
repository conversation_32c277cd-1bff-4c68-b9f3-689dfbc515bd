package config

type WechatMini struct {
	AppID         string // 小程序APPID
	AppSecret     string // 小程序密钥
	MessageToken  string // 消息校验Token
	MessageAesKey string // 消息加密Key
}

type WechatPayment struct {
	AppID              string // 微信支付APPID
	MchID              string // 微信支付商户号
	MchApiKey          string // 微信支付APIv2密钥
	MchApiV3Key        string // 微信支付APIv3密钥
	WechatPaySerial    string // 微信支付证书序列号
	ApiClientCert      string // 微信支付证书
	ApiClientKey       string // 微信支付证书私钥
	CertificateKeyPath string // 微信支付证书密钥路径
	RSAPublicKeyPath   string // 微信支付公钥路径
	NotifyURL          string // 微信支付通知地址
}

package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// FoodsService 菜品业务逻辑
type FoodsService struct {
	DB *gorm.DB
}

// GetMerchantFoods 新建菜品业务逻辑
func (serv *FoodsService) GetMerchantFoods(ctx context.Context, merchantNo string) ([]*model.FoodModel, error) {
	var list []*model.FoodModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =? AND deleted_at IS NULL", merchantNo).Find(&list).Error
}

// GetMerchantAvailableFoods 获取商户可用的菜品业务逻辑
func (serv *FoodsService) GetMerchantAvailableFoods(ctx context.Context, merchantNo string, catId int64, keyword string, sellClearAll string) ([]*model.FoodModel, error) {
	var foods []*model.FoodModel
	// 预加载与食品相关的组合信息，仅加载类型为1的组合，并预加载其子组合（类型为2）
	result := util.GetDB(ctx, serv.DB).
		Where("foods.merchant_no = ? AND foods.state = ?", merchantNo, 1).
		Preload("Combos", func(db *gorm.DB) *gorm.DB {
			return db.Where("type = ?", 1).
				Preload("Childs", "type = ?", 2)
		})

	// 筛选已禁用的分类
	result = result.Joins("JOIN food_categories ON foods.food_category_id = food_categories.id").
		Where("food_categories.merchant_no = ? AND food_categories.state = ?", merchantNo, 1)

	// 分类
	if catId != 0 {
		result = result.Where("food_category_id = ?", catId)
	}
	// 关键字搜索
	if keyword != "" {
		result = result.Where("foods.name_ug LIKE ? OR foods.name_zh LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 如果 sellClearAll 参数存在，则仅筛选出清理状态为1的食品
	if sellClearAll != "" {
		result = result.Where("foods.cell_clear_state = ?", 1)
	}

	// 按照排序字段降序排列
	result = result.Order("sort asc")

	// 返回食品列表和可能的错误信息
	return foods, result.Find(&foods).Error
}

// UpdateFoodSellClearData 更新菜品沽清数据业务逻辑
func (serv *FoodsService) UpdateFoodSellClearData(ctx context.Context, formItem request.FoodSellClearDataRequest) error {
	food := model.FoodModel{}
	err := util.GetDB(ctx, serv.DB).
		Where("merchant_no =? and id =?", formItem.MerchantNo, formItem.ID).
		First(&food).Error
	if err != nil {
		return err
	}
	food.CellClearState = formItem.CellClearState
	food.SellClearCount = formItem.SellClearCount
	food.RemainingCount = formItem.RemainingCount
	return util.GetDB(ctx, serv.DB).
		Select("cell_clear_state", "sell_clear_count", "remaining_count").
		Save(&food).Error
}

package service

import (
	"context"
	"fmt"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/internal/printer"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
	"strconv"
	"strings"
	"time"
)

type PrintService struct {
	DB     *gorm.DB
	Client *redis.Client
}

type PrintData struct {
	Action     string `json:"action"`
	MerchantNo string `json:"merchant_no"`
	DeviceID   string `json:"device_id"`
	Content    string `json:"content"`
}
type PrintCmd struct {
	Data PrintData `json:"data"`
}

func (serv *PrintService) FoodAction(ctx context.Context, merchantNo string, action string, tableID int64, orderID int64, userNname string, list []*model.OrderDetailModel) {

	// 检查操作类型是否有效。
	if action != "加菜" && action != "退菜" && action != "催菜" {
		return
	}

	// 初始化组合菜品列表。
	var comboList []model.OrderDetailModel

	// 遍历订单详情列表，处理组合菜品。
	for i := range list {
		if list[i].IsCombo {
			ids := []int64{}
			for j := range *list[i].ComboInfo {
				ids = append(ids, (*list[i].ComboInfo)[j].FoodID)
			}
			comboFoods := []*model.FoodModel{}
			err := util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
				Where("merchant_no = ?", merchantNo).
				Where("id IN ?", ids).
				Find(&comboFoods).Error
			if err != nil {
				return
			}
			//套餐里面的美食分解
			for _, food := range comboFoods {
				for _, item := range *list[i].ComboInfo {
					if item.FoodID == food.ID {
						list[i].Food = food
						list[i].FoodsCount = item.Count
						comboList = append(comboList, *list[i])
					}
				}
			}
		}
	}
	// 移除原始列表中的组合菜品。
	for i := 0; i < len(list); i++ {
		if (list)[i].IsCombo {
			(list) = append((list)[:i], (list)[i+1:]...)
			i--
		}
	}

	printerList := []*model.PrinterModel{}
	// 获取打印机列表。
	err := util.GetDB(ctx, serv.DB).Where("merchant_no = ? AND status = ?", merchantNo, model.PrinterStateEnabled).Find(&printerList).Error
	if err != nil {
		return
	}

	// 初始化打印机与食物映射字典。
	mapPrinter := make(map[string][]*model.OrderDetailModel)

	// 遍历食物列表，根据食物和打印机的关联关系，将食物分配到相应的打印机。
	for i := range list {
		detail := list[i]
		printerFoods := []*model.FoodPrinterModel{}
		err := util.GetDB(ctx, serv.DB).Where("merchant_no = ? and food_id= ?", merchantNo, detail.FoodID).Find(&printerFoods).Error

		if err == nil && printerFoods != nil {
			for _, printerFood := range printerFoods {
				mapPrinter[printerFood.PrinterID] = append(mapPrinter[printerFood.PrinterID], detail)
			}
		}
	}

	table := model.TableModel{}
	err2 := util.GetDB(ctx, serv.DB).Where("id = ? and merchant_no = ?", tableID, merchantNo).Find(&table).Error
	if err2 != nil {
		return
	}
	order := model.CloudOrderModel{}
	err2 = util.GetDB(ctx, serv.DB).Where("id = ? and merchant_no = ?", orderID, merchantNo).Find(&order).Error
	if err2 != nil {
		return
	}

	// 遍历打印机与食物映射字典，根据打印机配置和操作类型，创建打印任务。
	for printerId, details := range mapPrinter {
		for _, printer := range printerList {
			if printer.ID != printerId || printer.Status < 1 {
				continue
			}
			if action == "拼单" && !printer.KitchenConfig.OrderMergeTicket {
				continue
			}
			if action == "退菜" && !printer.KitchenConfig.BackTicket {
				continue
			}

			// 根据打印机配置，决定是整单打印还是分单打印。
			if printer.KitchenConfig.SplitTicket {
				for i := range details {
					orderDetails := append([]*model.OrderDetailModel{}, details[i])
					serv.FoodActionSend(ctx, merchantNo, action, table, order, userNname, orderDetails, printer)
				}
			} else {
				serv.FoodActionSend(ctx, merchantNo, action, table, order, userNname, details, printer)
			}
		}
	}
}

func (serv *PrintService) FoodActionSend(ctx context.Context, merchantNo string, action string, table model.TableModel, order model.CloudOrderModel, userNname string, list []*model.OrderDetailModel, printerModel *model.PrinterModel) {
	isZh := i18n.IsZh(&ctx)
	isPrint80 := false
	printText := printer.NewPrintText(true)
	printText.SetLang(selectStr(isZh, "zh", "ug"))

	var title = ""
	if action == "加菜" || action == "提交订单" {
		title = selectStr(isZh, "下单", "يېڭى تاماق")
	} else if action == "退菜" || action == "全单退菜" {
		title = selectStr(isPrint80, "***", "") + selectStr(isZh, " 退菜 ", "تاماق قايتۇرۇش") + selectStr(isPrint80, "***", "")
	} else if action == "催单" || action == "催菜" {
		title = selectStr(isPrint80, "***", "*") + selectStr(isZh, " 催单 ", "تاماق سۈيلەش") + selectStr(isPrint80, "***", "*")
	}
	printText.SetTextType(printer.TEXT_TYPE_TITLE).
		SetAlign(printer.ALIGN_CENTER).
		Text(title).
		Text(selectStr(isZh, table.NameZh, table.NameUg)).
		SetTextType(printer.TEXT_TYPE_BODY).
		SetAlign(selectInt(isZh, printer.ALIGN_LEFT, printer.ALIGN_RIGHT)).
		Feed(2).
		Text(selectStr(isZh, "人数 : ", "ئادەم سانى : "), strconv.Itoa(order.CustomersCount)).
		Text(selectStr(("全单退菜" == action || "退菜" == action), selectStr(isZh, "退菜员 :", "قايتۇرغۇچى : "), selectStr(isZh, "点菜员 : ", "تىزىملىغۇچى : ")), userNname).
		Text(selectStr(isZh, "订单号 : ", "زاكاز نومۇرى : "), order.No).
		Text(selectStr(isZh, "时间 : ", "ۋاقتى : "), moment()).
		Line().
		SetTextType(printer.TEXT_TYPE_BODY).
		TextAlign(selectStr(isZh, "美食", "تاماق"), selectStr(isZh, "数量", "سانى"), "", "").Feed(1).
		SetTextType(printer.TEXT_TYPE_TITLE)

	for _, item := range list {
		printText.TextAlign(selectStr(isZh, item.Food.NameZh, item.Food.NameUg), strconv.FormatFloat(util.Round(item.FoodsCount), 'f', -1, 64), "", "").
			Text(selectStr(isZh, item.Food.NameUg, item.Food.NameZh)).
			Line()

		if !(item.Remarks == nil || *item.Remarks == "") {
			printText.TextBody(selectStr(isZh, "备注 : ", "ئەسكەرتىش : ") + *item.Remarks).
				Line()
		}
	}

	if order.Remarks != "" {
		printText.TextBody(selectStr(isZh, "订单备注 : ", "زاكاز ئەسكەرتىش : ") + order.Remarks).Line()
	}
	printText.Feed(3)

	if printerModel.ConnectionType == "cloud" {
		serv.send(ctx, merchantNo, printerModel.Cloud, vectorToHexString(printText.GetBytes()))
	}

}

func (serv *PrintService) send(ctx context.Context, merchantNo string, deviceID string, content string) {
	printData := PrintData{
		Action:     "打印指令",
		MerchantNo: merchantNo,
		DeviceID:   deviceID,
		Content:    content,
	}

	var printCmd = PrintCmd{
		Data: printData,
	}
	var cmdStr []byte
	cmdStr, _ = json.Marshal(printCmd)

	serv.Client.Publish(ctx, "print", cmdStr)
}

func (serv *PrintService) TableAction(ctx context.Context, merchantNo string, action string, table *model.TableModel, targetTable *model.TableModel, order *model.CloudOrderModel, targetOrder *model.CloudOrderModel, operator string) {
	// 检查操作类型是否有效。
	if action != "换台" && action != "并单" {
		return
	}

	// 获取打印机列表。
	printerList := []*model.PrinterModel{}
	// 获取打印机列表。
	err := util.GetDB(ctx, serv.DB).Where("merchant_no = ? AND status = ?", merchantNo, model.PrinterStateEnabled).Find(&printerList).Error
	if err != nil {
		return
	}

	// 遍历打印机列表，为符合条件的打印机创建打印任务。
	for _, printer := range printerList {
		// 检查打印机状态和厨房配置，以确定是否需要创建打印任务。
		if printer.Status == 1 && printer.KitchenConfig.TableChangeTicket {
			serv.TableActionSend(ctx, merchantNo, action, table, targetTable, order, targetOrder, operator, printer)
		}
	}
}

func (serv *PrintService) TableActionSend(ctx context.Context, merchantNo string, action string, table *model.TableModel, targetTable *model.TableModel, order *model.CloudOrderModel, targetOrder *model.CloudOrderModel, operator string, printerModel *model.PrinterModel) {
	isZh := i18n.IsZh(&ctx)
	printText := printer.NewPrintText(true)
	printText.SetLang(selectStr(isZh, "zh", "ug"))
	printText.SetTextType(printer.TEXT_TYPE_TITLE)

	changeOrder := "换台" == action
	var title = ""
	if changeOrder {
		if isZh {
			title = "换台"
		} else {
			title = "ئۈستەل يۆتكەش"
		}
	} else {
		if isZh {
			title = "订单拼单"
		} else {
			title = "زاكاز بىرلەشتۈرۈش"
			printText.SetTextType(printer.TEXT_TYPE_BODY_BIG)
		}
	}

	printText.SetAlign(printer.ALIGN_CENTER).
		Text(title).
		SetTextType(printer.TEXT_TYPE_BODY).
		SetAlign(selectInt(isZh, printer.ALIGN_LEFT, printer.ALIGN_RIGHT))

	if changeOrder {
		printText.Text(selectStr(isZh, "换台人 : ", "ئۈستەل يۆتكىگۈچى : "), operator)
	} else {
		printText.Text(selectStr(isZh, "拼单人 : ", "زاكاز بىرلەشتۈرگۈچى : "), operator)
	}

	printText.Text(selectStr(isZh, "原台 : ", "ئەسلى ئۈستەل : "), selectStr(isZh, table.NameZh, table.NameUg))
	printText.Text(selectStr(isZh, "订单号 : ", "زاكاز نومۇرى : "), order.No)

	if !changeOrder {
		printText.Text(selectStr(isZh, "人数 : ", "ئادەم سانى : "), strconv.Itoa(order.CustomersCount))
	}
	printText.Text(selectStr(isZh, "时间 : ", "ۋاقتى : "), momentShort())
	printText.Line()
	printText.Text(selectStr(isZh, "目标台 : ", "يۆتكەلگەن ئۈستەل : "), selectStr(isZh, targetTable.NameZh, targetTable.NameUg))
	if !changeOrder {
		printText.Text(selectStr(isZh, "目标订单 : ", "بىرلەشكەن زاكاز نومۇرى : "), targetOrder.No)
	}
	printText.Feed(3)

	if printerModel.ConnectionType == "cloud" {
		serv.send(ctx, merchantNo, printerModel.Cloud, vectorToHexString(printText.GetBytes()))
	}
}

func vectorToHexString(vB []byte) string {
	var hexString strings.Builder
	for _, b := range vB {
		// 将每个字节转换为两位十六进制字符串
		hexString.WriteString(fmt.Sprintf("%02X", b))
	}
	return hexString.String()
}

func selectStr(con bool, v1 string, v2 string) string {
	if con {
		return v1
	}
	return v2
}
func selectInt(con bool, v1 int, v2 int) int {
	if con {
		return v1
	}
	return v2
}

func getItemCount(item float64) string {
	// 将浮点数转换为字符串
	itemCount := fmt.Sprintf("%f", item)

	// 判断是否以 .0 结尾
	if strings.HasSuffix(itemCount, ".0") {
		// 去除 .0 部分
		itemCount = strings.Split(itemCount, ".0")[0]
	}

	return itemCount
}

func moment() string {
	var layout string
	layout = "2006-01-02 15:04:05" // Go标准时间格式模板
	return time.Now().Format(layout)
}

func momentShort() string {
	return time.Now().Format("15:04:05") // Go时间格式模板，对应 HH:mm:ss
}

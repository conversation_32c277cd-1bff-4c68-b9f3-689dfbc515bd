package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
)

// DebtTransactionService 赊账交易记录服务
type DebtTransactionService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// Create 创建消费记录
func (serv *DebtTransactionService) Create(ctx context.Context, log *model.DebtTransactionModel) error {
	return util.GetDB(ctx, serv.DB).Create(log).Error
}

// GetList 获取赊账交易记录列表
func (serv *DebtTransactionService) GetList(ctx context.Context, merchantNo string, req *request.DebtTransactionListRequest) ([]*model.DebtTransactionModel, *util.PaginationResult, error) {
	var transactions []*model.DebtTransactionModel

	// 构建查询
	query := util.GetDB(ctx, serv.DB).Model(&model.DebtTransactionModel{}).
		Where("merchant_no = ?", merchantNo).
		Order("id DESC")

	// 添加关联
	query = query.Preload("Cashier")

	// 应用过滤条件
	if req.HolderID != nil {
		query = query.Where("holder_id = ?", *req.HolderID)
	}

	if req.Type != nil {
		query = query.Where("type = ?", *req.Type)
	}

	if req.BeginAt != nil && req.EndAt != nil {
		query = query.Where("created_at BETWEEN ? AND ?", req.BeginAt, req.EndAt)
	} else if req.BeginAt != nil {
		query = query.Where("created_at >= ?", req.BeginAt)
	} else if req.EndAt != nil {
		query = query.Where("created_at <= ?", req.EndAt)
	}

	// 执行分页查询
	result, err := util.WrapPageQuery(ctx, query, req.PaginationParam, &transactions)
	if err != nil {
		return nil, nil, err
	}

	return transactions, result, nil
}

// GetByID 根据ID获取赊账交易记录
func (serv *DebtTransactionService) GetByID(ctx context.Context, merchantNo string, id int64) (*model.DebtTransactionModel, error) {
	var transaction model.DebtTransactionModel

	// 查询记录并加载关联
	ok, err := util.FindOne(
		ctx,
		util.GetDB(ctx, serv.DB).
			Where("merchant_no = ? AND id = ?", merchantNo, id).
			Preload("Holder").
			Preload("Cashier"),
		&transaction,
	)

	if err != nil {
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &transaction, nil
}

// GetDebtTransactionTypes 获取赊账交易类型列表
func (serv *DebtTransactionService) GetDebtTransactionTypes(ctx *context.Context, withTotal bool) []schema.DebtTransactionType {

	types := []schema.DebtTransactionType{
		{
			ID:   model.DebtTransactionTypeDebt,
			Name: i18n.Msg(ctx, "DebtTransactionTypeDebt"),
		},
		{
			ID:   model.DebtTransactionTypeRepayment,
			Name: i18n.Msg(ctx, "DebtTransactionTypeRepayment"),
		},
		{
			ID:   model.DebtTransactionTypeRefund,
			Name: i18n.Msg(ctx, "DebtTransactionTypeRefund"),
		},
	}
	if withTotal {
		return append([]schema.DebtTransactionType{
			{
				ID:   nil,
				Name: i18n.Msg(ctx, "DebtTransactionTypeTotal"),
			},
		}, types...)
	}
	return types
}

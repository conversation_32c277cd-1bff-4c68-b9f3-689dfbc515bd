package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// AreaService 区域业务逻辑
type AreaService struct {
	DB *gorm.DB
}

// GetMerchantAreas 返回商户的区域列表
func (serv *AreaService) GetMerchantAreas(ctx context.Context, merchantNo string) ([]*model.AreaModel, error) {
	var list []*model.AreaModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

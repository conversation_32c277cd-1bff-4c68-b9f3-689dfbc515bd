package service

import (
	"context"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"

	"gorm.io/gorm"
)

// PrinterService 打印机业务逻辑
type PrinterService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// checkDuplicateName checks if printer name already exists for the merchant
func (serv *PrinterService) checkDuplicateName(ctx context.Context, merchantNo, nameUg, nameZh, excludeID string) error {
	db := util.GetDB(ctx, serv.DB)

	var count int64
	query := db.Model(&model.PrinterModel{}).
		Where("merchant_no = ? AND (name_ug = ? OR name_zh = ?) AND status > ?",
			merchantNo, nameUg, nameZh, model.PrinterStateDeleted)

	if excludeID != "" {
		query = query.Where("id != ?", excludeID)
	}

	if err := query.Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return errors.ValidationError("", "PrinterNameUgExists")
	}

	return nil
}

// Create 创建打印机
func (serv *PrinterService) Create(ctx context.Context, req *request.PrinterCreateRequest) (*model.PrinterModel, error) {
	// Check for duplicate names first
	if err := serv.checkDuplicateName(ctx, req.MerchantNo, req.NameUg, req.NameZh, ""); err != nil {
		return nil, err
	}

	printer := &model.PrinterModel{
		ID:             util.NewXID(),
		MerchantNo:     req.MerchantNo,
		NameUg:         req.NameUg,
		NameZh:         req.NameZh,
		ConnectionType: req.ConnectionType,
		IpAddress:      req.IpAddress,
		UsbPort:        req.UsbPort,
		Cloud:          req.Cloud,
		PaperWidth:     req.PaperWidth,
		PrintMode:      req.PrintMode,
		Status:         model.PrinterStateEnabled,
		Buzzer:         req.Buzzer,
		CashierConfig:  req.CashierConfig,
		KitchenConfig:  req.KitchenConfig,
	}

	err := util.GetDB(ctx, serv.DB).Create(printer).Error
	if err != nil {
		return nil, err
	}

	return printer, nil
}

// Update 更新打印机
func (serv *PrinterService) Update(ctx context.Context, req *request.PrinterUpdateRequest) (*model.PrinterModel, error) {
	printer, err := serv.GetByID(ctx, req.MerchantNo, req.ID)
	if err != nil {
		return nil, err
	}

	// Check for duplicate names, excluding current printer
	if err := serv.checkDuplicateName(ctx, req.MerchantNo, req.NameUg, req.NameZh, req.ID); err != nil {
		return nil, err
	}

	// 更新字段
	printer.NameUg = req.NameUg
	printer.NameZh = req.NameZh
	printer.ConnectionType = req.ConnectionType
	printer.IpAddress = req.IpAddress
	printer.UsbPort = req.UsbPort
	printer.Cloud = req.Cloud
	printer.PaperWidth = req.PaperWidth
	printer.PrintMode = req.PrintMode
	printer.Buzzer = req.Buzzer
	printer.CashierConfig = req.CashierConfig
	printer.KitchenConfig = req.KitchenConfig

	err = util.GetDB(ctx, serv.DB).Updates(printer).Error
	if err != nil {
		return nil, err
	}

	return printer, nil
}

// Delete 删除打印机
func (serv *PrinterService) Delete(ctx context.Context, merchantNo, printerID string) error {
	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		result := util.GetDB(ctx, serv.DB).Model(&model.PrinterModel{}).
			Where("id = ? AND merchant_no = ? AND status > ?", printerID, merchantNo, model.PrinterStateDeleted).
			Updates(map[string]interface{}{
				"status":     model.PrinterStateDeleted,
				"deleted_at": time.Now(),
				"updated_at": time.Now(),
			})

		if result.Error != nil {
			return result.Error
		}

		if result.RowsAffected == 0 {
			return errors.NotFound("1001", "PrinterNotFound")
		}

		return util.GetDB(ctx, serv.DB).
			Where("printer_id = ? AND merchant_no = ?", printerID, merchantNo).Delete(&model.FoodPrinterModel{}).Error

	})

}

// UpdateStatus 更新打印机状态
func (serv *PrinterService) UpdateStatus(ctx context.Context, merchantNo, printerID string, status int) error {
	if status != model.PrinterStateEnabled && status != model.PrinterStateDisabled {
		return errors.BadRequest("1001", "InvalidStatus")
	}

	result := util.GetDB(ctx, serv.DB).Model(&model.PrinterModel{}).
		Where("id = ? AND merchant_no = ? AND status > ?", printerID, merchantNo, model.PrinterStateDeleted).
		Updates(map[string]interface{}{
			"status": status,
		})

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.NotFound("1002", "PrinterNotFound")
	}

	return nil
}

// GetByID 根据ID获取打印机
func (serv *PrinterService) GetByID(ctx context.Context, merchantNo, printerID string) (*model.PrinterModel, error) {
	printer := &model.PrinterModel{}
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("id = ? AND merchant_no = ? AND status > ?",
		printerID, merchantNo, model.PrinterStateDeleted), printer)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.NotFound("1003", "PrinterNotFound")
	}

	return printer, nil
}

// GetMerchantAvailablePrinters 返回商户的打印机列表
func (serv *PrinterService) GetMerchantAvailablePrinters(ctx context.Context, merchantNo string) ([]*model.PrinterModel, error) {
	var list []*model.PrinterModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =? AND status > ?", merchantNo, model.PrinterStateDeleted).Find(&list).Error
}

// GetMerchantPrinters 返回商户的打印机列表
func (serv *PrinterService) GetMerchantPrinters(ctx context.Context, merchantNo string) ([]*model.PrinterModel, error) {
	var list []*model.PrinterModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

// SyncPrinter 同步打印机信息 (本地  ===》 云端)
func (serv *PrinterService) SyncPrinter(ctx context.Context, req *request.PrinterSyncRequest) error {
	printer := &model.PrinterModel{}
	// 填充数据
	req.FillTo(printer)
	// 使用事务保存数据
	db := util.GetDB(ctx, serv.DB)
	// 检查打印机是否存在
	var existingPrinter model.PrinterModel
	err := db.Where("id = ? AND merchant_no = ?", req.ID, req.MerchantNo).First(&existingPrinter).Error

	if err == nil && existingPrinter.ID != "" {
		// 如果存在则更新
		if existingPrinter.UpdatedAt.Before(*req.UpdatedAt) {
			printer.ID = existingPrinter.ID
			return db.Save(printer).Error
		}
		return nil
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		// 如果不存在则创建
		return db.Create(printer).Error
	} else {
		// 其他错误
		return err
	}
}

// GetFoodPrinters 按分类返回商户的美食打印机关联列表
func (serv *PrinterService) GetFoodPrinters(ctx context.Context, merchantNo string) ([]*model.FoodCategoryModel, error) {
	var list []*model.FoodCategoryModel
	return list, util.GetDB(ctx, serv.DB).
		Where("merchant_no =? AND state = ?", merchantNo, model.FoodCategoryStateNormal).
		Preload("Foods", func(db *gorm.DB) *gorm.DB {
			return db.Where("state = ?", model.FoodStateNormal).
				Order("sort desc").
				Preload("Printers", "status = ?", model.PrinterStateEnabled)
		}).
		Order("sort desc").
		Find(&list).Error
}

// SavePrinterFoods 保存打印机与美食的关联关系
func (serv *PrinterService) SavePrinterFoods(ctx context.Context, merchantNo string, req *request.PrinterFoodsRequest) error {
	// 检查打印机是否存在
	printer, err := serv.GetByID(ctx, merchantNo, req.PrinterID)
	if err != nil {
		return err
	}

	// 使用事务进行保存
	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		db := util.GetDB(ctx, serv.DB)

		// 删除原有关联关系
		if err := db.Where("merchant_no = ? AND printer_id = ?",
			merchantNo, printer.ID).Delete(&model.FoodPrinterModel{}).Error; err != nil {
			return err
		}

		// 如果没有要关联的菜品，直接返回
		if len(req.FoodIDs) == 0 {
			return nil
		}

		// 构建新的关联关系
		foods := make([]*model.FoodPrinterModel, 0, len(req.FoodIDs))
		for _, foodID := range req.FoodIDs {
			foods = append(foods, &model.FoodPrinterModel{
				MerchantNo: merchantNo,
				FoodID:     foodID,
				PrinterID:  printer.ID,
			})
		}

		// 批量创建新的关联关系
		return db.Create(&foods).Error
	})
}

package service

import (
	"context"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
	"time"

	"gorm.io/gorm"
)

// MerchantRoleService 商户角色业务逻辑
type MerchantRoleService struct {
	DB *gorm.DB
}

// GetMerchantRoles 获取当前商户角色列表
func (serv *MerchantRoleService) GetMerchantRoles(ctx context.Context, merchantNo string, state *int8) ([]*model.MerchantRoleModel, error) {
	var list []*model.MerchantRoleModel
	query := util.GetDB(ctx, serv.DB).
		Where("merchant_no = ?", merchantNo) // Fixed spacing around =
	if state == nil {
		query = query.Where("state > ?", model.MerchantRoleStateDeleted)
	} else {
		query = query.Where("state = ?", *state) // Dereference state pointer
	}
	err := query.Find(&list).Error
	if err != nil {
		return nil, err // Return nil slice on error
	}
	return list, nil // Return nil error on success
}

// CreateMerchantRole 创建商户角色
func (serv *MerchantRoleService) CreateMerchantRole(
	ctx context.Context,
	merchantNo string,
	nameUg string,
	nameZh string,
	permissions []string,
	state int8,
) error {
	var role model.MerchantRoleModel
	role.MerchantNo = merchantNo
	role.NameUg = nameUg
	role.NameZh = nameZh
	role.Permissions = permissions
	role.State = state
	return util.GetDB(ctx, serv.DB).Create(&role).Error
}

// UpdateMerchantRole 更新商户角色
func (serv *MerchantRoleService) UpdateMerchantRole(
	ctx context.Context,
	merchantNo string,
	roleId int64,
	nameUg string,
	nameZh string,
	permissions util.TArray[string],
	state int8,
) error {

	return util.GetDB(ctx, serv.DB).
		Model(&model.MerchantRoleModel{}).
		Where("merchant_no = ? AND id = ?", merchantNo, roleId).
		Updates(map[string]interface{}{
			"name_ug":     nameUg,
			"name_zh":     nameZh,
			"permissions": permissions,
			"state":       state,
		}).Error
}

// DeleteMerchantRole 删除商户角色
func (serv *MerchantRoleService) DeleteMerchantRole(ctx context.Context, merchantNo string, roleId int64) error {
	return util.GetDB(ctx, serv.DB).
		Model(&model.MerchantRoleModel{}).
		Where("merchant_no = ? AND id = ?", merchantNo, roleId).
		Updates(map[string]interface{}{
			"state":      model.MerchantRoleStateDeleted,
			"deleted_at": time.Now(),
		}).Error
}

// GetMerchantRole 获取商户角色
func (serv *MerchantRoleService) GetMerchantRole(
	ctx context.Context,
	merchantNo string,
	roleId int64) (*model.MerchantRoleModel, error) {
	var role model.MerchantRoleModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("merchant_no = ? AND id = ?", merchantNo, roleId), &role)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, nil
	}
	return &role, nil

}

// Enable 启用商户角色
func (serv *MerchantRoleService) Enable(ctx context.Context, merchantNo string, roleId int64) error {
	return util.GetDB(ctx, serv.DB).
		Model(&model.MerchantRoleModel{}).
		Where("merchant_no = ? AND id = ?", merchantNo, roleId).
		Updates(map[string]interface{}{
			"state": model.MerchantRoleStateEnabled,
		}).Error
}

// Disable 禁用商户角色
func (serv *MerchantRoleService) Disable(ctx context.Context, merchantNo string, roleId int64) error {
	return util.GetDB(ctx, serv.DB).
		Model(&model.MerchantRoleModel{}).
		Where("merchant_no = ? AND id = ?", merchantNo, roleId).
		Updates(map[string]interface{}{
			"state": model.MerchantRoleStateDisabled,
		}).Error
}

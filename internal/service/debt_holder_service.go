package service

import (
	"context"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"time"
)

// DebtHolderService 赊账人业务
type DebtHolderService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// GetList 获取赊账人列表
func (serv *DebtHolderService) GetList(ctx context.Context, merchantNo string, req *request.DebtHolderListRequest) ([]*model.DebtHolderModel, *util.PaginationResult, error) {
	var debtHolders []*model.DebtHolderModel

	query := util.GetDB(ctx, serv.DB).Model(&model.DebtHolderModel{}).Preload("Cashier").Where("merchant_no = ?", merchantNo)

	// Apply filters
	if req.Keyword != "" {
		query = query.Where("phone LIKE ?", "%"+req.Keyword+"%")
	}

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 排序字段
	req.Sort.Apply(query, "-id", "id", "credit_limit", "balance", "status")

	// 执行查询并处理错误
	result, err := util.WrapPageQuery(ctx, query, req.PaginationParam, &debtHolders)
	if err != nil {
		return nil, nil, err
	}

	return debtHolders, result, nil
}

// GetTotalBalance 获取总欠账金额
func (serv *DebtHolderService) GetTotalBalance(ctx context.Context, merchantNo string) (float64, error) {
	var totalBalance float64
	err := util.GetDB(ctx, serv.DB).Model(&model.DebtHolderModel{}).
		Select("COALESCE(SUM(balance), 0)").
		Where("merchant_no = ? AND balance > 0", merchantNo).
		Scan(&totalBalance).Error
	if err != nil {
		return 0, err
	}
	return util.DivideFloat(totalBalance, 100), nil
}

// GetHoldersCount 获取欠账人数
func (serv *DebtHolderService) GetHoldersCount(ctx context.Context, merchantNo string) (int64, error) {
	var totalBalance int64
	err := util.GetDB(ctx, serv.DB).Model(&model.DebtHolderModel{}).
		Where("merchant_no = ?", merchantNo).
		Count(&totalBalance).Error
	if err != nil {
		return 0, err
	}
	return totalBalance, nil
}

// GetByID 根据ID获取赊账人信息
func (serv *DebtHolderService) GetByID(ctx context.Context, merchantNo string, id int64) (*model.DebtHolderModel, error) {
	var debtHolder model.DebtHolderModel

	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("merchant_no = ? AND id = ?", merchantNo, id), &debtHolder)
	if err != nil {
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &debtHolder, nil
}

// Create 创建新的赊账人
func (serv *DebtHolderService) Create(ctx context.Context, req *request.CreateDebtHolderRequest, createdBy int64) (*model.DebtHolderModel, error) {
	// 检查手机号是否已存在
	var count int64
	err := util.GetDB(ctx, serv.DB).Model(&model.DebtHolderModel{}).Where("merchant_no = ? AND phone = ?", req.MerchantNo, req.Phone).Count(&count).Error
	if err != nil {
		return nil, err
	}

	if count > 0 {
		return nil, errors.BadRequest("PhoneExists", "PhoneExists")
	}

	debtHolder := &model.DebtHolderModel{
		MerchantNo:  req.MerchantNo,
		NameZh:      req.NameZh,
		NameUg:      req.NameUg,
		Phone:       req.Phone,
		CreditLimit: req.CreditLimit,
		Balance:     0, // Initial balance is 0
		Status:      req.Status,
		CreatedBy:   createdBy,
	}

	err = util.GetDB(ctx, serv.DB).Create(debtHolder).Error
	if err != nil {
		return nil, err
	}

	return debtHolder, nil
}

// Update 更新赊账人信息
func (serv *DebtHolderService) Update(ctx context.Context, id int64, req *request.UpdateDebtHolderRequest) (*model.DebtHolderModel, error) {
	// 检查赊账人是否存在
	debtHolder, err := serv.GetByID(ctx, req.MerchantNo, id)
	if err != nil {
		return nil, err
	}

	if debtHolder == nil {
		return nil, errors.NotFound("1006", "DebtHolderNotFound")
	}

	// 更新字段
	debtHolder.NameZh = req.NameZh
	debtHolder.NameUg = req.NameUg
	debtHolder.CreditLimit = req.CreditLimit
	debtHolder.Status = req.Status

	err = util.GetDB(ctx, serv.DB).Save(debtHolder).Error
	if err != nil {
		return nil, err
	}

	return debtHolder, nil
}

// UpdateStatus 更新赊账人状态
func (serv *DebtHolderService) UpdateStatus(ctx context.Context, merchantNo, id string, status int) error {
	result := util.GetDB(ctx, serv.DB).Model(&model.DebtHolderModel{}).
		Where("merchant_no = ? AND id = ?", merchantNo, id).
		Updates(map[string]interface{}{
			"status": status,
		})

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.NotFound("1007", "DebtHolderNotFound")
	}

	return nil
}

// GetRepaymentOrderByPaymentNo 根据还款订单号获取客户信息
func (serv *DebtHolderService) GetRepaymentOrderByPaymentNo(ctx context.Context, paymentNo string) (*model.DebtRepaymentOrderModel, error) {
	// 先查询还款订单
	var order model.DebtRepaymentOrderModel
	_, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("no = ?", paymentNo).
		Preload("Holder"), &order)
	if err != nil {
		return nil, err
	}

	return &order, nil
}

// IncreaseBalance 增加余额
func (serv *DebtHolderService) IncreaseBalance(ctx context.Context, id int64, amount int64) error {

	// 使用SELECT FOR UPDATE锁定记录
	var holder model.DebtHolderModel
	if err := util.GetDB(ctx, serv.DB).
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", id).
		First(&holder).Error; err != nil {
		return err
	}

	// 更新余额
	return util.GetDB(ctx, serv.DB).Model(&model.DebtHolderModel{}).
		Where("id = ?", id).
		Update("balance", gorm.Expr("balance + ?", amount)).Error
}

// DecreaseBalance 减少余额
func (serv *DebtHolderService) DecreaseBalance(ctx context.Context, id int64, amount int64) error {

	// 使用SELECT FOR UPDATE锁定记录
	var holder model.DebtHolderModel
	if err := util.GetDB(ctx, serv.DB).Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", id).
		First(&holder).Error; err != nil {
		return err
	}

	// 更新余额
	return util.GetDB(ctx, serv.DB).Model(&model.DebtHolderModel{}).
		Where("id = ?", id).
		Update("balance", gorm.Expr("balance - ?", amount)).Error
}

// Pay 赊账支付
func (serv *DebtHolderService) Pay(
	ctx context.Context,
	holder *model.DebtHolderModel,
	merchantPayment *model.MerchantPaymentModel,
	cashierId int64) error {
	db := util.GetDB(ctx, serv.DB)

	err := serv.Trans.Exec(ctx, func(ctx context.Context) error {

		// 创建消费日志
		holderConsumptionLog := model.DebtTransactionModel{
			MerchantNo:    merchantPayment.MerchantNo,
			HolderID:      holder.ID,
			Type:          model.DebtTransactionTypeDebt,
			OrderNo:       &merchantPayment.OrderNo,
			Amount:        merchantPayment.Amount,
			Balance:       holder.Balance + merchantPayment.Amount,
			PaymentTypeID: merchantPayment.PaymentTypeID,
			CashierID:     cashierId,
		}

		if err := db.Create(&holderConsumptionLog).Error; err != nil {
			return err
		}

		// 更新赊账人余额
		if err := serv.IncreaseBalance(ctx, holder.ID, merchantPayment.Amount); err != nil {
			return err
		}
		// 更新商户付款记录
		err := db.Model(&model.MerchantPaymentModel{}).Where("id = ?", merchantPayment.ID).Updates(map[string]interface{}{
			"status":  consts.PAY_STATUS_PAID,
			"paid_at": time.Now(),
		}).Error
		return err
	})
	if err != nil {
		logging.Context(ctx).Error("赊账支付失败",
			zap.String("merchant_no", merchantPayment.MerchantNo),
			zap.Int64("holder_id", holder.ID),
			zap.Int64("cashier_id", cashierId),
			zap.Int64("amount", merchantPayment.Amount),
			zap.String("order_no", merchantPayment.OrderNo),
			zap.String("payment_no", merchantPayment.PaymentNo),
			zap.Error(err))
		db.Model(&model.MerchantPaymentModel{}).
			Where("id = ?", merchantPayment.ID).
			Update("status", consts.PAY_STATUS_FAILED)
	}
	return err
}

// GetHandoverNewHoldersCount 获取交接班新增赊账人数
func (serv *DebtHolderService) GetHandoverNewHoldersCount(ctx context.Context, handoverLog *model.HandoverLogModel, endAt time.Time) (int64, error) {
	db := util.GetDB(ctx, serv.DB)
	var newCustomers int64
	return newCustomers, db.Model(&model.DebtHolderModel{}).
		Where("created_by = ? AND merchant_no = ?", handoverLog.UserID, handoverLog.MerchantNo).
		Where("created_at BETWEEN ? AND ?", handoverLog.StartAt, endAt).
		Count(&newCustomers).Error
}

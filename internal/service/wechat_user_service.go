package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
	"time"
)

type WechatUserService struct {
	DB *gorm.DB
}

// GetByID 根据open_id获取用户信息
func (w *WechatUserService) GetByID(ctx context.Context, id int64) (user *model.WechatUserModel, err error) {
	var wechatUser model.WechatUserModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, w.DB).Where("id =?", id), &wechatUser)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &wechatUser, nil
}

// GetByOpenID 根据open_id获取用户信息
func (w *WechatUserService) GetByOpenID(ctx context.Context, openID string) (user *model.WechatUserModel, err error) {
	var wechatUser model.WechatUserModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, w.DB).Where("open_id =?", openID), &wechatUser)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &wechatUser, nil
}

// GetByOpenID 根据open_id获取用户信息
func (w *WechatUserService) GetByToken(ctx context.Context, token string) (user *model.WechatUserModel, err error) {
	var wechatUser model.WechatUserModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, w.DB).Where("api_token =?", token), &wechatUser)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &wechatUser, nil
}

// GetByMobile 根据手机号获取用户信息
func (w *WechatUserService) GetByMobile(ctx context.Context, mobile string) (user *model.WechatUserModel, err error) {
	var wechatUser model.WechatUserModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, w.DB).Where("mobile =?", mobile), &wechatUser)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &wechatUser, nil
}

// 根据手机号和open_id创建用户
func (w *WechatUserService) CreateByMobileAndOpenID(ctx context.Context, mobile, openID string) (user *model.WechatUserModel, err error) {
	now := time.Now()
	wechatUser := &model.WechatUserModel{
		OpenID:    openID,
		Mobile:    mobile,
		APIToken:  util.RandomString(32),
		LastLogin: &now,
	}
	err = util.GetDB(ctx, w.DB).Create(&wechatUser).Error
	if err != nil {
		return nil, err
	}
	return wechatUser, nil
}

// UpdateLastLogin 登录更新用户信息
func (w *WechatUserService) UpdateLastLogin(ctx context.Context, openID string) (err error) {
	now := time.Now()
	wechatUser := &model.WechatUserModel{
		LastLogin: &now,
		APIToken:  util.RandomString(32),
	}
	return util.GetDB(ctx, w.DB).Model(&model.WechatUserModel{}).Where("open_id =?", openID).Updates(wechatUser).Error
}

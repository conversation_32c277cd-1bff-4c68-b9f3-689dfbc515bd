package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// MerchantConfigService 商家配置业务逻辑
type MerchantConfigService struct {
	DB *gorm.DB
}

// GetMerchantMerchantConfigs 返回商户的商家配置列表
func (serv *MerchantConfigService) GetMerchantMerchantConfigs(ctx context.Context, merchantNo string) ([]*model.MerchantConfigModel, error) {
	var list []*model.MerchantConfigModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

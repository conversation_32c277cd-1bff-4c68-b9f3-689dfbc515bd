package service

import (
	"context"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
)

// LocalServerService 本地服务器业务逻辑
type LocalServerService struct {
	Trans *util.Trans
	DB    *gorm.DB
}

// RegisterLocalServer 注册本地服务
func (serv *LocalServerService) RegisterLocalServer(ctx context.Context,
	formItem *request.LoginForm) (*model.LocalServerModel, error) {

	var localServer *model.LocalServerModel

	// 先注销其他设备注册的本地服务
	err := serv.InactiveServersByMerchantNo(ctx, formItem.MerchantNo)
	if err != nil {
		return nil, err
	}
	// 根据UUID查询本地服务
	existsServer, err := serv.GetByUuid(ctx, formItem.UUID, formItem.MerchantNo)
	if err != nil {
		return nil, err
	}
	if existsServer == nil { // 不存在本地服务，则创建
		serverData := request.LocalServerForm{
			MerchantNo: formItem.MerchantNo,
			UserID:     formItem.UserID,
			UUID:       formItem.UUID,
			Ipv4:       formItem.Ipv4,
			Ipv6:       formItem.Ipv6,
			Token:      util.RandomString(32),
			Status:     1,
		}
		err := serverData.Validate()
		if err != nil {
			return nil, err
		}

		localServer, err = serv.Create(ctx, &serverData)
		if err != nil {
			return nil, err
		}
	} else {
		serverData := map[string]any{
			"user_id": formItem.UserID,
			"ipv4":    formItem.Ipv4,
			"ipv6":    formItem.Ipv6,
			"token":   util.RandomString(32),
			"status":  1,
		}
		localServer, err = serv.Update(ctx, existsServer.ID, serverData)
		if err != nil {
			return nil, err
		}
	}

	logging.Context(ctx).Info("设备注册成功：", zap.Any("localServer", localServer))

	return localServer, nil
}

// GetActiveServerByMerchantNo 根据商户号获取激活的本地服务
func (serv *LocalServerService) GetActiveServerByMerchantNo(ctx context.Context, merchantNo string) (*model.LocalServerModel, error) {
	var item model.LocalServerModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("merchant_no=?", merchantNo).
		Where("status=?", model.ServerActivated),
		&item)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return &item, nil
}

// GetByID 根据ID获取本地服务
func (serv *LocalServerService) GetByID(ctx context.Context, ID int64) (*model.LocalServerModel, error) {
	var item model.LocalServerModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("id=?", ID), &item)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return &item, nil
}

// GetByToken 根据token获取本地服务
func (serv *LocalServerService) GetByToken(ctx context.Context, token string) (*model.LocalServerModel, error) {
	var item model.LocalServerModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("token=?", token), &item)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return &item, nil
}

// GetByUuid 根据设备uuid获取本地服务
func (serv *LocalServerService) GetByUuid(ctx context.Context, uuid string, merchantNo string) (*model.LocalServerModel, error) {
	var item model.LocalServerModel
	ok, err := util.FindOne(ctx,
		util.GetDB(ctx, serv.DB).Where("uuid=?", uuid).Where("merchant_no=?", merchantNo),
		&item)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return &item, nil
}

// InactiveServersByMerchantNo 根据MerchantNo批量下线
func (serv *LocalServerService) InactiveServersByMerchantNo(ctx context.Context, merchantNo string) error {
	return util.GetDB(ctx, serv.DB).Model(new(model.LocalServerModel)).
		Where("merchant_no=?", merchantNo).
		Where("status=?", model.ServerActivated).
		Update("status", model.ServerInactive).Error

}

// Create 新建本地服务
func (serv *LocalServerService) Create(ctx context.Context, formItem *request.LocalServerForm) (*model.LocalServerModel, error) {
	localServer := &model.LocalServerModel{
		MerchantNo: formItem.MerchantNo,
		UserID:     formItem.UserID,
		UUID:       formItem.UUID,
		Ipv4:       formItem.Ipv4,
		Ipv6:       formItem.Ipv6,
		Token:      formItem.Token,
		Status:     1,
	}
	result := util.GetDB(ctx, serv.DB).Create(localServer)
	return localServer, result.Error
}

// Update 更新本地服务
func (serv *LocalServerService) Update(ctx context.Context, id int64, item map[string]any) (*model.LocalServerModel, error) {
	db := util.GetDB(ctx, serv.DB).Model(new(model.LocalServerModel)).Where("id=?", id)
	err := db.Updates(item).Error
	if err != nil {
		return nil, err
	}
	return serv.GetByID(ctx, id)
}

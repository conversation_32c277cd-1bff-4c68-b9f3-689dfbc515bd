package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
	"time"
)

type OrderService struct {
	DB *gorm.DB
}

func (serv *OrderService) CreateFromCloudOrder(ctx context.Context, order *model.CloudOrderModel) (*model.OrderModel, error) {
	orderData := model.OrderModel{
		MerchantNo:      order.MerchantNo,
		No:              order.No,
		CustomerID:      order.CustomerID,
		TableID:         order.TableID,
		OpenID:          &order.OpenID,
		TerminalID:      order.TerminalID,
		CustomersCount:  order.CustomersCount,
		FoodsCount:      order.FoodsCount,
		CostPrice:       order.CostPrice,
		OriginalPrice:   order.OriginalPrice,
		VipPrice:        order.VipPrice,
		Price:           order.Price,
		IgnorePrice:     order.IgnorePrice,
		CollectedAmount: &order.CollectedAmount,
		UserID:          order.UserID,
		CashierID:       &order.CashierID,
		GiveChange:      &order.GiveChange,
		State:           order.State,
		Remarks:         &order.Remarks,
		PaidAt:          order.PaidAt,
		TaxTicket:       order.TaxTicket,
		IsPrint:         order.IsPrint,
		WechatUserID:    &order.WechatUserID,
		IsScanOrder:     order.IsScanOrder,
		IsSync:          order.IsSync,
		CreatedAt:       order.CreatedAt,
		UpdatedAt:       order.UpdatedAt,
	}
	if err := util.GetDB(ctx, serv.DB).Select("*").Create(&orderData).Error; err != nil {
		return nil, err
	}
	return &orderData, nil
}

// GetByID 根据ID获取订单
func (serv *OrderService) GetByID(ctx context.Context, id int64, merchantNo string) (*model.OrderModel, error) {
	var order model.OrderModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id =?", id).Where("merchant_no = ?", merchantNo), &order)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return &order, nil
}

// GetByNo 根据订单号获取订单
func (serv *OrderService) GetByNo(ctx context.Context, orderNo string, merchantNo string) (*model.OrderModel, error) {
	var order model.OrderModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("no =?", orderNo).Where("merchant_no = ?", merchantNo), &order)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return &order, nil
}

// GetUserOrdersToday 获取用户当日已结账订单
func (serv *OrderService) GetUserOrdersToday(ctx context.Context, userID int64, merchantNo string) ([]*model.OrderModel, error) {
	var order []*model.OrderModel
	err := util.GetDB(ctx, serv.DB).Model(&model.OrderModel{}).
		Where("merchant_no = ?", merchantNo).
		Where("user_id = ?", userID).
		Where("state = ?", consts.ORDER_STATE_PAID).
		Where("paid_at >= ?", time.Now().Format("2006-01-02 00:00:00")).
		Preload("Table").
		Order("paid_at DESC").
		Find(&order).Error
	if err != nil {
		return nil, err
	}
	return order, nil
}

// GetUserOrderDetail 获取用户订单详情
func (serv *OrderService) GetUserOrderDetail(ctx context.Context, orderID int64, userId int64, merchantNo string) (*model.OrderModel, error) {
	var order *model.OrderModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Model(&model.OrderModel{}).
		Where("merchant_no=? AND id=? AND user_id=?", merchantNo, orderID, userId).
		Preload("Details", func(db *gorm.DB) *gorm.DB {
			return db.Where("state = ?", consts.ORDER_DETAIL_STATE_PAID).Preload("Food")
		}).
		Preload("Table").
		Preload("RefundLogs.Food").
		Preload("Payments.PaymentType"), &order)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return order, nil
}

// UpdateOrderRelatedIds 更新订单相关数据中的orderId
func (serv *OrderService) UpdateOrderRelatedIds(ctx context.Context, orderNo, merchantNo string, orderID int64) error {
	// 更新订单详情
	err := util.GetDB(ctx, serv.DB).Model(&model.OrderDetailModel{}).
		Where("order_no = ? and merchant_no = ?", orderNo, merchantNo).
		Update("order_id", orderID).Error
	if err != nil {
		return err
	}
	// 更新订单支付记录
	err = util.GetDB(ctx, serv.DB).Model(&model.MerchantPaymentModel{}).
		Where("order_no = ? and merchant_no = ?", orderNo, merchantNo).
		Update("order_id", orderID).Error
	if err != nil {
		return err
	}
	// 更新订单赊账记录
	err = util.GetDB(ctx, serv.DB).Model(&model.DebtTransactionModel{}).
		Where("order_no = ? and merchant_no = ?", orderNo, merchantNo).
		Update("order_id", orderID).Error
	if err != nil {
		return err
	}
	return nil
}

// UpdateForRefund 根据订单ID更新退款金额
func (serv *OrderService) UpdateForRefund(ctx context.Context, order *model.OrderModel, merchantNo string, refundIgnorePrice float64) error {
	db := util.GetDB(ctx, serv.DB)
	refundQuery := db.Model(&model.RefundOrderLogModel{}).Where("merchant_no = ? and order_id = ?", merchantNo, order.ID).Select("sum(refund_amount)")
	data := map[string]interface{}{
		"refund_price": gorm.Expr("(?)", refundQuery),
	}
	if order.IgnorePrice <= refundIgnorePrice {
		data["ignore_price"] = 0
	} else {
		data["ignore_price"] = gorm.Expr("(ignore_price - ?)", refundIgnorePrice)
	}
	return db.Model(&model.OrderModel{}).Where("id = ?", order.ID).Updates(data).Error
}

// GetHandoverData 获取交接班数据
func (serv *OrderService) GetHandoverData(
	ctx context.Context, merchantNo string, cashier *model.MerchantEmployeeModel,
	startAt time.Time, endAt time.Time) (*schema.HandoverOverviewFromOrder, error) {
	handoverTopData := &schema.HandoverOverviewFromOrder{
		UserID:   cashier.UserID,
		UserName: cashier.NameUg,
		StartAt:  util.FormatDateTime(&startAt),
		LeaveAt:  util.FormatDateTime(&endAt),
	}
	isZh := i18n.IsZh(&ctx)
	if isZh {
		handoverTopData.UserName = cashier.NameZh
	}
	db := util.GetDB(ctx, serv.DB).Model(&model.OrderModel{}).
		Where("merchant_no = ? and cashier_id = ? and state = ?", merchantNo, cashier.UserID, consts.ORDER_STATE_PAID).
		Where("paid_at >= ? and paid_at <= ?", startAt, endAt).
		Select("SUM(original_price) as receivable_amount, SUM(price) as paid_amount," +
			"SUM(refund_price) as refund_amount, SUM(customers_count) as customer_count, count(id) as order_count")
	return handoverTopData, db.First(&handoverTopData).Error
}

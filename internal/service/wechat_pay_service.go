package service

import (
	"context"
	"fmt"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/wechat"
	wechatV3 "github.com/go-pay/gopay/wechat/v3"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"net/http"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"ros-api-go/pkg/wechatx"
	"time"
)

// WechatPayService 微信支付服务
type WechatPayService struct {
	DB              *gorm.DB
	PaymentClient   *wechat.Client
	PaymentClientV3 *wechatV3.ClientV3
}

// MicroPay 微信付款码支付
func (serv *WechatPayService) MicroPay(ctx context.Context, authCode string, paymentLog *model.MerchantPaymentLogModel, merchant *model.MerchantModel) (*schema.PaymentResult, error) {

	// 校验支付金额
	if paymentLog.Amount < 1 {
		return nil, errors.BadRequest("", "PayAmountInvalid")
	}

	attach := make(gopay.BodyMap)
	attach.Set("pn", paymentLog.PaymentNo).
		Set("no", paymentLog.OrderNo)

	bm := make(gopay.BodyMap)
	bm.Set("nonce_str", util.RandomString(32)).
		Set("mch_id", merchant.MchID).
		Set("sub_mch_id", merchant.SubMchID).
		Set("body", paymentLog.Subject).
		Set("out_trade_no", paymentLog.OutTradeNo).
		Set("total_fee", paymentLog.Amount).
		Set("spbill_create_ip", paymentLog.ClientIP).
		Set("auth_code", authCode).
		Set("time_expire", paymentLog.ExpiresAt.Format("20060102150405")).
		Set("attach", attach)

	logging.Context(ctx).Debug("微信付款码支付请求参数",
		zap.String("merchantNo", paymentLog.MerchantNo),
		zap.String("orderNo", paymentLog.OrderNo),
		zap.Int64("orderType", paymentLog.OrderType),
		zap.String("paymentNo", paymentLog.PaymentNo),
		zap.Any("params", bm),
	)

	result, err := serv.PaymentClient.Micropay(ctx, bm)
	if err != nil {
		logging.Context(ctx).Error("微信付款码支付请求失败",
			zap.Int64("payment_log_id", paymentLog.ID),
			zap.Any("params", bm.JsonBody()),
			zap.Error(err))
		return nil, errors.BadRequest("", "支付请求失败")
	}

	if result.ReturnCode == "FAIL" {
		logging.Context(ctx).Error("微信付款码支付失败",
			zap.Int64("payment_log_id", paymentLog.ID),
			zap.Any("params", bm.JsonBody()),
			zap.Any("result", result))
		return nil, errors.BadRequest("", result.ReturnMsg)
	}

	// 验签
	ok, err := wechatx.Verified(result)
	if err != nil || !ok {
		logging.Context(ctx).Error("微信付款码支付验签失败",
			zap.Int64("payment_log_id", paymentLog.ID),
			zap.Error(err))
		return nil, errors.BadRequest("", "微信付款码支付验签失败")
	}

	// 如果支付成功直接返回
	if result.ResultCode == "SUCCESS" {
		paidAt, _ := wechatx.ParseWechatTimeV2(result.TimeEnd)
		return &schema.PaymentResult{
			Status:        consts.PAY_STATUS_PAID,
			PaymentTypeID: consts.PAY_TYPE_WECHAT,
			TransactionID: result.TransactionId,
			OutTradeNo:    result.OutTradeNo,
			OrderNo:       paymentLog.OrderNo,
			PaymentNo:     paymentLog.PaymentNo,
			MerchantNo:    paymentLog.MerchantNo,
			PaidAt:        &paidAt,
			TradeType:     result.TradeType,
			TradeState:    "SUCCESS",
			TradeDesc:     "支付成功",
		}, nil
	}

	// SYSTEMERROR 系统错误，订单状态未知，查询订单支付状态
	// USERPAYING 用户正在输入密码，继续查询订单支付状态
	// BANKERROR 银行系统错误，继续查询订单支付状态
	// 其他情况，则确定订单支付失败
	if result.ResultCode == "FAIL" &&
		(result.ErrCode != "USERPAYING" && result.ErrCode != "BANKERROR" && result.ErrCode != "SYSTEMERROR") {
		logging.Context(ctx).Error("微信付款码支付失败",
			zap.Int64("payment_log_id", paymentLog.ID),
			zap.Any("params", bm.JsonBody()),
			zap.Any("result", result))
		return nil, errors.BadRequest("", result.ErrCodeDes)
	}

	// 轮询查询订单状态
	tryTimes := 0
	var queryResult *schema.PaymentResult

	bgctx := context.Background()

	for tryTimes < 10 {
		logging.Context(bgctx).Info("微信付款码支付查询订单状态",
			zap.String("order_no", paymentLog.OrderNo),
			zap.String("payment_no", paymentLog.PaymentNo),
			zap.Int64("payment_log_id", paymentLog.ID),
			zap.String("out_trade_no", paymentLog.OutTradeNo),
			zap.Int("tryTimes", tryTimes))
		time.Sleep(time.Second * 4)
		queryResult, err = serv.QueryByOutTradeNo(bgctx, paymentLog.OutTradeNo, merchant.SubMchID)

		if err != nil {
			logging.Context(bgctx).Error("微信付款码支付查询订单状态失败",
				zap.String("order_no", paymentLog.OrderNo),
				zap.String("payment_no", paymentLog.PaymentNo),
				zap.Int64("payment_log_id", paymentLog.ID),
				zap.String("out_trade_no", paymentLog.OutTradeNo),
				zap.Error(err))
			return nil, err
		}
		if queryResult.TradeState == "PAYERROR" {
			logging.Context(bgctx).Error("微信付款码支付失败",
				zap.String("order_no", paymentLog.OrderNo),
				zap.String("payment_no", paymentLog.PaymentNo),
				zap.Int64("payment_log_id", paymentLog.ID),
				zap.String("out_trade_no", paymentLog.OutTradeNo),
				zap.Any("queryResult", queryResult))
			return nil, errors.BadRequest("1001", "PaymentFailed")
		}
		if queryResult.Status == consts.PAY_STATUS_PAID {
			return queryResult, nil
		}
		tryTimes++
	}

	// 如果查询结果失败，则撤销订单
	//tryTimes = 0
	//var reverseResult *wechat.ReverseResponse
	//for tryTimes < 10 {
	//	reverseResult, err = serv.ReverseMicroPay(ctx, paymentLog)
	//	if err != nil {
	//		logging.Context(ctx).Error("微信付款码支付撤销订单失败",
	//			zap.String("order_no", paymentLog.OrderNo),
	//			zap.String("payment_no", paymentLog.PaymentNo),
	//			zap.Int64("payment_log_id", paymentLog.ID),
	//			zap.String("out_trade_no", paymentLog.OutTradeNo),
	//			zap.Error(err))
	//		return nil, err
	//	}
	//	if reverseResult.Recall == "N" {
	//		return nil, errors.BadRequest("1002", "PaymentFailed")
	//	}
	//	time.Sleep(time.Second * 1)
	//	tryTimes++
	//}

	logging.Context(bgctx).Error("微信付款码支付失败",
		zap.String("order_no", paymentLog.OrderNo),
		zap.String("payment_no", paymentLog.PaymentNo),
		zap.Int64("payment_log_id", paymentLog.ID),
		zap.String("out_trade_no", paymentLog.OutTradeNo),
		zap.Any("params", bm.JsonBody()),
		zap.Any("queryResult", queryResult))

	return nil, errors.BadRequest("1003", "PaymentFailed")
}

// ReverseMicroPay 撤销付款码支付
func (serv *WechatPayService) ReverseMicroPay(ctx context.Context, paymentLog *model.MerchantPaymentLogModel) (*wechat.ReverseResponse, error) {

	// 校验支付金额
	if paymentLog.Amount < 1 {
		return nil, errors.BadRequest("", "PayAmountInvalid")
	}

	// 构造请求参数
	bm := make(gopay.BodyMap)
	bm.Set("mch_id", paymentLog.MchID).
		Set("sub_mch_id", paymentLog.SubMchID).
		Set("nonce_str", util.RandomString(32)).
		Set("out_trade_no", paymentLog.OutTradeNo).
		Set("sign_type", wechat.SignType_MD5)

	logging.Context(ctx).Debug("微信付款码撤销订单请求参数",
		zap.String("merchantNo", paymentLog.MerchantNo),
		zap.String("orderNo", paymentLog.OrderNo),
		zap.Int64("orderType", paymentLog.OrderType),
		zap.String("paymentNo", paymentLog.PaymentNo),
		zap.Any("params", bm),
	)

	// 请求撤销订单，成功后得到结果，沙箱环境下，证书路径参数可传nil
	wxRsp, err := serv.PaymentClient.Reverse(ctx, bm)
	logging.Context(ctx).Debug("微信付款码撤销订单请求结果",
		zap.Int64("payment_log_id", paymentLog.ID),
		zap.Any("params", bm.JsonBody()),
		zap.Any("result", wxRsp),
		zap.Error(err))

	if err != nil {
		return nil, err
	}
	if wxRsp.ReturnCode == "FAIL" {
		logging.Context(ctx).Error("付款码撤销订单失败",
			zap.Int64("payment_log_id", paymentLog.ID),
			zap.Any("params", bm.JsonBody()),
			zap.Any("result", wxRsp))
		return nil, errors.BadRequest("", wxRsp.ReturnMsg)
	}
	return wxRsp, nil
}

// QueryByOutTradeNo 根据outTradeNo查询订单
func (serv *WechatPayService) QueryByOutTradeNo(ctx context.Context, outTradeNo string, subMchID string) (*schema.PaymentResult, error) {

	bm := make(gopay.BodyMap)
	bm.Set("sub_mchid", subMchID)

	queryResponse, err := serv.PaymentClientV3.V3PartnerQueryOrder(ctx, wechatx.QUERY_BY_OUT_TRADE_NO, outTradeNo, bm)
	if err != nil {
		return nil, errors.BadRequest("", "微信支付结果查询失败", err)
	}

	logging.Context(ctx).Debug("微信支付结果查询结果", zap.Any("params", bm), zap.Any("result", queryResponse))

	// 解析attach信息
	paymentLog := &model.MerchantPaymentLogModel{}
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("out_trade_no=?", outTradeNo), paymentLog)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.NotFound("140", "PaymentLogNotFound")
	}

	//
	paymentResult := &schema.PaymentResult{
		Status:        consts.PAY_STATUS_UNPAID,
		PaymentTypeID: consts.PAY_TYPE_WECHAT,
		PaymentNo:     paymentLog.PaymentNo,
		MerchantNo:    paymentLog.MerchantNo,
		OrderNo:       paymentLog.OrderNo,
		OutTradeNo:    paymentLog.OutTradeNo,
	}

	if queryResponse.Code != 0 {
		logging.Context(ctx).Error("微信支付结果查询失败",
			zap.String("outTradeNo", outTradeNo),
			zap.Any("params", bm),
			zap.Any("queryResponse", queryResponse))
		errorVal := gopay.BodyMap{}
		err = jsoniter.Unmarshal([]byte(queryResponse.Error), &errorVal)
		if err != nil {
			return nil, err
		}
		return nil, errors.BadRequest("", errorVal.GetString("message"))
	}

	queryResult := queryResponse.Response

	if queryResult.TradeState != "SUCCESS" {
		paymentResult.TradeState = queryResult.TradeState
		paymentResult.TradeDesc = queryResult.TradeStateDesc
		return paymentResult, nil
	}

	// 解析时间
	paidAt, err := wechatx.ParseWechatTime(queryResult.SuccessTime)

	paymentResult.OpenID = queryResult.Payer.SpOpenid
	paymentResult.Status = consts.PAY_STATUS_PAID
	paymentResult.TradeType = queryResult.TradeType
	paymentResult.TradeState = queryResult.TradeState
	paymentResult.TradeDesc = queryResult.TradeStateDesc
	paymentResult.TransactionID = queryResult.TransactionId
	paymentResult.PaidAt = &paidAt
	return paymentResult, nil

}

//HandleRefundByRefundLog 根据退款记录退款
/*
 * RefundByOutTradeNo 微信退款
 * @param ctx 上下文
 * @param outTradeNo 商户订单号
 * @param PayAmount 支付金额
 * @param Amount 退款金额
 * @param subMchID 子商户号
 * @return 退款结果
 */
func (serv *WechatPayService) RefundByRefundLog(ctx context.Context, refundLog *model.MerchantRefundLogModel) (*schema.RefundResult, error) {
	// 初始化 BodyMap
	bm := make(gopay.BodyMap)
	// 选填 商户订单号（支付后返回的，一般是以42000开头）
	bm.
		Set("sub_mchid", refundLog.SubMchID).
		Set("out_trade_no", refundLog.OutTradeNo).
		Set("sign_type", wechat.SignType_MD5).
		// 必填 退款订单号（程序员定义的）
		Set("out_refund_no", refundLog.OutRefundNo).
		// 选填 退款描述
		Set("reason", "撤销付款").
		Set("notify_url", wechatx.GetRefundNotifyUrl(refundLog.OutRefundNo)).
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			// 退款金额:单位是分
			bm.Set("refund", refundLog.Amount). //实际退款金额
								Set("total", refundLog.PayAmount). // 订单支付金额
								Set("currency", "CNY")
		})

	logging.Context(ctx).Debug("微信退款请求参数", zap.Any("params", bm))

	refundRes, err := serv.PaymentClientV3.V3Refund(ctx, bm)

	if err != nil {
		return nil, err
	}

	if refundRes.Code != 0 {
		var errorMessage map[string]any
		err := jsoniter.Unmarshal([]byte(refundRes.Error), &errorMessage)
		if err != nil {
			return nil, err
		}

		logging.Context(ctx).Error("微信退款失败",
			zap.Any("refund_log_id", refundLog.ID),
			zap.String("merchant_no", refundLog.MerchantNo),
			zap.String("order_no", refundLog.OrderNo),
			zap.Any("params", bm),
			zap.Any("error", errorMessage))
		return nil, errors.BadRequest("", fmt.Sprintf("微信退款失败: %v", errorMessage["message"]))
	}

	refundResponse := refundRes.Response

	refundResult := &schema.RefundResult{
		Status:        consts.REFUND_STATUS_WAITING,
		PaymentTypeID: refundLog.PaymentTypeID,
		OutTradeNo:    refundLog.OutTradeNo,
		OutRefundNo:   refundLog.OutRefundNo,
	}

	// 如果成功
	if refundResponse.Status == "SUCCESS" {
		refundAt, err := wechatx.ParseWechatTime(refundResponse.SuccessTime)

		if err != nil {
			return nil, err
		}
		refundResult.Status = consts.REFUND_STATUS_SUCCESS
		refundResult.RefundAt = &refundAt
		refundResult.RefundID = refundResponse.RefundId
		refundResult.TradeState = refundResponse.Status
		refundResult.TradeDesc = refundResponse.UserReceivedAccount // 退款资金接收方
	}
	// 如果处理中
	if refundResponse.Status == "PROCESSING" {
		refundResult.Status = consts.REFUND_STATUS_PROCESSING
		refundResult.RefundID = refundResponse.RefundId
		refundResult.TradeState = refundResponse.Status
		refundResult.TradeDesc = refundResponse.UserReceivedAccount // 退款资金接收方
	}

	return refundResult, nil
}

// JsAPIPay 微信JSAPI支付
func (serv *WechatPayService) JsAPIPay(ctx context.Context, openid string, paymentLog *model.MerchantPaymentLogModel, merchant *model.MerchantModel) (*wechatV3.JSAPIPayParams, error) {

	// 校验支付金额
	if paymentLog.Amount < 1 {
		return nil, errors.BadRequest("", "PayAmountInvalid")
	}
	// 构造请求参数
	attach := fmt.Sprintf("pn=%s&no=%s", paymentLog.PaymentNo, paymentLog.OrderNo)
	bm := make(gopay.BodyMap)
	bm.Set("sp_appid", config.C.WechatPayment.AppID).
		Set("sp_mchid", config.C.WechatPayment.MchID).
		Set("sub_mchid", merchant.SubMchID).
		Set("description", paymentLog.Subject).
		Set("out_trade_no", paymentLog.OutTradeNo).
		Set("time_expire", paymentLog.ExpiresAt.Format(time.RFC3339)).
		Set("attach", attach).
		Set("notify_url", wechatx.GetPaymentNotifyUrl(paymentLog.OrderType, paymentLog.PaymentNo)).
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", paymentLog.Amount)
		}).
		SetBodyMap("payer", func(bm gopay.BodyMap) {
			bm.Set("sp_openid", openid)
		})

	logging.Context(ctx).Debug("微信JSAPI支付请求参数", zap.Any("params", bm))

	jsapiResponse, err := serv.PaymentClientV3.V3PartnerTransactionJsapi(ctx, bm)
	if err != nil {
		return nil, err
	}
	if jsapiResponse.Code != 0 {
		logging.Context(ctx).Error("Wechat JsAPIPay Failed",
			zap.String("merchantNo", paymentLog.MerchantNo),
			zap.String("paymentNo", paymentLog.PaymentNo),
			zap.String("outTradeNo", paymentLog.OutTradeNo),
			zap.Any("params", bm),
			zap.Any("jsapiResponse", jsapiResponse))
		errorVal := gopay.BodyMap{}
		err = jsoniter.Unmarshal([]byte(jsapiResponse.Error), &errorVal)
		if err != nil {
			return nil, errors.BadRequest("", "无法解析微信接口错误: %v", err)
		}
		return nil, errors.BadRequest("", errorVal.GetString("message"))
	}

	return serv.PaymentClientV3.PaySignOfJSAPI(config.C.WechatPayment.AppID, jsapiResponse.Response.PrepayId)
}

// NativePay 微信Native支付
func (serv *WechatPayService) NativePay(ctx context.Context, paymentLog *model.MerchantPaymentLogModel, merchant *model.MerchantModel) (string, error) {

	// 校验支付金额
	if paymentLog.Amount < 1 {
		return "", errors.BadRequest("", "PayAmountInvalid")
	}

	attach := fmt.Sprintf("pn=%s&no=%s", paymentLog.PaymentNo, paymentLog.OrderNo)
	bm := make(gopay.BodyMap)
	bm.Set("sp_appid", config.C.WechatPayment.AppID).
		Set("sp_mchid", config.C.WechatPayment.MchID).
		Set("sub_mchid", merchant.SubMchID).
		Set("description", paymentLog.Subject).
		Set("out_trade_no", paymentLog.OutTradeNo).
		Set("time_expire", paymentLog.ExpiresAt.Format(time.RFC3339)).
		Set("attach", attach).
		Set("notify_url", wechatx.GetPaymentNotifyUrl(paymentLog.OrderType, paymentLog.PaymentNo)).
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", paymentLog.Amount)
		})

	logging.Context(ctx).Debug("微信Native支付请求参数",
		zap.String("merchantNo", paymentLog.MerchantNo),
		zap.String("orderNo", paymentLog.OrderNo),
		zap.Any("params", bm),
	)

	wxRsp, err := serv.PaymentClientV3.V3PartnerTransactionNative(ctx, bm)
	if err != nil {
		return "", err
	}
	if wxRsp.Code != wechatV3.Success {
		logging.Context(ctx).Error("微信Native支付失败",
			zap.String("merchantNo", paymentLog.MerchantNo),
			zap.String("orderNo", paymentLog.OrderNo),
			zap.String("paymentNo", paymentLog.PaymentNo),
			zap.String("outTradeNo", paymentLog.OutTradeNo),
			zap.Any("params", bm),
			zap.Any("wxRsp", wxRsp))
		errorVal := gopay.BodyMap{}
		err = jsoniter.Unmarshal([]byte(wxRsp.Error), &errorVal)
		if err != nil {
			return "", errors.BadRequest("", "无法解析微信接口错误: %v", err)
		}
		return "", errors.BadRequest("", errorVal.GetString("message"))
	}

	return wxRsp.Response.CodeUrl, nil
}

// ParsePaymentNotify 解析微信支付通知
func (serv *WechatPayService) ParsePaymentNotify(ctx context.Context, req *http.Request) (*schema.PaymentResult, error) {

	// 解析微信回调
	notifyReq, err := wechatV3.V3ParseNotify(req)
	if err != nil {
		return nil, err
	}

	logging.Context(ctx).Info(
		"接受微信支付付款通知",
		zap.Any("notifyReq", notifyReq),
		zap.Any("SignInfo", wechatV3.SignInfo{
			HeaderTimestamp: req.Header.Get(wechatV3.HeaderTimestamp),
			HeaderNonce:     req.Header.Get(wechatV3.HeaderNonce),
			HeaderSignature: req.Header.Get(wechatV3.HeaderSignature),
			HeaderSerial:    req.Header.Get(wechatV3.HeaderSerial),
		}),
	)

	// 解密数据
	notifyResult, err := notifyReq.DecryptPartnerPayCipherText(string(serv.PaymentClientV3.ApiV3Key))
	logging.Context(ctx).Info("微信支付通知解密成功", zap.Any("notifyResult", notifyResult))
	if err != nil {
		return nil, err
	}

	// 解析attach信息
	paymentLog := &model.MerchantPaymentLogModel{}
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("out_trade_no=?", notifyResult.OutTradeNo), paymentLog)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.NotFound("140", "PaymentLogNotFound")
	}

	paidAt, err := wechatx.ParseWechatTime(notifyResult.SuccessTime)
	if err != nil {
		return nil, err
	}

	paymentResult := schema.PaymentResult{
		OpenID:        notifyResult.Payer.SpOpenid,
		Status:        consts.PAY_STATUS_PAID,
		MerchantNo:    paymentLog.MerchantNo,
		OrderNo:       paymentLog.OrderNo,   // no: orderNo
		PaymentNo:     paymentLog.PaymentNo, // pn: paymentNo
		PaymentTypeID: consts.PAY_TYPE_WECHAT,
		OutTradeNo:    notifyResult.OutTradeNo,
		TradeType:     notifyResult.TradeType,
		TradeState:    notifyResult.TradeState,
		TradeDesc:     notifyResult.TradeStateDesc,
		TransactionID: notifyResult.TransactionId,
		PaidAt:        &paidAt,
	}

	return &paymentResult, nil
}

// ParseRefundNotify 解析微信支付通知
func (serv *WechatPayService) ParseRefundNotify(ctx context.Context, req *http.Request) (*schema.RefundResult, error) {

	// 解析微信回调
	notifyReq, err := wechatV3.V3ParseNotify(req)
	if err != nil {
		return nil, err
	}
	logging.Context(ctx).Info(
		"接受微信退款通知",
		zap.Any("notifyReq", notifyReq),
		zap.Any("SignInfo", wechatV3.SignInfo{
			HeaderTimestamp: req.Header.Get(wechatV3.HeaderTimestamp),
			HeaderNonce:     req.Header.Get(wechatV3.HeaderNonce),
			HeaderSignature: req.Header.Get(wechatV3.HeaderSignature),
			HeaderSerial:    req.Header.Get(wechatV3.HeaderSerial),
		}),
	)

	// 解密数据
	notifyResult, err := notifyReq.DecryptPartnerRefundCipherText(string(serv.PaymentClientV3.ApiV3Key))
	if err != nil {
		return nil, err
	}
	logging.Context(ctx).Info("微信退款通知解密成功", zap.Any("notifyResult", notifyResult))

	paymentResult := schema.RefundResult{
		Status:        consts.REFUND_STATUS_SUCCESS,
		PaymentTypeID: consts.PAY_TYPE_WECHAT,
		OutTradeNo:    notifyResult.OutTradeNo,
		OutRefundNo:   notifyResult.OutRefundNo,
		TradeState:    notifyResult.RefundStatus,
		RefundID:      notifyResult.RefundId,
		TradeDesc:     notifyResult.UserReceivedAccount,
	}

	refundAt, err := wechatx.ParseWechatTime(notifyResult.SuccessTime)
	if err != nil {
		return nil, err
	}
	// 填充数据
	paymentResult.RefundAt = &refundAt

	return &paymentResult, nil
}

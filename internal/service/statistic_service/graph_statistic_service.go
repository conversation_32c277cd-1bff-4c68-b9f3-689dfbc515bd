package statistic_service

import (
	"context"
	"ros-api-go/internal/common/schema"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
	"time"

	"gorm.io/gorm"
)

type GraphStatisticService struct {
	DB *gorm.DB
}

func (serv *GraphStatisticService) GetStatistics(
	ctx context.Context, merchantNo string,
	beginAt, endAt time.Time) (int, []schema.GraphCustomerStatistics, []schema.GraphBusinessStatistics, []schema.GraphPayTypeStatistics) {
	// 计算天数差
	days := int(endAt.Sub(beginAt).Hours() / 24)
	var typeParam int

	typeParam = 1
	if days < 1 {
		typeParam = 3
	} else if days <= 31 {
		typeParam = 2
	}

	language := "zh"
	if !i18n.IsZh(&ctx) {
		language = "ug"
	}
	// 执行存储过程
	var customerStatistics []schema.GraphCustomerStatistics
	var orderStatistics []schema.GraphBusinessStatistics
	var payTypeStatistics []schema.GraphPayTypeStatistics

	util.GetDB(ctx, serv.DB).Raw(
		"CALL get_customer_order_statistics_by_merchant_for_graph(?, ?, ?, ?)",
		merchantNo, beginAt, endAt, typeParam).Scan(&customerStatistics)
	util.GetDB(ctx, serv.DB).Raw(
		"CALL get_order_statistics_by_merchant_for_graph(?, ?, ?, ?)",
		merchantNo, beginAt, endAt, typeParam).Scan(&orderStatistics)
	util.GetDB(ctx, serv.DB).Raw(
		"CALL get_payed_type_statistics_by_merchant_for_graph(?, ?, ?, ?, ?)",
		merchantNo, beginAt, endAt, language, typeParam).Scan(&payTypeStatistics)

	if customerStatistics == nil {
		customerStatistics = []schema.GraphCustomerStatistics{}
	}
	if orderStatistics == nil {
		orderStatistics = []schema.GraphBusinessStatistics{}
	}
	if payTypeStatistics == nil {
		payTypeStatistics = []schema.GraphPayTypeStatistics{}
	}
	return typeParam, customerStatistics, orderStatistics, payTypeStatistics
}

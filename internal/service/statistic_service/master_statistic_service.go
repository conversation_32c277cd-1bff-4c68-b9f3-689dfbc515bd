package statistic_service

import (
	"context"
	"fmt"
	"gorm.io/gorm"
	"math"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
	"time"
)

type MasterStatisticService struct {
	DB *gorm.DB
}

type Names struct {
	Yesterday string
	LastWeek  string
}

func getNames(ctx *context.Context, weekDay time.Weekday, days int, isToday bool, isYesterday bool) Names {
	if isToday {
		return Names{
			Yesterday: i18n.Msg(ctx, "Days.Yesterday"),
			LastWeek:  fmt.Sprintf("%s%s", i18n.Msg(ctx, "Days.Last"), util.GetWeekday(ctx, weekDay)),
		}
	}
	if isYesterday {
		return Names{
			Yesterday: fmt.Sprintf("%s-%s", time.Now().AddDate(0, 0, -2).Format("01.02"), i18n.Msg(ctx, "Days.Day")),
			LastWeek:  fmt.Sprintf("%s%s", i18n.Msg(ctx, "Days.Last"), util.GetWeekday(ctx, weekDay)),
		}
	}
	if days == 7 {
		return Names{
			Yesterday: "",
			LastWeek:  i18n.Msg(ctx, "Days.LastWeek"),
		}
	}

	return Names{
		Yesterday: "",
		LastWeek:  fmt.Sprintf("%s%d%s", i18n.Msg(ctx, "Days.Last"), days, i18n.Msg(ctx, "Days.Day2")),
	}
}

func (serv *MasterStatisticService) GetStatistics(ctx context.Context, merchantNo string, cashierID int64, startAt, endAt time.Time) (*schema.MasterHomeStatisticsData, error) {

	weekDay := startAt.Weekday()
	days := int(math.Ceil(endAt.Sub(startAt).Hours() / 24))

	isToday := startAt.Format("2006-01-02") == time.Now().Format("2006-01-02") && days == 1
	isYesterday := startAt.Format("2006-01-02") == time.Now().AddDate(0, 0, -1).Format("2006-01-02") && days == 1

	fmt.Println(weekDay)

	startAtYesterday := startAt.AddDate(0, 0, -1)
	endAtYesterday := endAt.AddDate(0, 0, -1)

	startAtLastWeek := startAt.AddDate(0, 0, -7)
	endAtLastWeek := endAt.AddDate(0, 0, -7)

	// 订单金额(总金额/实收金额/总成本)
	cashInfo, err := serv.getCashInfo(ctx, merchantNo, cashierID, startAt, endAt, false)
	if err != nil {
		return nil, err
	}
	// 上周
	cashInfoLastWeek, err := serv.getCashInfo(ctx, merchantNo, cashierID, startAtLastWeek, endAtLastWeek, false)
	if err != nil {
		return nil, err
	}
	cashInfoYesterday, err := serv.getCashInfo(ctx, merchantNo, cashierID, startAtYesterday, endAtYesterday, true)
	if err != nil {
		return nil, err
	}

	// 订单金额(总人数/总单数/单均消费/人均消费)
	orderStatisticsInfo, err := serv.getOrderStatisticsInfo(ctx, cashInfo, merchantNo, cashierID, startAt, endAt, false)
	if err != nil {
		return nil, err
	}
	// 上周
	orderStatisticsInfoLastWeek, err := serv.getOrderStatisticsInfo(ctx, cashInfoLastWeek, merchantNo, cashierID, startAtLastWeek, endAtLastWeek, false)
	if err != nil {
		return nil, err
	}
	// 昨天
	orderStatisticsInfoYesterday, err := serv.getOrderStatisticsInfo(ctx, cashInfoYesterday, merchantNo, cashierID, startAtYesterday, endAtYesterday, true)
	if err != nil {
		return nil, err
	}

	// 取消订单金额
	cancelAmount, err := serv.getCancelAmount(ctx, merchantNo, cashierID, startAt, endAt)
	if err != nil {
		return nil, err
	}

	names := getNames(&ctx, weekDay, days, isToday, isYesterday)

	// 头部部分
	cashInfoHead := []*schema.MasterHeadCashInfoItem{
		{
			Name:          i18n.Msg(&ctx, "StatTotalAmount"),
			Value:         util.Round(cashInfo.TotalAmount),
			YesterdayName: names.Yesterday,
			Yesterday:     util.Round(cashInfoYesterday.TotalAmount),
			LastWeekName:  names.LastWeek,
			LastWeek:      util.Round(cashInfoLastWeek.TotalAmount),
			IsPrice:       true,
		}, // 总金额
		{
			Name:          i18n.Msg(&ctx, "StatRealAmount"),
			Value:         util.Round(cashInfo.RealAmount),
			YesterdayName: names.Yesterday,
			Yesterday:     util.Round(cashInfoYesterday.RealAmount),
			LastWeekName:  names.LastWeek,
			LastWeek:      util.Round(cashInfoLastWeek.RealAmount),
			IsPrice:       true,
		}, // 实收金额
		{
			Name:          i18n.Msg(&ctx, "StatTotalDiscount"),
			Value:         util.Round(util.SubtractFloatMore(cashInfo.TotalAmount, cashInfo.RealAmount, orderStatisticsInfo.IgnorePrice)),
			YesterdayName: names.Yesterday,
			Yesterday:     util.Round(util.SubtractFloatMore(cashInfoYesterday.TotalAmount, cashInfoYesterday.RealAmount, orderStatisticsInfoYesterday.IgnorePrice)),
			LastWeekName:  names.LastWeek,
			LastWeek:      util.Round(util.SubtractFloatMore(cashInfoLastWeek.TotalAmount, cashInfoLastWeek.RealAmount, orderStatisticsInfoLastWeek.IgnorePrice)),
			IsPrice:       true,
		}, // 优惠金额
		{
			Name:          i18n.Msg(&ctx, "StatOrdersCount"),
			Value:         orderStatisticsInfo.OrderCount,
			YesterdayName: names.Yesterday,
			Yesterday:     orderStatisticsInfoYesterday.OrderCount,
			LastWeekName:  names.LastWeek,
			LastWeek:      orderStatisticsInfoLastWeek.OrderCount,
			IsPrice:       false,
		}, // 订单数量
	}

	// 计算头部部分比率
	serv.calcRate(cashInfoHead)

	// 详情部分
	cashInfoDetail := []*schema.MasterDetailInfoItem{
		{Name: i18n.Msg(&ctx, "StatIgnorePrice"), Value: util.Round(orderStatisticsInfo.IgnorePrice), IsPrice: true},    // 抹零价格
		{Name: i18n.Msg(&ctx, "StatRefundedAmount"), Value: util.Round(orderStatisticsInfo.RefundPrice), IsPrice: true}, // 退款金额
		{Name: i18n.Msg(&ctx, "StatCanceledAmount"), Value: util.Round(cancelAmount), IsPrice: true},                    // 退菜金额
		{
			Name:    i18n.Msg(&ctx, "StatTotalDiscount"),
			Value:   util.Round(util.SubtractFloatMore(cashInfo.TotalAmount, cashInfo.RealAmount, orderStatisticsInfo.IgnorePrice)),
			IsPrice: true,
		}, // 优惠金额
		{Name: i18n.Msg(&ctx, "StatOrderAvg"), Value: util.Round(orderStatisticsInfo.OrderAvg), IsPrice: true},          // 订单均价
		{Name: i18n.Msg(&ctx, "StatCustomersAvg"), Value: util.Round(orderStatisticsInfo.CustomersAvg), IsPrice: true},  // 客户均价
		{Name: i18n.Msg(&ctx, "StatCustomerCount"), Value: float64(orderStatisticsInfo.CustomersCount), IsPrice: false}, // 客户数量
	}

	return &schema.MasterHomeStatisticsData{
		HeadCashInfo: cashInfoHead,
		DetailInfo:   cashInfoDetail,
	}, nil

}

func (serv *MasterStatisticService) calcRate(cashInfos []*schema.MasterHeadCashInfoItem) {

	for _, item := range cashInfos {

		value := 0.0
		yesterdayValue := 0.0
		lastWeekValue := 0.0
		switch item.Value.(type) {
		case int:
			value = float64(item.Value.(int))
			yesterdayValue = float64(item.Yesterday.(int))
			lastWeekValue = float64(item.LastWeek.(int))
		case float64:
			value = item.Value.(float64)
			yesterdayValue = item.Yesterday.(float64)
			lastWeekValue = item.LastWeek.(float64)
		}
		if yesterdayValue > 0 {
			item.YesterdayRatio = util.MultiplyFloat(util.Round(util.DivideFloat(util.SubtractFloat(value, yesterdayValue), yesterdayValue)), 100)
		} else {
			item.YesterdayRatio = 100
			if value == 0 {
				item.YesterdayRatio = 0
			}
		}
		if lastWeekValue > 0 {
			item.LastWeekRatio = util.MultiplyFloat(util.Round(util.DivideFloat(util.SubtractFloat(value, lastWeekValue), lastWeekValue)), 100)
		} else {
			item.LastWeekRatio = 100
			if value == 0 {
				item.LastWeekRatio = 0
			}
		}

	}
}

// GetOrderAmount 获取订单金额(总人数/总单数/单均消费/人均消费)
func (serv *MasterStatisticService) getOrderStatisticsInfo(ctx context.Context, cashInfo *schema.BusinessHeadCashInfoData, merchantNo string, cashierID int64, startAt, endAt time.Time, last bool) (*schema.BusinessOrderStatisticsInfo, error) {

	// 如果统计天数大于1天，则返回空数据
	days := math.Ceil(endAt.Sub(startAt).Hours() / 24)
	if days > 1 && last {
		return &schema.BusinessOrderStatisticsInfo{}, nil
	}
	var data schema.BusinessOrderStatisticsInfo

	query := util.GetDB(ctx, serv.DB).
		Model(&model.OrderModel{}).
		Select("SUM(customers_count) AS customers_count,"+
			"SUM(ignore_price) AS ignore_price,SUM(refund_price) AS refund_price, count(id) AS order_count").
		Where("merchant_no =? AND state = ?", merchantNo, consts.ORDER_STATE_PAID).
		Where("paid_at BETWEEN ? AND ?", startAt, endAt)

	if cashierID > 0 {
		query = query.Where("cashier_id =?", cashierID)
	}
	err := query.Scan(&data).Error
	if err != nil {
		return nil, err
	}
	if data.OrderCount > 0 {
		data.OrderAvg = util.Round(cashInfo.RealAmount / float64(data.OrderCount))
	}
	if data.CustomersCount > 0 {
		data.CustomersAvg = util.Round(cashInfo.RealAmount / float64(data.CustomersCount))
	}
	return &data, err

}

// GetCancelAmount 获取取消订单金额
func (serv *MasterStatisticService) getCancelAmount(ctx context.Context, merchantNo string, cashierID int64, startAt, endAt time.Time) (float64, error) {

	var canceledAmount float64
	query := util.GetDB(ctx, serv.DB).
		Table("order_details od").
		Select("COALESCE(SUM(total_price), 0) AS amount").
		Joins("JOIN orders o ON od.order_id = o.id").
		Where("o.merchant_no =? AND od.merchant_no = ? AND od.state = ?", merchantNo, merchantNo, consts.ORDER_DETAIL_STATE_CANCEL).
		Where("o.created_at BETWEEN ? AND ?", startAt, endAt)

	if cashierID > 0 {
		query = query.Where("o.user_id =?", cashierID)
	}
	return canceledAmount, query.Scan(&canceledAmount).Error
}

// GetCashInfo 获取订单实际金额(退款后)(总金额/实收金额/总成本)
func (serv *MasterStatisticService) getCashInfo(
	ctx context.Context, merchantNo string, cashierID int64,
	startAt, endAt time.Time, last bool) (*schema.BusinessHeadCashInfoData, error) {
	// 如果统计天数大于1天，则返回空数据
	days := math.Ceil(endAt.Sub(startAt).Hours() / 24)
	if days > 1 && last {
		return &schema.BusinessHeadCashInfoData{}, nil
	}
	var cashInfo schema.BusinessHeadCashInfoData
	query := util.GetDB(ctx, serv.DB).
		Table("order_details od").
		Select(
			"SUM(od.original_price * od.foods_count) AS total_amount,SUM(od.total_price) AS real_amount").
		Joins("JOIN orders o ON od.order_id = o.id").
		Where("o.merchant_no =? AND o.state = ? AND o.paid_at BETWEEN ? AND ?", merchantNo, consts.ORDER_STATE_PAID, startAt, endAt).
		Where("od.merchant_no =? AND od.state = ?", merchantNo, consts.ORDER_DETAIL_STATE_PAID)
	if cashierID > 0 {
		query = query.Where("o.cashier_id = ?", cashierID)
	}
	err := query.Scan(&cashInfo).Error
	if err != nil {
		return nil, err
	}
	return &cashInfo, err
}

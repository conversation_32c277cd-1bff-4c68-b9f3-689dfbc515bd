package statistic_service

import (
	"context"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
	"time"

	"gorm.io/gorm"
)

type BusinessStatisticService struct {
	DB *gorm.DB
}

func (serv *BusinessStatisticService) GetStatistics(ctx context.Context, merchantNo string, cashierID int64, startAt, endAt time.Time) (*schema.BusinessStatisticsData, error) {

	// 订单金额(总金额/实收金额/总成本)
	cashInfo, err := serv.getCashInfo(ctx, merchantNo, cashierID, startAt, endAt)
	if err != nil {
		return nil, err
	}

	// 订单金额(总人数/总单数/单均消费/人均消费)
	orderStatisticsInfo, err := serv.getOrderStatisticsInfo(ctx, cashInfo, merchantNo, cashierID, startAt, endAt)
	if err != nil {
		return nil, err
	}

	// 取消订单金额
	cancelAmount, err := serv.getCancelAmount(ctx, merchantNo, cashierID, startAt, endAt)
	if err != nil {
		return nil, err
	}

	// 订单付款类型金额占比
	orderProportions, err := serv.getOrderProportions(ctx, merchantNo, cashierID, startAt, endAt, orderStatisticsInfo.GiveChange)
	if err != nil {
		return nil, err
	}

	// 营业额（总交易额/实收金额/优惠合计/退菜合计/退款合计/总成本/总利润/抹零价格）
	headCashInfo := serv.getHeadCashInfo(ctx, cashInfo, cancelAmount, orderStatisticsInfo)

	// 会员统计
	vipStatistics, err := serv.getVipStatistics(ctx, merchantNo, cashierID, startAt, endAt)
	if err != nil {
		return nil, err
	}

	// 赊账统计
	debtStatistics, err := serv.getDebtStatistics(ctx, merchantNo, cashierID, startAt, endAt)
	if err != nil {
		return nil, err
	}

	data := &schema.BusinessStatisticsData{
		BeginDate:           startAt.Format(time.DateTime),
		EndDate:             endAt.Format(time.DateTime),
		HeadCashInfo:        headCashInfo,
		OrderStatisticsInfo: orderStatisticsInfo,
		OrderProportion:     orderProportions,
		Vip:                 vipStatistics,
		Debt:                debtStatistics,
	}

	return data, nil
}

// GetHeadStatistic 获取总览统计数据(总交易额/实收金额/优惠合计/退菜合计/退款合计/总成本/总利润/抹零价格)
func (serv *BusinessStatisticService) getHeadCashInfo(ctx context.Context, cashInfo *schema.BusinessHeadCashInfoData, cancelAmount float64, orderStatisticsInfo *schema.BusinessOrderStatisticsInfo) []*schema.BusinessHeadCashInfoItem {

	totalAmount := util.Round(util.AddFloatMore(cashInfo.TotalAmount, cancelAmount))

	headStat := []*schema.BusinessHeadCashInfoItem{
		{Name: i18n.Msg(&ctx, "StatTotalAmount"), Value: totalAmount},                                                                                                      // 总交易额
		{Name: i18n.Msg(&ctx, "StatRealAmount"), Value: util.Round(cashInfo.RealAmount)},                                                                                   // 实收金额
		{Name: i18n.Msg(&ctx, "StatTotalDiscount"), Value: util.Round(util.SubtractFloatMore(cashInfo.TotalAmount, cashInfo.RealAmount, orderStatisticsInfo.IgnorePrice))}, // 优惠金额
		{Name: i18n.Msg(&ctx, "StatCanceledAmount"), Value: util.Round(cancelAmount)},                                                                                      // 退菜金额
		{Name: i18n.Msg(&ctx, "StatRefundedAmount"), Value: util.Round(orderStatisticsInfo.RefundPrice)},                                                                   // 退款金额
		{Name: i18n.Msg(&ctx, "StatTotalCost"), Value: util.Round(cashInfo.TotalCost)},                                                                                     // 总成本
		{Name: i18n.Msg(&ctx, "StatTotalProfit"), Value: util.Round(util.SubtractFloatMore(cashInfo.RealAmount, cashInfo.TotalCost))},                                      // 总利润
		{Name: i18n.Msg(&ctx, "StatIgnorePrice"), Value: util.Round(orderStatisticsInfo.IgnorePrice)},                                                                      // 抹零价格
	}

	return headStat

}

// GetCashInfo 获取订单实际金额(退款后)(总金额/实收金额/总成本)
func (serv *BusinessStatisticService) getCashInfo(
	ctx context.Context, merchantNo string, cashierID int64,
	startAt, endAt time.Time) (*schema.BusinessHeadCashInfoData, error) {
	var cashInfo schema.BusinessHeadCashInfoData
	query := util.GetDB(ctx, serv.DB).
		Table("order_details od").
		Select(
			"SUM(od.original_price * od.foods_count) AS total_amount,"+
				"SUM(od.total_price) AS real_amount,"+
				"SUM(od.cost_price * od.foods_count) AS total_cost").
		Joins("JOIN orders o ON od.order_id = o.id").
		Where("o.merchant_no =? AND o.paid_at BETWEEN ? AND ?", merchantNo, startAt, endAt).
		Where("o.state = ?", consts.ORDER_STATE_PAID).
		Where("od.state = ?", consts.ORDER_DETAIL_STATE_PAID)
	if cashierID > 0 {
		query = query.Where("o.cashier_id = ?", cashierID)
	}
	err := query.Scan(&cashInfo).Error
	if err != nil {
		return nil, err
	}
	return &cashInfo, err
}

// GetOrderAmount 获取订单金额(总人数/总单数/单均消费/人均消费)
func (serv *BusinessStatisticService) getOrderStatisticsInfo(ctx context.Context, cashInfo *schema.BusinessHeadCashInfoData, merchantNo string, cashierID int64, startAt, endAt time.Time) (*schema.BusinessOrderStatisticsInfo, error) {

	var data schema.BusinessOrderStatisticsInfo

	query := util.GetDB(ctx, serv.DB).
		Model(&model.OrderModel{}).
		Select("SUM(customers_count) AS customers_count,"+
			"SUM(ignore_price) AS ignore_price,SUM(refund_price) AS refund_price, SUM(give_change) as give_change, count(id) AS order_count").
		Where("merchant_no =? AND state = ?", merchantNo, consts.ORDER_STATE_PAID).
		Where("paid_at BETWEEN ? AND ?", startAt, endAt)

	if cashierID > 0 {
		query = query.Where("cashier_id =?", cashierID)
	}
	err := query.Scan(&data).Error
	if err != nil {
		return nil, err
	}
	if data.OrderCount > 0 {
		data.OrderAvg = util.Round(util.DivideFloat(cashInfo.RealAmount, float64(data.OrderCount)))
	}
	if data.CustomersCount > 0 {
		data.CustomersAvg = util.Round(util.DivideFloat(cashInfo.RealAmount, float64(data.CustomersCount)))
	}
	return &data, err

}

// GetCancelAmount 获取取消订单金额
func (serv *BusinessStatisticService) getCancelAmount(ctx context.Context, merchantNo string, cashierID int64, startAt, endAt time.Time) (float64, error) {

	var canceledAmount float64
	query := util.GetDB(ctx, serv.DB).
		Table("order_details od").
		Select("COALESCE(SUM(total_price), 0) AS amount").
		Joins("JOIN orders o ON od.order_id = o.id").
		Where("o.merchant_no =? AND od.merchant_no = ? AND od.state = ?", merchantNo, merchantNo, consts.ORDER_DETAIL_STATE_CANCEL).
		Where("o.created_at BETWEEN ? AND ?", startAt, endAt)

	if cashierID > 0 {
		query = query.Where("o.user_id =?", cashierID)
	}
	return canceledAmount, query.Scan(&canceledAmount).Error
}

// 获取支付方式统计数据
func (serv *BusinessStatisticService) getOrderProportions(ctx context.Context, merchantNo string, cashierID int64, startAt, endAt time.Time, giveChange float64) ([]*schema.BusinessOrderProportionItem, error) {
	var orderProportionItems []*schema.BusinessOrderProportionItem
	query := util.GetDB(ctx, serv.DB).
		Table("merchant_payments p").
		Select("COALESCE(SUM(p.amount / 100),0) as amount, COALESCE(SUM((select COALESCE(SUM(r.amount / 100), 0) from merchant_refund_logs r where payment_no = p.payment_no)),0) as refund_amount, "+
			"COUNT(p.order_no) as order_count, p.payment_type_id, pt.name_zh, pt.name_ug").
		Joins("LEFT JOIN orders o ON p.order_id = o.id").
		Joins("LEFT JOIN payment_types pt ON p.payment_type_id = pt.id").
		Where("o.merchant_no = ? AND o.paid_at BETWEEN ? AND ?", merchantNo, startAt, endAt).
		Where("o.state = ?", consts.ORDER_STATE_PAID).
		Where("p.merchant_no = ? AND p.status = ?", merchantNo, consts.PAY_STATUS_PAID)

	if cashierID > 0 {
		query = query.Where("o.cashier_id = ?", cashierID)
	}

	err := query.Group("p.payment_type_id").Scan(&orderProportionItems).Error

	total := &schema.BusinessOrderProportionItem{
		Amount:        0,
		RefundAmount:  0,
		OrderCount:    0,
		PaymentTypeID: 0,
		PayTypeName:   "总计",
		NameZh:        "总计",
		NameUg:        "جەمئىي",
	}

	isUg := !i18n.IsZh(&ctx)

	if isUg {
		total.PayTypeName = total.NameUg
	}

	data := []*schema.BusinessOrderProportionItem{total}

	hasCashInfo := false

	for _, item := range orderProportionItems {
		if !isUg {
			item.PayTypeName = item.NameUg
		} else {
			item.PayTypeName = item.NameZh
		}
		// 如果是现金，则减去找零金额
		if item.PaymentTypeID == consts.PAY_TYPE_CASH {
			item.Amount = util.SubtractFloat(item.Amount, giveChange)
			hasCashInfo = true
		}
		item.RealReceived = util.SubtractFloat(item.Amount, item.RefundAmount)
		total.Amount = util.AddFloat(total.Amount, item.Amount)
		total.RealReceived = util.AddFloat(total.RealReceived, item.RealReceived)
		total.RefundAmount = util.AddFloat(total.RefundAmount, item.RefundAmount)
		total.OrderCount += item.OrderCount
		data = append(data, item)
	}

	// 如果没有现金支付记录，则新增一个记录
	if !hasCashInfo {
		paymentTypeCash := &model.PaymentTypeModel{}
		ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Table("payment_types").
			Where("id = ?", consts.PAY_TYPE_CASH), paymentTypeCash)
		if err != nil {
			return nil, err
		}
		if !ok {
			return nil, errors.BadRequest("1001", "PaymentTypeNotFound")
		}
		cashItem := &schema.BusinessOrderProportionItem{
			Amount:        util.SubtractFloat(0.00, giveChange),
			RefundAmount:  0,
			OrderCount:    0,
			PaymentTypeID: consts.PAY_TYPE_CASH,
			PayTypeName:   paymentTypeCash.NameZh,
			NameZh:        paymentTypeCash.NameZh,
			NameUg:        paymentTypeCash.NameUg,
		}
		cashItem.RealReceived = util.SubtractFloat(cashItem.Amount, cashItem.RefundAmount)
		total.Amount = util.AddFloat(total.Amount, cashItem.Amount)
		total.RealReceived = util.AddFloat(total.RealReceived, cashItem.Amount)
		total.RefundAmount = util.AddFloat(total.RefundAmount, cashItem.RefundAmount)
		total.OrderCount += cashItem.OrderCount
		data = append(data, cashItem)

	}

	return data, err
}

// getVipStatistics 获取会员统计数据
func (serv *BusinessStatisticService) getVipStatistics(ctx context.Context, merchantNo string, cashierID int64, startAt, endAt time.Time) (*schema.BusinessVipStatistics, error) {
	vipCount, err := serv.getNewCustomerCount(ctx, merchantNo, cashierID, startAt, endAt)
	if err != nil {
		return nil, err
	}
	rechargeAmounts, err := serv.getCustomerRechargeAmount(ctx, merchantNo, cashierID, startAt, endAt)
	if err != nil {
		return nil, err
	}
	rechargeAmount := 0.00
	presentAmount := 0.00

	isUg := !i18n.IsZh(&ctx)

	for _, amount := range rechargeAmounts {
		if isUg {
			amount.PayTypeName = amount.NameUg
		} else {
			amount.PayTypeName = amount.NameZh
		}

		rechargeAmount = util.AddFloat(rechargeAmount, amount.Amount)
		presentAmount = util.AddFloat(presentAmount, amount.PresentAmount)

		amount.Amount = util.Round(amount.Amount)
		amount.PresentAmount = util.Round(amount.PresentAmount)
	}

	data := schema.BusinessVipStatistics{
		VipCount:        vipCount,
		VipRecharge:     util.Round(rechargeAmount),
		VipPresent:      util.Round(presentAmount),
		VipTotalBalance: util.Round(util.AddFloat(rechargeAmount, presentAmount)),
		VipProportion:   rechargeAmounts,
	}

	return &data, nil
}

// getDebtStatistics 获取赊账还款统计数据
func (serv *BusinessStatisticService) getDebtStatistics(ctx context.Context, merchantNo string, cashierID int64, startAt, endAt time.Time) (*schema.BusinessDebtStatistics, error) {
	holderCount, err := serv.getNewDebtHolderCount(ctx, merchantNo, cashierID, startAt, endAt)
	if err != nil {
		return nil, err
	}
	repaymentAmounts, err := serv.getDebtRepaymentAmount(ctx, merchantNo, cashierID, startAt, endAt)
	if err != nil {
		return nil, err
	}
	repaymentAmount := 0.00

	isUg := !i18n.IsZh(&ctx)

	for _, amount := range repaymentAmounts {

		if isUg {
			amount.PayTypeName = amount.NameUg
		} else {
			amount.PayTypeName = amount.NameZh
		}

		repaymentAmount = util.AddFloat(repaymentAmount, amount.Amount)

		amount.Amount = util.Round(amount.Amount)
	}

	data := schema.BusinessDebtStatistics{
		HolderCount:     holderCount,
		RepaymentAmount: util.Round(repaymentAmount),
		DebtProportion:  repaymentAmounts,
	}

	return &data, nil
}

// getNewCustomerCount 获取新增客户数量
func (serv *BusinessStatisticService) getNewCustomerCount(ctx context.Context, merchantNo string, cashierID int64, startAt, endAt time.Time) (int64, error) {
	var count int64
	query := util.GetDB(ctx, serv.DB).
		Model(&model.CustomerModel{}).
		Where("merchant_no = ? AND created_at BETWEEN ? AND ?", merchantNo, startAt, endAt)
	if cashierID > 0 {
		query = query.Where("cashier_id = ?", cashierID)
	}
	err := query.Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, err
}

// getCustomerRechargeAmount 获取充值金额
func (serv *BusinessStatisticService) getCustomerRechargeAmount(ctx context.Context, merchantNo string, cashierID int64, startAt, endAt time.Time) ([]*schema.BusinessVipRechargeAmount, error) {
	var amounts []*schema.BusinessVipRechargeAmount
	query := util.GetDB(ctx, serv.DB).
		Model(&model.CustomerConsumptionLogModel{}).
		Select("SUM(amount) AS amount, SUM(present_amount) AS present_amount, payment_type_id, pt.name_zh, pt.name_ug").
		Joins("JOIN payment_types pt ON customer_consumption_logs.payment_type_id = pt.id").
		Where("customer_consumption_logs.type = ?", model.ConsumptionTypeRecharge).
		Where("merchant_no = ? AND customer_consumption_logs.created_at BETWEEN ? AND ?", merchantNo, startAt, endAt).
		Group("payment_type_id")
	if cashierID > 0 {
		query = query.Where("cashier_id = ?", cashierID)
	}
	err := query.Scan(&amounts).Error
	if err != nil {
		return nil, err
	}
	if amounts == nil {
		amounts = []*schema.BusinessVipRechargeAmount{}
	}
	return amounts, err
}

// getNewCustomerCount 获取还款用户数量
func (serv *BusinessStatisticService) getDebtRepaymentHolderCount(ctx context.Context, merchantNo string, cashierID int64, startAt, endAt time.Time) (int64, error) {
	var count int64
	query := util.GetDB(ctx, serv.DB).
		Model(&model.DebtTransactionModel{}).
		Select("COUNT(DISTINCT debt_transactions.holder_id) AS count").
		Where("merchant_no = ? AND type = ? AND created_at BETWEEN ? AND ?", merchantNo, model.DebtTransactionTypeRepayment, startAt, endAt)
	if cashierID > 0 {
		query = query.Where("cashier_id = ?", cashierID)
	}
	err := query.Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, err
}

// getNewCustomerCount 获取新增客户数量
func (serv *BusinessStatisticService) getNewDebtHolderCount(ctx context.Context, merchantNo string, cashierID int64, startAt, endAt time.Time) (int64, error) {
	var count int64
	query := util.GetDB(ctx, serv.DB).
		Model(&model.DebtHolderModel{}).
		Select("COUNT(1) AS count").
		Where("merchant_no = ? AND created_at BETWEEN ? AND ?", merchantNo, startAt, endAt)
	if cashierID > 0 {
		query = query.Where("created_by = ?", cashierID)
	}
	err := query.Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, err
}

// getDebtRepaymentAmount 获取还款金额
func (serv *BusinessStatisticService) getDebtRepaymentAmount(ctx context.Context, merchantNo string, cashierID int64, startAt, endAt time.Time) ([]*schema.BusinessDebtRepaymentAmount, error) {
	var amounts []*schema.BusinessDebtRepaymentAmount
	query := util.GetDB(ctx, serv.DB).
		Model(&model.DebtTransactionModel{}).
		Select("SUM(amount * -1 / 100) AS amount, payment_type_id, pt.name_zh, pt.name_ug").
		Joins("JOIN payment_types pt ON debt_transactions.payment_type_id = pt.id").
		Where("merchant_no = ? AND debt_transactions.type = ? AND debt_transactions.created_at BETWEEN ? AND ?", merchantNo, model.DebtTransactionTypeRepayment, startAt, endAt).
		Group("payment_type_id")
	if cashierID > 0 {
		query = query.Where("cashier_id = ?", cashierID)
	}
	err := query.Scan(&amounts).Error
	if err != nil {
		return nil, err
	}
	if amounts == nil {
		amounts = []*schema.BusinessDebtRepaymentAmount{}
	}
	return amounts, err
}

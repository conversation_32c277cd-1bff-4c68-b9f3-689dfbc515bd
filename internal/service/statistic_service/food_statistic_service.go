package statistic_service

import (
	"context"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/collect"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
	"time"

	"gorm.io/gorm"
)

type FoodsStatisticService struct {
	DB *gorm.DB
}

type CategoryProportion struct {
	CategoryID     int64   `json:"category_id"`
	CategoryName   string  `json:"category_name"`
	CategoryNameZh string  `json:"category_name_zh"`
	CategoryNameUg string  `json:"category_name_ug"`
	Amount         float64 `json:"amount"`
}
type CategoryData struct {
	CategoryID     int64             `json:"category_id"`
	CategoryName   string            `json:"category_name"`
	CategoryNameZh string            `json:"category_name_zh"`
	CategoryNameUg string            `json:"category_name_ug"`
	FoodsList      []*DetailListItem `json:"foods_list"`
}

type DetailListItem struct {
	FoodID         int64   `json:"food_id"`
	Name           string  `json:"name"`
	NameUg         string  `json:"name_ug"`
	NameZh         string  `json:"name_zh"`
	Price          float64 `json:"price"`
	Count          float64 `json:"count"`
	RealAmount     float64 `json:"real_amount"`
	CostAmount     float64 `json:"cost_amount"`
	DiscountAmount float64 `json:"discount_amount"`
	DipositAmount  float64 `json:"diposit_amount"`
}

type FoodsStatisticResource struct {
	BeginAt             *string               `json:"begin_at"`
	EndAt               *string               `json:"end_at"`
	CategoryProportions []*CategoryProportion `json:"food_category_proportion"`
	CategoryList        []*CategoryData       `json:"list"`
}

func (serv *FoodsStatisticService) GetStatistics(ctx context.Context, merchantNo string, startAt, endAt time.Time) (*FoodsStatisticResource, error) {

	details, err := serv.getDetailStatistics(ctx, merchantNo, startAt, endAt)
	if err != nil {
		return nil, err
	}

	detailGroup := collect.GroupBy(details, func(detail *model.OrderDetailStatisticsModel) int64 {
		return detail.Food.FoodCategoryID
	})

	var categories []*model.FoodCategoryModel
	err = util.GetDB(ctx, serv.DB).Find(&categories, "merchant_no = ?", merchantNo).Error
	if err != nil {
		return nil, err
	}
	isUg := !i18n.IsZh(&ctx)

	CategoryProportions := make([]*CategoryProportion, 0, len(categories))
	CategoryList := make([]*CategoryData, 0, len(categories))
	categorySummary := &CategoryProportion{
		CategoryID:     0,
		CategoryName:   "总计",
		CategoryNameZh: "总计",
		CategoryNameUg: "جەمئىي",
	}
	if isUg {
		categorySummary.CategoryName = categorySummary.CategoryNameUg
	}
	CategoryProportions = append(CategoryProportions, categorySummary)
	categoryTotalAmount := 0.00

	for _, category := range categories {
		categoryProportion := CategoryProportion{
			CategoryID:     category.ID,
			CategoryName:   category.NameZh,
			CategoryNameZh: category.NameZh,
			CategoryNameUg: category.NameUg,
		}

		detailItems := detailGroup[category.ID]

		categoryData := CategoryData{
			CategoryID:     category.ID,
			CategoryName:   category.NameZh,
			CategoryNameZh: category.NameZh,
			CategoryNameUg: category.NameUg,
			FoodsList:      make([]*DetailListItem, 0, len(detailItems)),
		}
		totalAmount := 0.00
		summaryItem := DetailListItem{
			Name:           "总计",
			NameUg:         "جەمئىي",
			NameZh:         "总计",
			Price:          0.00,
			Count:          0.00,
			RealAmount:     0.00,
			CostAmount:     0.00,
			DiscountAmount: 0.00,
			DipositAmount:  0.00,
		}
		if isUg {
			summaryItem.Name = summaryItem.NameUg
			categoryProportion.CategoryName = categoryProportion.CategoryNameUg
			categoryData.CategoryName = categoryData.CategoryNameUg
		}
		categoryData.FoodsList = append(categoryData.FoodsList, &summaryItem)
		for _, item := range detailItems {
			detailItem := DetailListItem{
				FoodID:         item.FoodID,
				Name:           item.Food.NameZh,
				NameUg:         item.Food.NameUg,
				NameZh:         item.Food.NameZh,
				Price:          item.Food.Price,
				Count:          item.Count,
				RealAmount:     item.RealAmount,
				CostAmount:     item.CostAmount,
				DiscountAmount: item.DiscountAmount,
				DipositAmount:  item.DipositAmount,
			}
			if isUg {
				detailItem.Name = detailItem.NameUg
			}
			summaryItem.Price = util.AddFloat(summaryItem.Price, util.MultiplyFloat(detailItem.Price, detailItem.Count))
			summaryItem.Count += detailItem.Count
			summaryItem.RealAmount = util.AddFloat(summaryItem.RealAmount, detailItem.RealAmount)
			summaryItem.CostAmount = util.AddFloat(summaryItem.CostAmount, detailItem.CostAmount)
			summaryItem.DiscountAmount = util.AddFloat(summaryItem.DiscountAmount, detailItem.DiscountAmount)
			summaryItem.DipositAmount = util.AddFloat(summaryItem.DipositAmount, detailItem.DipositAmount)

			totalAmount = util.AddFloat(totalAmount, detailItem.RealAmount)
			categoryData.FoodsList = append(categoryData.FoodsList, &detailItem)
		}
		categoryProportion.Amount = util.Round(totalAmount)
		CategoryProportions = append(CategoryProportions, &categoryProportion)
		CategoryList = append(CategoryList, &categoryData)
		categoryTotalAmount = util.AddFloat(categoryTotalAmount, categoryProportion.Amount)
	}
	categorySummary.Amount = util.Round(categoryTotalAmount)

	return &FoodsStatisticResource{
		BeginAt:             util.FormatDateTime(&startAt),
		EndAt:               util.FormatDateTime(&endAt),
		CategoryProportions: CategoryProportions,
		CategoryList:        CategoryList,
	}, nil
}

func (serv *FoodsStatisticService) getDetailStatistics(ctx context.Context, merchantNo string, startAt, endAt time.Time) ([]*model.OrderDetailStatisticsModel, error) {
	db := util.GetDB(ctx, serv.DB)

	// 使用JOIN直接关联查询订单和订单详情
	var orderDetails []*model.OrderDetailStatisticsModel
	err := db.Select("food_id, "+
		"SUM(order_details.foods_count) as count, "+
		"sum(order_details.total_price) as real_amount,"+
		"SUM(order_details.foods_count * order_details.cost_price) as cost_amount, "+
		"sum(order_details.foods_count * order_details.original_price - order_details.total_price) AS discount_amount, "+
		"(sum(order_details.total_price) - sum(order_details.foods_count * order_details.cost_price)) AS diposit_amount",
	).
		Preload("Food").
		Joins("INNER JOIN orders ON order_details.order_id = orders.id").
		Where("orders.merchant_no = ? AND orders.state = ?", merchantNo, consts.ORDER_STATE_PAID).
		Where("orders.paid_at BETWEEN ? AND ?", startAt, endAt).
		Where("order_details.state = ?", consts.ORDER_DETAIL_STATE_PAID).
		Having("SUM(order_details.foods_count) > 0").
		Group("food_id").
		Find(&orderDetails).Error

	return orderDetails, err
}

package statistic_service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
	"time"
)

type HandoverStatisticService struct {
	DB *gorm.DB
}

func (serv *HandoverStatisticService) GetHandoverLogs(ctx context.Context, merchantNo string, startAt, endAt time.Time, cashierID int64) ([]*model.HandoverLogModel, error) {

	var handoverLogs []*model.HandoverLogModel

	query := util.GetDB(ctx, serv.DB).
		Preload("Cashier").
		Preload("ShiftModel").
		Preload("HandoverDetails.PaymentType").
		Select("*, SUM(handover_logs.alternate_amount + handover_logs.working_balance + handover_logs.paid_amount) as submitted_amount").
		Where("merchant_no =?", merchantNo).
		Where("start_at BETWEEN ? AND ?", startAt, endAt).
		Where("leave_at BETWEEN ? AND ?", startAt, endAt)

	if cashierID > 0 {
		query = query.Where("user_id =?", cashierID)
	}
	return handoverLogs, query.Group("start_at").Find(&handoverLogs).Error
}

package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// RemarkCategoryService 备注分类业务逻辑层
type RemarkCategoryService struct {
	DB *gorm.DB
}

// GetRemarkCategories   获取商户备注分类列表
func (serv *RemarkCategoryService) GetRemarkCategories(ctx context.Context) ([]*model.RemarkCategoryModel, error) {
	var list []*model.RemarkCategoryModel
	return list, util.GetDB(ctx, serv.DB).Find(&list).Error
}

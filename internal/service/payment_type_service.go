package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// PaymentTypeService 区域业务逻辑
type PaymentTypeService struct {
	DB *gorm.DB
}

// GetMerchantPaymentTypes 返回商户的支付方式
func (serv *PaymentTypeService) GetMerchantPaymentTypes(ctx context.Context, merchantNo string) ([]*model.PaymentTypeModel, error) {
	var list []*model.PaymentTypeModel
	db := util.GetDB(ctx, serv.DB).
		Select(
			"payment_types.id",
			"payment_types.tag",
			"payment_types.name_ug",
			"payment_types.name_zh",
			"payment_types.icon",
			"payment_types.type",
			"merchant_payment_types.pay_method",
			"payment_types.online",
			"merchant_payment_types.sort as sort",
			"merchant_payment_types.state",
			"payment_types.is_sync",
			"payment_types.created_at",
			"payment_types.updated_at",
			"payment_types.deleted_at",
		)
	db = db.Joins("left join merchant_payment_types on merchant_payment_types.payment_type_id = payment_types.id").
		Where("payment_types.state = ?", model.PaymentTypeStateNormal).
		Where("merchant_payment_types.merchant_no = ?", merchantNo)
	return list, db.Find(&list).Error
}

// GetPaymentTypesForRecharge 返回商户用于充值/还款的支付方式
func (serv *PaymentTypeService) GetPaymentTypesForRecharge(ctx context.Context, merchantNo string) ([]*model.PaymentTypeModel, error) {
	var list []*model.PaymentTypeModel
	db := util.GetDB(ctx, serv.DB).
		Table("payment_types as pt").
		Select(
			"pt.id",
			"pt.tag",
			"pt.name_ug",
			"pt.name_zh",
			"pt.icon",
			"pt.type",
			"mpt.pay_method",
			"pt.online",
			"mpt.sort as sort",
			"mpt.state",
			"pt.is_sync",
			"pt.created_at",
			"pt.updated_at",
			"pt.deleted_at",
		)
	db = db.Joins("left join merchant_payment_types mpt on mpt.payment_type_id = pt.id").
		Where("pt.state = ? AND pt.id NOT IN ?", model.PaymentTypeStateNormal, []int64{consts.PAY_TYPE_VIPCARD, consts.PAY_TYPE_DEBT}).
		Where("pt.state = ?", model.PaymentTypeStateNormal).
		Where("mpt.merchant_no = ? AND mpt.state = ?", merchantNo, model.PaymentTypeStateNormal).
		Order("mpt.sort asc")
	return list, db.Find(&list).Error
}

// GetMerchantActivePaymentTypeByID 根据ID获取商户正常的支付方式
func (serv *PaymentTypeService) GetMerchantActivePaymentTypeByID(ctx context.Context, merchantNo string, id int64) (*model.PaymentTypeModel, error) {
	var paymentType model.PaymentTypeModel
	db := util.GetDB(ctx, serv.DB).
		Select(
			"payment_types.id",
			"payment_types.tag",
			"payment_types.name_ug",
			"payment_types.name_zh",
			"payment_types.icon",
			"payment_types.type",
			"merchant_payment_types.pay_method",
			"payment_types.online",
			"merchant_payment_types.sort as sort",
			"merchant_payment_types.state",
			"payment_types.is_sync",
			"payment_types.created_at",
			"payment_types.updated_at",
			"payment_types.deleted_at",
		)
	db = db.Joins("left join merchant_payment_types on merchant_payment_types.payment_type_id = payment_types.id").
		Where("payment_types.state = ?", model.PaymentTypeStateNormal).
		Where("merchant_payment_types.state = ?", model.PaymentTypeStateNormal).
		Where("merchant_payment_types.merchant_no = ?", merchantNo).
		Where("payment_types.id = ?", id)

	ok, err := util.FindOne(ctx, db, &paymentType)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &paymentType, nil
}

// GetPaymentTypeByID 根据ID获取支付方式
func (serv *PaymentTypeService) GetPaymentTypeByID(ctx context.Context, id int64) (*model.PaymentTypeModel, error) {
	var paymentType model.PaymentTypeModel
	db := util.GetDB(ctx, serv.DB).Where("payment_types.id = ?", id)
	ok, err := util.FindOne(ctx, db, &paymentType)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &paymentType, nil
}

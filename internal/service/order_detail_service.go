package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type OrderDetailService struct {
	DB *gorm.DB
}

func (serv *OrderDetailService) GetByID(ctx context.Context, detailID int64) (*model.OrderDetailModel, error) {
	var detail model.OrderDetailModel
	query := util.GetDB(ctx, serv.DB).Preload("Food").Where("id", detailID)
	ok, err := util.FindOne(ctx, query, &detail)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &detail, nil
}
func (serv *OrderDetailService) GetByIDs(ctx context.Context, orderID int64, detailIds []int64) ([]*model.OrderDetailModel, error) {
	var details []*model.OrderDetailModel
	return details, util.GetDB(ctx, serv.DB).
		Preload("Food").
		Where("order_id=?", orderID).
		Where("id in (?)", detailIds).Find(&details).Error
}

// UpdateForRefund 退款更新订单详情
func (serv *OrderDetailService) UpdateForRefund(ctx context.Context, id int64, refundCount, refundAmount float64) error {
	return util.GetDB(ctx, serv.DB).Model(&model.OrderDetailModel{}).
		Where("id=?", id).
		Updates(map[string]interface{}{
			"foods_count": gorm.Expr("foods_count -?", refundCount),
			"total_price": gorm.Expr("total_price -?", refundAmount),
		}).Error
}

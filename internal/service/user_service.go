package service

import (
	"context"
	"ros-api-go/internal/model"
	"ros-api-go/internal/sms"
	"ros-api-go/pkg/cachex"
	"ros-api-go/pkg/crypto/hash"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// UserService 餐厅用户业务逻辑
type UserService struct {
	Trans *util.Trans
	Cache cachex.Cacher
	DB    *gorm.DB
}

// GetMerchantUsers 获取商户下的用户列表
func (serv *UserService) GetMerchantUsers(ctx context.Context, merchantNo string) ([]*model.UserModel, error) {
	var list []*model.UserModel
	err := util.GetDB(ctx, serv.DB).
		Select("users.id, users.avatar, users.phone, users.password, users.role_id, users.api_token, users.leaved_at, users.last_login, "+
			"me.merchant_no, me.no as job_no, me.name_ug, me.name_zh, me.is_owner, me.operation_password, me.state, me.created_at, me.updated_at, me.deleted_at").
		Joins("JOIN merchant_employees as me ON me.user_id = users.id where me.merchant_no =?", merchantNo).Find(&list).Error
	return list, err
}

// GetMerchantUsersWithPermissions 获取商户下的用户列表
func (serv *UserService) GetMerchantUsersWithPermissions(ctx context.Context, merchantNo string) ([]*model.UserModel, error) {
	var list []*model.UserModel
	err := util.GetDB(ctx, serv.DB).
		Select("users.id, users.avatar, users.phone, users.password, users.role_id, users.api_token, users.leaved_at, users.last_login, "+
			"me.merchant_no, me.no as job_no, me.name_ug, me.name_zh, me.is_owner, me.operation_password, me.state, me.created_at, me.updated_at, me.deleted_at").
		Joins("JOIN merchant_employees as me ON me.user_id = users.id where me.merchant_no =?", merchantNo).
		Preload("Employee", func(db *gorm.DB) *gorm.DB {
			return db.Select("id,user_id").Where("merchant_no =?", merchantNo).Preload("Roles")
		}).
		Find(&list).Error
	return list, err
}

// GetByPhone 根据手机号获取用户信息
func (serv *UserService) GetByPhone(ctx context.Context, phone string) (*model.UserModel, error) {
	var user model.UserModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("phone = ? ", phone), &user)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return &user, nil
}

// GetByUserID 根据用户ID获取用户信息
func (serv *UserService) GetByUserID(ctx context.Context, userID int64) (*model.UserModel, error) {
	user := new(model.UserModel)
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("id =?", userID), user)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return user, nil
}

// GetUserTokenWithEmployeeByUserID 根据用户ID获取用户信息
func (serv *UserService) GetUserTokenWithEmployeeByUserID(ctx context.Context, merchantNo string, clientType model.ClientType, userID int64) (*model.UserTokenModel, error) {
	user := new(model.UserTokenModel)
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("user_id = ? and client_type = ?", userID, clientType).
		Preload("Employee", func(db *gorm.DB) *gorm.DB {
			return db.Select("id,merchant_no,user_id,no,name_ug,name_zh,is_owner,state").
				Where("merchant_no =?", merchantNo)
		}), user)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return user, nil
}

// GetUserWithEmployeeByUserID 根据用户ID获取用户信息
func (serv *UserService) GetUserWithEmployeeByUserID(ctx context.Context, merchantNo string, userID int64) (*model.UserModel, error) {
	user := new(model.UserModel)
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Select("id, phone, password, api_token").
		Where("id = ?", userID).
		Preload("Employee", func(db *gorm.DB) *gorm.DB {
			return db.Select("id,merchant_no,user_id,no,name_ug,name_zh,is_owner,state").
				Where("merchant_no =?", merchantNo)
		}), user)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return user, nil
}

func (serv *UserService) MustGetByUserID(ctx context.Context, userID int64) (*model.UserModel, error) {
	user, err := serv.GetByUserID(ctx, userID)

	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.NotFound("", "UserNotFound")
	}
	return user, nil
}

// GetByUserIDWithEmployeeData 根据用户ID获取用户信息
func (serv *UserService) GetByUserIDWithEmployeeData(ctx context.Context, userID int64, merchantNo string) (*model.UserModel, error) {
	user := new(model.UserModel)
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Select("users.id, users.avatar, users.phone, users.password, users.role_id, users.api_token, users.leaved_at, users.last_login, "+
			"me.merchant_no, me.no as job_no, me.name_ug, me.name_zh, me.is_owner, me.operation_password, me.state, me.created_at, me.updated_at, me.deleted_at").
		Joins("JOIN merchant_employees me ON me.user_id = users.id").
		Where("users.id =? AND me.merchant_no =?", userID, merchantNo), &user)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return user, nil
}

// FindOrCreate 根据手机号创建用户
func (serv *UserService) FindOrCreate(ctx context.Context, phone string) (*model.UserModel, error) {
	user, err := serv.GetByPhone(ctx, phone)
	if err != nil {
		logging.
			Context(ctx).
			Error("failed to get user by phone",
				zap.String("phone", phone),
				zap.Error(err))
		return nil, err
	}
	if user != nil {
		logging.
			Context(ctx).
			Info("user already exists",
				zap.String("phone", phone))
		return user, nil
	}
	user = &model.UserModel{
		Phone: phone,
		State: model.UserStatusActivated,
	}
	// 随机6位数字生成
	passwd := util.RandomNumber(6)
	user.Password, err = hash.GeneratePassword(passwd)

	if err != nil {
		logging.Context(ctx).Error("failed to generate password", zap.Error(err))
		return nil, err
	}
	err = util.GetDB(ctx, serv.DB).Create(&user).Error
	logging.Context(ctx).Info("user created",
		zap.String("phone", phone),
		zap.String("password", passwd))
	if err != nil {
		logging.
			Context(ctx).
			Error("failed to create user",
				zap.String("phone", phone),
				zap.Error(err))
		return nil, err
	}
	go func() {
		if err := sms.SendUserPasswordSMS(user.Phone, passwd); err != nil {
			logging.Context(ctx).Error("failed to send user password sms", zap.Error(err))
		}
	}()
	return user, nil
}

// UpdatePassword 更新用户密码
func (serv *UserService) UpdatePassword(ctx context.Context, userID int64, password string) error {
	// 加密密码
	password, err := hash.GeneratePassword(password)
	if err != nil {
		logging.Context(ctx).Error("failed to generate password",
			zap.Int64("userID", userID),
			zap.Error(err))
		return err
	}
	return util.GetDB(ctx, serv.DB).Model(&model.UserModel{}).Where("id = ?", userID).Update("password", password).Error
}

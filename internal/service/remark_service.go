package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// RemarkService 备注信息业务逻辑
type RemarkService struct {
	DB *gorm.DB
}

// GetMerchantRemarks 获取商户备注信息列表
func (serv *RemarkService) GetMerchantRemarks(ctx context.Context, merchantNo string) ([]*model.RemarkModel, error) {
	var list []*model.RemarkModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

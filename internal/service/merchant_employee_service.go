package service

import (
	"context"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/crypto/hash"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type MerchantEmployeeService struct {
	Db    *gorm.DB
	Trans util.Trans
}

// FindByMerchantIdAndUserId 查找商户员工
func (serv *MerchantEmployeeService) FindByMerchantIdAndUserId(ctx context.Context, merchantNo string, userID int64) (*model.MerchantEmployeeModel, error) {
	var item model.MerchantEmployeeModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.Db).Where("merchant_no = ? and user_id = ?", merchantNo, userID), &item)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &item, nil
}

// List 获取商户员工列表
// 参数：
//   - ctx: 上下文
//   - merchantNo: 商户编号
//   - state: 状态
//   - page: 页码
//   - pageSize: 页大小
//
// 返回：
//   - []model.MerchantEmployeeModel: 商户员工列表
//   - int64 总记录数
//   - error: 错误信息
func (serv *MerchantEmployeeService) List(
	ctx context.Context,
	merchantNo string,
	state *int8,
	mobile string,
	page int,
	pageSize int,
) ([]model.MerchantEmployeeModel, int64, error) {
	var total int64
	query := util.GetDB(ctx, serv.Db).
		Model(&model.MerchantEmployeeModel{}).
		Where("merchant_no = ?", merchantNo)
	if state != nil {
		query = query.Where("state = ?", *state)
	} else {
		query = query.Where("state > ?", model.EmployeeStateDeleted)
	}
	if mobile != "" {
		query = query.Where("EXISTS (SELECT 1 FROM users where users.id = merchant_employees.user_id and users.phone like ?)", "%"+mobile+"%")
	}
	if err := query.
		Count(&total).Error; err != nil {
		logging.
			Context(ctx).
			Error("failed to count merchant stuff", zap.Error(err))
		return nil, 0, err
	}
	if total == 0 {
		return nil, 0, nil
	}
	var items []model.MerchantEmployeeModel

	if err := query.
		Preload("Roles", func(db *gorm.DB) *gorm.DB {
			return db.Where("state > ?", model.MerchantRoleStateDeleted)
		}).
		Preload("User").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&items).Error; err != nil {
		logging.
			Context(ctx).
			Error("failed to list merchant stuff", zap.Error(err))
		return nil, 0, err
	}
	return items, total, nil
}

// Create 创建商户员工
// 参数：
//   - ctx: 上下文
//   - merchantNo: 商户编号
//   - userID: 用户ID
//   - isOwner: 是否为店主
//   - state: 状态
//   - roleIds: 角色ID列表
//
// 返回：
//   - *model.MerchantEmployeeModel: 创建的商户员工信息
//   - error: 错误信息

func (serv *MerchantEmployeeService) Create(
	ctx context.Context,
	employeeData model.MerchantEmployeeModel,
	roleIds []int64,
) (*model.MerchantEmployeeModel, error) {
	err := serv.Trans.Exec(ctx, func(ctx context.Context) error {
		db := util.GetDB(ctx, serv.Db)
		var roles []model.MerchantRoleModel
		if len(roleIds) > 0 {
			err := db.Model(&model.MerchantRoleModel{}).
				Where("merchant_no = ?", employeeData.MerchantNo).
				Where("id IN ?", roleIds).
				Where("state > ?", model.MerchantRoleStateDeleted).
				Find(&roles).Error
			if err != nil {
				return err
			}
			if len(roles) == 0 {
				return errors.BadRequest("MerchantRoleNotFound", "MerchantRoleNotFound")
			}
		}
		err := db.Create(&employeeData).Error
		if err != nil {
			return err
		}
		return db.Model(&employeeData).
			Association("Roles").
			Append(&roles)
	})
	if err != nil {
		return nil, err
	}

	return &employeeData, nil
}

// Update 更新商户员工
// 参数：
//   - ctx: 上下文
//   - employee: 员工信息
//
// 返回：
//   - *model.MerchantEmployeeModel: 更新后的商户员工信息
//   - error: 错误信息

func (serv *MerchantEmployeeService) Update(ctx context.Context, employee model.MerchantEmployeeModel) (*model.MerchantEmployeeModel, error) {
	db := util.GetDB(ctx, serv.Db)

	if employee.State == model.EmployeeStateDeleted {
		return nil, errors.NotFound("EmployeeNotFound", "EmployeeNotFound")
	}

	if err := db.Save(&employee).Error; err != nil {
		return nil, err
	}

	return &employee, nil
}

// UpdateWithRoleIds 更新商户员工及其角色
// 参数：
//   - ctx: 上下文
//   - employee: 员工信息
//   - roleIds: 角色ID列表
//
// 返回：
//   - *model.MerchantEmployeeModel: 更新后的商户员工信息
//   - error: 错误信息
func (serv *MerchantEmployeeService) UpdateWithRoleIds(ctx context.Context, employee model.MerchantEmployeeModel, roleIds []int64) (*model.MerchantEmployeeModel, error) {
	db := util.GetDB(ctx, serv.Db)

	if employee.State == model.EmployeeStateDeleted {
		return nil, errors.NotFound("EmployeeNotFound", "EmployeeNotFound")
	}

	if err := db.Save(&employee).Error; err != nil {
		return nil, err
	}
	db.Model(&employee).Association("Roles").Clear()
	if len(roleIds) == 0 {
		return &employee, nil
	}
	var roles []model.MerchantRoleModel
	if err := db.Model(&model.MerchantRoleModel{}).
		Where("merchant_no = ?", employee.MerchantNo).
		Where("id IN ?", roleIds).
		Where("state > ?", model.MerchantRoleStateDeleted).
		Find(&roles).Error; err == nil {
		if len(roles) == 0 {
			return nil, errors.BadRequest("MerchantRoleNotFound", "MerchantRoleNotFound")
		}
		db.Model(&employee).
			Association("Roles").
			Replace(&roles)
	} else {
		logging.Context(ctx).Error(
			"查询商家角色失败",
			zap.String("merchant_no", employee.MerchantNo),
		)
	}
	return &employee, nil
}

// Delete 删除商户员工
// 参数：
//   - ctx: 上下文
//   - employee: 员工信息
//
// 返回：
//   - error: 错误信息

func (serv *MerchantEmployeeService) Delete(ctx context.Context, employee model.MerchantEmployeeModel) error {

	if employee.State == model.EmployeeStateDeleted {
		return errors.NotFound("EmployeeNotFound", "EmployeeNotFound")
	}
	return util.GetDB(ctx, serv.Db).
		Model(&model.MerchantEmployeeModel{}).
		Where("id = ?", employee.ID).
		Updates(map[string]interface{}{
			"state":      model.EmployeeStateDeleted,
			"deleted_at": time.Now(),
		}).Error
}

// Get 获取商户员工
// 参数：
//   - ctx: 上下文
//   - ID: 员工ID
//
// 返回：
//   - *model.MerchantEmployeeModel: 商户员工信息
//   - error: 错误信息
func (serv *MerchantEmployeeService) Get(ctx context.Context, id int64) (*model.MerchantEmployeeModel, error) {
	var item model.MerchantEmployeeModel
	err := util.GetDB(ctx, serv.Db).
		Find(&item, id).Error
	if err != nil {
		logging.
			Context(ctx).
			Error(
				"failed to get merchant stuff",
				zap.Error(err),
				zap.Int64("employee_id", id),
			)
		return nil, errors.NotFound("EmployeeNotFound", "EmployeeNotFound")
	}
	return &item, nil
}

// GetWithRoles 获取商户员工及其角色
// 参数：
//   - ctx: 上下文
//   - ID: 员工ID
//
// 返回：
//   - *model.MerchantEmployeeModel: 商户员工信息
//   - error: 错误信息
func (serv *MerchantEmployeeService) GetWithRolesAndUser(ctx context.Context, id int64) (*model.MerchantEmployeeModel, error) {
	var employee model.MerchantEmployeeModel
	err := util.GetDB(ctx, serv.Db).
		Preload("Roles").
		Preload("User").
		Find(&employee, id).Error
	if err != nil {
		return nil, errors.NotFound("EmployeeNotFound", "EmployeeNotFound")
	}
	return &employee, nil
}

// GetByUserID 获取商户员工
// 参数：
//   - ctx: 上下文
//   - merchantNo: 商户编号
//   - userID: 用户ID
//
// 返回：
//   - *model.MerchantEmployeeModel: 商户员工信息
//   - error: 错误信息
func (serv *MerchantEmployeeService) GetByUserID(ctx context.Context, merchantNo string, userID int64) (*model.MerchantEmployeeModel, error) {
	var employee model.MerchantEmployeeModel
	err := util.GetDB(ctx, serv.Db).
		Where("merchant_no = ?", merchantNo).
		Where("user_id = ?", userID).
		First(&employee).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return &employee, nil
}

// MustGetByUserID 获取商户员工(获取正常的，如没有直接报错)
func (serv *MerchantEmployeeService) MustGetByUserID(ctx context.Context, merchantNo string, userID int64) (*model.MerchantEmployeeModel, error) {
	employee, err := serv.GetByUserID(ctx, merchantNo, userID)
	if err != nil {
		return nil, err
	}
	if employee == nil {
		return nil, errors.NotFound("EmployeeNotFound", "EmployeeNotFound")
	}
	if employee.State == model.EmployeeStateDeleted {
		return nil, errors.NotFound("EmployeeNotFound", "EmployeeNotFound")
	}
	if employee.State == model.EmployeeStateInactive {
		return nil, errors.Forbidden("EmployeeForbidden", "EmployeeForbidden")
	}
	return employee, nil
}

// GetMerchantEmployeeByNo 获取商户员工
// 参数：
//   - ctx: 上下文
//   - merchantNo: 商户编号
//   - no: 员工编号
//
// 返回：
//   - *model.MerchantEmployeeModel: 商户员工信息
//   - error: 错误信息
func (serv *MerchantEmployeeService) GetMerchantEmployeeByNo(ctx context.Context, merchantNo string, no string) (*model.MerchantEmployeeModel, error) {
	var employee *model.MerchantEmployeeModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.Db).Where("merchant_no = ? AND no = ?", merchantNo, no), &employee)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return employee, nil
}

// Enable 启用商户角色
func (serv *MerchantEmployeeService) Enable(ctx context.Context, merchantNo string, employeeId int64) error {
	return util.GetDB(ctx, serv.Db).
		Model(&model.MerchantEmployeeModel{}).
		Where("merchant_no = ? AND id = ?", merchantNo, employeeId).
		Updates(map[string]interface{}{
			"state": model.EmployeeStateActive,
		}).Error
}

// Disable 禁用商户角色
func (serv *MerchantEmployeeService) Disable(ctx context.Context, merchantNo string, employeeId int64) error {
	return util.GetDB(ctx, serv.Db).
		Model(&model.MerchantEmployeeModel{}).
		Where("merchant_no = ? AND id = ?", merchantNo, employeeId).
		Updates(map[string]interface{}{
			"state": model.EmployeeStateInactive,
		}).Error
}

// SetOperationPassword 设置操作密码
func (serv *MerchantEmployeeService) SetOperationPassword(ctx context.Context, employeeId int64, merchantNo string, password string) error {
	password, err := hash.GeneratePassword(password)
	if err != nil {
		return err
	}
	data := model.MerchantEmployeeModel{
		ID:                employeeId,
		MerchantNo:        merchantNo,
		OperationPassword: password,
	}
	return util.GetDB(ctx, serv.Db).
		Model(&model.MerchantEmployeeModel{}).
		Where("merchant_no = ? AND id = ?", merchantNo, employeeId).
		Select("operation_password").
		Updates(data).Error
}

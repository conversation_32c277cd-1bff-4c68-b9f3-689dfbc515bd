package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type ShiftService struct {
	DB *gorm.DB
}

// GetByID 根据ID获取ShiftModel
func (s *ShiftService) GetByID(ctx context.Context, id int64) (*model.ShiftModel, error) {
	var shift model.ShiftModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, s.DB).Where("id=?", id), &shift)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &shift, nil
}

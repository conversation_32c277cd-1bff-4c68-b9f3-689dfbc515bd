package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// TerminalService handles terminal-related business logic
type TerminalService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// GetActiveTerminals retrieves all active terminals
func (serv *TerminalService) GetActiveTerminals(ctx context.Context) ([]*model.TerminalModel, error) {
	var terminals []*model.TerminalModel

	err := util.GetDB(ctx, serv.DB).
		Where("state = ?", model.TerminalStateActivated).
		Find(&terminals).Error

	if err != nil {
		return nil, err
	}

	return terminals, nil
}

// GetTerminalByID retrieves a terminal by its ID
func (serv *TerminalService) GetTerminalByID(ctx context.Context, id uint) (*model.TerminalModel, error) {
	var terminal model.TerminalModel

	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("id = ?", id), &terminal)
	if err != nil {
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &terminal, nil
}

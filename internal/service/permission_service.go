package service

import (
	"context"
	"gorm.io/gorm"
	"path/filepath"
	"ros-api-go/internal/config"
	"ros-api-go/internal/http"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// PermissionService 权限业务逻辑
type PermissionService struct {
	Trans   *util.Trans
	DB      *gorm.DB
	Casbinx *http.Casbinx
}

// GetAllPermissions 获取所有权限
func (serv *PermissionService) GetAllPermissions(ctx context.Context) ([]*model.Permission, error) {
	fullPath := filepath.Join(config.C.General.WorkDir, "configs/permissions.json")
	permissions, err := serv.Casbinx.InitPermissionsFromFile(ctx, fullPath)
	if err != nil {
		return nil, err
	}
	return permissions, nil
}

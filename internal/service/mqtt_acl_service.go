package service

import (
	"context"
	"fmt"
	"github.com/golang-jwt/jwt"
	"ros-api-go/internal/config"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"strconv"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type MqttAclService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// CreateClientSubscribePermission 创建客户端的MQTT订阅权限。
// 该函数接收商户号和本地服务器ID作为输入参数，返回一个布尔值表示操作是否成功以及可能发生的错误。
// 函数会检查mqtt_acl表中是否已存在指定服务器ID和主题的权限记录。如果记录存在，返回true。
// 如果不存在，则创建新的权限记录，允许服务器订阅其RPC请求主题。
func (serv *MqttAclService) CreateClientSubscribePermission(ctx context.Context, merchantNo string,
	localServerId int64) (bool, error) {
	// 查询 mqtt_acl 表记录是否存在
	var count int64
	// 构造RPC请求主题格式: rpc/{商户号}/{主收银ID}/request
	topic := fmt.Sprintf("rpc/%s/%d/request", merchantNo, localServerId)
	err := util.GetDB(ctx, serv.DB).Model(&model.MqttAcl{}).
		Where("username = ?", localServerId).
		Where("topic = ?", topic).
		Count(&count).Error

	if err != nil {
		logging.Context(ctx).Error(
			"查询mqtt_acl记录失败",
			zap.String("merchant_no", merchantNo),
			zap.Int64("local_server_id", localServerId),
			zap.String("topic", topic),
		)
		return false, errors.BadRequest("", "QuerySubscribePermissionFailed")
	}

	// 如果记录已存在，返回true
	if count > 0 {
		return true, nil
	}
	// 如果记录不存在，创建新记录
	mqttAcl := &model.MqttAcl{
		MerchantNo: merchantNo,                           // 商户号
		Username:   strconv.FormatInt(localServerId, 10), // 用户名(服务器ID)
		Topic:      topic,                                // 主题
		Permission: "allow",                              // 权限:允许
		Action:     "subscribe",                          // 动作:订阅
		Qos:        1,                                    // 服务质量
		Retain:     nil,                                  // 保留消息标志
	}

	if err := util.GetDB(ctx, serv.DB).Create(mqttAcl).Error; err != nil {
		logging.Context(ctx).Error("创建订阅权限失败",
			zap.String("merchant_no", merchantNo),
			zap.Int64("username", localServerId),
			zap.String("topic", topic),
		)
		return false, errors.BadRequest("", "CreatedSubscribePermissionFailed")
	}

	return true, nil
}

// CreateClientPublishPermission 创建客户端的MQTT发布权限。
// 该函数接收商户号和客户端ID作为输入参数，返回一个布尔值表示操作是否成功以及可能发生的错误。
// 函数会检查mqtt_acl表中是否已存在指定客户端ID和主题的权限记录。如果记录存在，返回true。
// 如果不存在，则创建新的权限记录，允许客户端向其RPC响应主题发布消息。
func (serv *MqttAclService) CreateClientPublishPermission(
	ctx context.Context,
	merchantNo string,
	clientId int64,
) (bool, error) {
	// 查询 mqtt_acl 表记录是否存在
	var count int64
	topic := fmt.Sprintf("rpc/response/#")
	err := util.GetDB(ctx, serv.DB).Model(&model.MqttAcl{}).
		Where("username = ?", clientId).
		Where("topic = ?", topic).
		Count(&count).Error

	if err != nil {
		logging.Context(ctx).Error("查询mqtt_acl记录失败",
			zap.String("merchant_no", merchantNo),
			zap.Int64("client_id", clientId),
			zap.String("topic", topic),
		)
		return false, errors.BadRequest("", "QueryPublishPermissionFailed")
	}

	// 如果记录已存在，返回true
	if count > 0 {
		return true, nil
	}

	// 如果记录不存在，创建新记录
	mqttAcl := &model.MqttAcl{
		MerchantNo: merchantNo,
		Username:   strconv.FormatInt(clientId, 10),
		Topic:      topic,
		Permission: "allow",
		Action:     "publish",
		Qos:        1,
		Retain:     nil,
	}

	if err := util.GetDB(ctx, serv.DB).Create(mqttAcl).Error; err != nil {
		return false, errors.BadRequest("", "CreatePublishPermissionFailed")
	}

	return true, nil
}

// GetClientMqttAuthInfo 生成并返回客户端的MQTT认证信息。
// 该方法接收一个上下文和客户端ID作为参数，
// 并返回一个包含JWT token字符串和过期时间的元组，以及可能出现的错误。
//
// ctx: 上下文，用于日志记录和错误处理。
// clientId: 请求认证信息的客户端ID。
// 返回值:
// - string: 生成的JWT token字符串。
// - int64: JWT token的过期时间戳。
// - error: 可能发生的错误，如果生成token失败，则返回相应的错误。
func (serv *MqttAclService) GetClientMqttAuthInfo(ctx context.Context, clientId int64) (string, int64, error) {
	// 计算JWT token的过期时间。
	exp := time.Now().Add(time.Duration(config.C.MQTT.JwtExp) * time.Second).Unix()

	// 创建一个包含客户端ID、用户名和过期时间的JWT token。
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"clientid": fmt.Sprintf("%d", clientId),
		"username": fmt.Sprintf("%d", clientId),
		"exp":      exp,
	})

	// 使用密钥对token进行签名并生成token字符串。
	tokenString, err := token.SignedString([]byte(config.C.MQTT.JwtKey))
	if err != nil {
		// 如果生成token失败，记录错误日志并返回错误。
		logging.Context(ctx).Error("生成客户端MQTT token失败", zap.Error(err))
		return "", 0, errors.BadRequest("", "CreateTokenFailed")
	}

	// 返回生成的token字符串和过期时间戳。
	return tokenString, exp, nil
}

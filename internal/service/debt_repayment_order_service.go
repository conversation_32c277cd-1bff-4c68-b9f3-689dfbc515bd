package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
	"time"
)

type DebtRepaymentOrderService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// Create 创建还款订单
func (serv *DebtRepaymentOrderService) Create(ctx context.Context, order *model.DebtRepaymentOrderModel) error {
	return util.GetDB(ctx, serv.DB).Create(order).Error
}

// GetByID 根据ID获取还款订单
func (serv *DebtRepaymentOrderService) GetByID(ctx context.Context, merchantNo string, id int64) (*model.DebtRepaymentOrderModel, error) {
	var order model.DebtRepaymentOrderModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("merchant_no = ? AND id = ?", merchantNo, id), &order)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &order, err
}

// UpdateStatus 更新订单状态
func (serv *DebtRepaymentOrderService) UpdateStatus(ctx context.Context, id int64, paymentTypeID int64, status int64) error {
	return util.GetDB(ctx, serv.DB).Model(&model.DebtRepaymentOrderModel{}).
		Where("id = ?", id).
		Updates(map[string]any{
			"payment_type_id": paymentTypeID,
			"status":          status,
		}).Error
}

// ListByHolderID 根据赊账人ID获取还款订单列表
func (serv *DebtRepaymentOrderService) ListByHolderID(ctx context.Context, merchantNo string, holderID int64) ([]*model.DebtRepaymentOrderModel, error) {
	var orders []*model.DebtRepaymentOrderModel
	err := util.GetDB(ctx, serv.DB).Where("merchant_no = ? AND customer_id = ?", merchantNo, holderID).Find(&orders).Error
	return orders, err
}

// GetByOrderNo 根据订单号获取还款订单
func (serv *DebtRepaymentOrderService) GetByOrderNo(ctx context.Context, merchantNo string, orderNo string) (*model.DebtRepaymentOrderModel, error) {
	var order model.DebtRepaymentOrderModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Preload("Holder").
		Where("merchant_no = ? AND no = ?", merchantNo, orderNo), &order)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &order, err
}

// ListForHandoverDetail 根据holderID获取赊账还款统计(用于交接班)
func (serv *DebtRepaymentOrderService) ListForHandoverDetail(ctx context.Context, cashierID int64, merchantNo string,
	startDate time.Time, endDate time.Time) ([]*schema.CashierDebtStatistics, error) {
	var orders []*schema.CashierDebtStatistics
	err := util.GetDB(ctx, serv.DB).
		Select("payment_type_id, SUM(amount) as amount, COUNT(id) as count, cashier_id").
		Model(&model.DebtRepaymentOrderModel{}).
		Where("merchant_no = ? AND status = ? AND cashier_id = ?", merchantNo, model.DebtRepaymentOrderStatusSuccess, cashierID).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Group("payment_type_id").
		Find(&orders).Error
	return orders, err
}

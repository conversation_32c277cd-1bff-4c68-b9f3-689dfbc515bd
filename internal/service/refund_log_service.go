package service

import (
	"context"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"

	"gorm.io/gorm"
)

// RefundLogService 退款记录服务
type RefundLogService struct {
	DB *gorm.DB
}

// CreateFromPaymentLog 创建退款记录(使用微信/支付宝支付记录)
func (serv *RefundLogService) CreateFromPaymentLog(ctx context.Context, merchant *model.MerchantModel, orderID, batchID int64, paymentInfo *model.MerchantPaymentLogModel, Amount int64, refundType int64) (*model.MerchantRefundLogModel, error) {
	reason := "撤销支付"
	if refundType == consts.REFUND_TYPE_ORDER {
		reason = "订单退菜"
	}
	refundLog := &model.MerchantRefundLogModel{
		RefundStatus:  consts.REFUND_STATUS_WAITING,
		PaymentLogID:  paymentInfo.ID,
		MerchantNo:    paymentInfo.MerchantNo,
		OrderID:       orderID,
		BatchID:       batchID,
		OrderNo:       paymentInfo.OrderNo,
		OrderType:     paymentInfo.OrderType,
		PaymentTypeID: paymentInfo.PaymentTypeID,
		PaymentNo:     paymentInfo.PaymentNo,
		OutTradeNo:    paymentInfo.OutTradeNo,
		TradeType:     paymentInfo.TradeType,
		PayAmount:     paymentInfo.Amount, // 支付金额
		RefundType:    refundType,
		Reason:        reason,
		Amount:        Amount, // 退款金额
		OutRefundNo:   util.SerialNumber(consts.RefundPaymentPrefix),
	}

	if paymentInfo.PaymentTypeID == consts.PAY_TYPE_WECHAT { // 微信支付
		refundLog.MchID = paymentInfo.MchID
		refundLog.SubMchID = paymentInfo.SubMchID
	}
	if paymentInfo.PaymentTypeID == consts.PAY_TYPE_ALIPAY { // 支付宝支付
		refundLog.MchID = paymentInfo.MchID
		refundLog.SubMchID = merchant.AlipaySubMchID
	}

	err := util.GetDB(ctx, serv.DB).Create(refundLog).Error
	return refundLog, err
}

// CreateFromPayment 创建退款记录
func (serv *RefundLogService) CreateFromPayment(ctx context.Context, merchant *model.MerchantModel, batchID int64, paymentInfo *model.MerchantPaymentModel, Amount int64, refundType int64) (*model.MerchantRefundLogModel, error) {
	reason := "撤销支付"
	if refundType == consts.REFUND_TYPE_ORDER {
		reason = "订单退菜"
	}
	refundLog := &model.MerchantRefundLogModel{
		PaymentLogID:  paymentInfo.ID,
		MerchantNo:    paymentInfo.MerchantNo,
		BatchID:       batchID,
		OrderID:       paymentInfo.OrderID,
		OrderNo:       paymentInfo.OrderNo,
		OrderType:     consts.ORDER_TYPE_ORDER,
		PaymentTypeID: paymentInfo.PaymentTypeID,
		PaymentNo:     paymentInfo.PaymentNo,
		PayAmount:     paymentInfo.Amount, // 支付金额
		RefundType:    refundType,
		Reason:        reason,
		Amount:        Amount, // 退款金额
		OutRefundNo:   util.SerialNumber(consts.RefundPaymentPrefix),
	}

	if paymentInfo.PaymentTypeID == consts.PAY_TYPE_WECHAT { // 微信支付
		refundLog.MchID = merchant.MchID
		refundLog.SubMchID = merchant.SubMchID
	}

	if paymentInfo.PaymentTypeID == consts.PAY_TYPE_ALIPAY { // 支付宝支付
		refundLog.MchID = merchant.MchID
		refundLog.SubMchID = merchant.AlipaySubMchID
	}

	if refundLog.PaymentTypeID == consts.PAY_TYPE_CASH { // 现金支付
		now := time.Now()
		refundLog.RefundStatus = consts.REFUND_STATUS_SUCCESS
		refundLog.RefundAt = &now
		refundLog.TradeType = "CASH"
		refundLog.TradeState = "SUCCESS"
		refundLog.TradeDesc = "现金退款成功"
	}

	if refundLog.PaymentTypeID == consts.PAY_TYPE_VIPCARD { // VIP支付
		refundLog.TradeType = "VIPCARD"
		refundLog.RefundID = util.Int64ToStr(paymentInfo.CustomerID)
	}

	err := util.GetDB(ctx, serv.DB).Create(refundLog).Error
	return refundLog, err
}

// UpdateRefundStatus 更新支付状态
func (serv *RefundLogService) UpdateRefundStatus(ctx context.Context, refundResult *schema.RefundResult) (*model.MerchantRefundLogModel, error) {
	refundLog, err := serv.GetByOutRefundNo(ctx, refundResult.OutRefundNo)
	if err != nil {
		return nil, err
	} else if refundLog == nil {
		return nil, errors.NotFound("", "PaymentNotFound")
	}
	// 如果已经支付过了，则不再更新
	if refundLog.RefundStatus == consts.REFUND_STATUS_SUCCESS {
		return refundLog, nil
	}

	// 更新支付状态
	if refundResult.Status == consts.REFUND_STATUS_FAILED { // 退款失败
		return refundLog, util.GetDB(ctx, serv.DB).Model(refundLog).Updates(map[string]interface{}{
			"refund_status": consts.REFUND_STATUS_FAILED,
			"trade_state":   refundResult.TradeState,
			"trade_desc":    refundResult.TradeDesc,
		}).Error
	} else { // 正在处理/已退款
		return refundLog, util.GetDB(ctx, serv.DB).Model(refundLog).Updates(map[string]interface{}{
			"refund_status": refundResult.Status,
			"refund_at":     refundResult.RefundAt,
			"refund_id":     refundResult.RefundID,
			"trade_state":   refundResult.TradeState,
			"trade_desc":    refundResult.TradeDesc,
		}).Error
	}
}

// ListByOutTradeNo 根据商户订单号获取退款记录
func (serv *RefundLogService) ListByOutTradeNo(ctx context.Context, outTradeNo string) ([]model.MerchantRefundLogModel, error) {
	var refundLogs []model.MerchantRefundLogModel
	return refundLogs, util.GetDB(ctx, serv.DB).Where("out_trade_no=?", outTradeNo).Find(&refundLogs).Error
}

// ListByPaymentNo 根据支付单号获取退款记录
func (serv *RefundLogService) ListByPaymentNo(ctx context.Context, paymentNo string) ([]model.MerchantRefundLogModel, error) {
	var refundLogs []model.MerchantRefundLogModel
	return refundLogs, util.GetDB(ctx, serv.DB).Where("out_trade_no=?", paymentNo).Find(&refundLogs).Error
}

// GetByOutRefundNo 根据商户退款单号获取退款记录
func (serv *RefundLogService) GetByOutRefundNo(ctx context.Context, outRefundNo string) (*model.MerchantRefundLogModel, error) {
	refundLog := &model.MerchantRefundLogModel{}
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("out_refund_no=?", outRefundNo), refundLog)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return refundLog, nil
}

// GetByPaymentNo 根据支付单号获取退款记录
func (serv *RefundLogService) GetByPaymentNo(ctx context.Context, merchantNo string, paymentNo string, refundType int64) (*model.MerchantRefundLogModel, error) {
	var refundLog model.MerchantRefundLogModel
	db := util.GetDB(ctx, serv.DB).
		Model(&model.MerchantRefundLogModel{}).
		Where("merchant_no=? AND payment_no=?", merchantNo, paymentNo).
		Where("refund_type=?", refundType)
	ok, err := util.FindOne(ctx, db, &refundLog)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &refundLog, nil
}

package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// TableService 餐厅餐桌业务逻辑
type TableService struct {
	DB *gorm.DB
}

// GetMerchantTables 获取餐厅餐桌列表

func (serv *TableService) GetMerchantTables(ctx context.Context, merchantNo string) ([]*model.TableModel, error) {
	var list []*model.TableModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

// GetTableByID 获取餐桌信息
func (serv *TableService) GetTableByID(ctx context.Context, merchantNo string, id int64) (*model.TableModel, error) {
	var table model.TableModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id =?", id).Where("merchant_no = ?", merchantNo), &table)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, nil
	}
	return &table, nil
}

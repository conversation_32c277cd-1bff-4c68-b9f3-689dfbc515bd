package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
	"time"
)

// MerchantInfoUpdateService 商家各项数据更新业务逻辑
type MerchantInfoUpdateService struct {
	DB *gorm.DB
}

// GetMerchantInfoUpdate 获取商家信息更新记录
func (serv *MerchantInfoUpdateService) GetMerchantInfoUpdate(ctx context.Context, merchantNo string) (*model.MerchantInfoUpdateModel, error) {
	result, err := serv.GetByMerchantNo(ctx, merchantNo)
	if err != nil {
		return nil, err
	}
	if result == nil {
		// 创建一条新的商户信息更新记录
		now := time.Now()
		result = &model.MerchantInfoUpdateModel{
			MerchantNo:           merchantNo,
			UserUpdatedAt:        now,
			AreaUpdatedAt:        now,
			TableUpdatedAt:       now,
			FoodUpdatedAt:        now,
			RemarkUpdatedAt:      now,
			PaymentTypeUpdatedAt: now,
			PrinterUpdatedAt:     now,
			FoodPrinterUpdatedAt: now,
			PermissionUpdatedAt:  now,
			CreatedAt:            now,
		}
		err = util.GetDB(ctx, serv.DB).Create(result).Error
		if err != nil {
			return nil, err
		}
	}
	return result, nil
}

// GetByMerchantNo 根据商户号获取商家信息更新记录
func (serv *MerchantInfoUpdateService) GetByMerchantNo(ctx context.Context, merchantNo string) (*model.MerchantInfoUpdateModel, error) {
	var item model.MerchantInfoUpdateModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("merchant_no=?", merchantNo), &item)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &item, nil
}

// UpdateColumn 更新指定字段的更新时间
func (serv *MerchantInfoUpdateService) UpdateColumn(ctx context.Context, no string, column string) error {
	db := util.GetDB(ctx, serv.DB)
	return db.Model(&model.MerchantInfoUpdateModel{}).Where("merchant_no=?", no).Update(column, time.Now()).Error
}

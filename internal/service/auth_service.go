package service

import (
	"context"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/cachex"
	"ros-api-go/pkg/crypto/hash"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/jwtx"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"strconv"
	"time"
)

// AuthService 用户鉴权相关业务逻辑
type AuthService struct {
	DB    *gorm.DB
	Cache cachex.Cacher
	Auth  jwtx.Auther
}

// LoginUser 账号密码获取用户信息
func (serv *AuthService) LoginUser(ctx context.Context, Username string, Password string) (*model.UserModel, error) {
	// 获取用户信息
	var user model.UserModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("phone =?", Username), &user)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, errors.BadRequest(consts.ErrInvalidUsernameOrPassword, "InvalidUsernameOrPassword")
	} else if user.State != model.UserStatusActivated {
		return nil, errors.BadRequest("", "UserNotActive")
	}
	// check password
	if err := hash.CompareHashAndPassword(user.Password, Password); err != nil {
		return nil, errors.BadRequest(consts.ErrInvalidUsernameOrPassword, "InvalidUsernameOrPassword")
	}

	return &user, nil
}

// LoginUserWithMerchant 账号密码获取用户信息
func (serv *AuthService) LoginUserWithMerchant(ctx context.Context, Username string, Password string, merchantNo string) (*model.UserModel, error) {
	// 获取用户信息
	var user model.UserModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Select("users.id, users.avatar, users.phone, users.password, users.role_id, users.api_token, users.leaved_at, users.last_login, "+
			"me.merchant_no, me.no as job_no, me.name_ug, me.name_zh, me.is_owner, me.operation_password, me.state, me.created_at, me.updated_at, me.deleted_at").
		Joins("JOIN merchant_employees me ON me.user_id = users.id").
		Where("users.phone =? AND me.merchant_no =?", Username, merchantNo), &user)
	if err != nil {
		return nil, err
	} else if !ok {
		return nil, errors.BadRequest("", "NoActiveMerchantFound")
	} else if user.State != model.UserStatusActivated {
		return nil, errors.BadRequest("", "UserNotActive")
	}
	// check password
	if err := hash.CompareHashAndPassword(user.Password, Password); err != nil {
		return nil, errors.BadRequest(consts.ErrInvalidUsernameOrPassword, "InvalidUsernameOrPassword")
	}

	return &user, nil
}

// GetUserPermissions 获取用户权限
func (serv *AuthService) GetUserPermissions(ctx context.Context, userID int64, merchantNo string) ([]string, error) {

	// 查询商家用户
	var merchantEmployee *model.MerchantEmployeeModel
	_, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("user_id =? AND merchant_no =?", userID, merchantNo).
		Preload("Roles"), &merchantEmployee)
	if err != nil {
		return nil, err
	}
	if merchantEmployee == nil {
		return nil, errors.BadRequest("", "UserNotFound")
	}
	if merchantEmployee.State == 0 {
		return nil, errors.BadRequest("", "UserNotActive")
	}

	// 店长直接返回空权限
	if merchantEmployee.IsOwner {
		return []string{consts.AdminPermission}, nil
	}

	// 使用 map 去重
	permissionSet := make(map[string]bool)
	for _, role := range merchantEmployee.Roles {
		for _, permission := range role.Permissions {
			permissionSet[permission] = true
		}
	}

	// 将 map 转换为切片
	uniquePermissions := make([]string, 0, len(permissionSet))
	for permission := range permissionSet {
		uniquePermissions = append(uniquePermissions, permission)
	}

	return uniquePermissions, nil

}

// GenerateUserToken 生成用户token
func (serv *AuthService) GenerateUserToken(ctx context.Context, user *model.UserModel, clientType model.ClientType) (*schema.LoginToken, error) {
	// 更新user_tokens 表
	userToken, err := serv.GetUserToken(ctx, user.ID, clientType)
	if err != nil {
		return nil, err
	}

	token, err := serv.Auth.GenerateAlmasToken(ctx, userToken)
	if err != nil {
		return nil, err
	}

	logging.Context(ctx).Info("生成用户token成功", zap.Any("user", user.Phone))

	return &schema.LoginToken{
		AccessToken:      token.GetAccessToken(),
		TokenType:        token.GetTokenType(),
		ExpiresAt:        token.GetExpiresAt(),
		RefreshToken:     token.GetRefreshToken(),
		RefreshExpiresAt: token.GetRefreshExpiresAt(),
	}, nil
}

// GetUserToken 根据用户ID和token类型获取用户token
func (serv *AuthService) GetUserToken(ctx context.Context, userID int64, clientType model.ClientType) (*model.UserTokenModel, error) {
	// 过期时间
	expiresAt := time.Now().Add(time.Duration(config.C.Middleware.Auth.Expired) * time.Second)
	userToken := &model.UserTokenModel{
		UserID:     userID,
		ClientType: clientType,
	}

	_, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("user_id =? AND client_type =?", userID, clientType), userToken)
	if err != nil {
		return nil, err
	}
	userToken.UserToken = util.RandomString(32)
	userToken.ExpiresAt = &expiresAt

	if err = util.GetDB(ctx, serv.DB).Save(userToken).Error; err != nil {
		return nil, err
	}

	// 清空互斥客户端的token
	clientTypes := model.GetUserTokenMutex(clientType)
	err = util.GetDB(ctx, serv.DB).Model(model.UserTokenModel{}).
		Where("user_id =? AND client_type IN (?)", userID, clientTypes).
		Update("user_token", "").Error
	if err != nil {
		return nil, err
	}

	return userToken, nil
}

// GetUserTokens 获取用户当前token
func (serv *AuthService) GetUserTokens(ctx context.Context, userID int64) (map[string]any, error) {
	userKey := "jwt:" + strconv.FormatInt(userID, 10)
	tokens := map[string]any{}
	err := serv.Cache.Iterator(
		ctx,
		userKey,
		func(ctx context.Context, key, value string) bool {
			tokens[key] = value
			return true
		})

	if err != nil {
		return nil, err
	}
	return tokens, nil
}

// Logout 登出
func (serv *AuthService) Logout(ctx context.Context, userToken string) error {
	ctx = logging.NewTag(ctx, logging.TagKeyLogout)
	if err := serv.Auth.DestroyToken(ctx, userToken); err != nil {
		return err
	}

	userID := util.FromUserID(ctx)
	err := serv.Cache.Delete(ctx, consts.CacheNSForUser, strconv.FormatInt(userID, 10))
	if err != nil {
		logging.Context(ctx).Error("Failed to delete user cache", zap.Error(err))
	}
	logging.Context(ctx).Info("Logout success")

	return nil
}

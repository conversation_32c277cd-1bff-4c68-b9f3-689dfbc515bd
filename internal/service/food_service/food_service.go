package food_service

import (
	"context"
	"fmt"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/cachex"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

// FoodsService 美食业务逻辑
type FoodsService struct {
	DB    *gorm.DB
	Cache cachex.Cacher
	Trans *util.Trans
}

// GetMerchantFoodsForSync 获取商户的美食列表（用于同步）
func (serv *FoodsService) GetMerchantFoodsForSync(ctx context.Context, merchantNo string) ([]*model.FoodModel, error) {
	var list []*model.FoodModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =? AND deleted_at IS NULL", merchantNo).Find(&list).Error
}

// GetMerchantAvailableFoods 获取商户可用的菜品业务逻辑(用于线上模式显示菜品列表)
func (serv *FoodsService) GetMerchantAvailableFoods(ctx context.Context, merchantNo string, catId int64, keyword string, sellClearAll string) ([]*model.FoodModel, error) {
	var foods []*model.FoodModel
	// 预加载与食品相关的组合信息，仅加载类型为1的组合，并预加载其子组合（类型为2）
	result := util.GetDB(ctx, serv.DB).
		Where("foods.pid = ? AND foods.merchant_no = ? AND foods.state = ?", 0, merchantNo, 1).
		Preload("Specs").
		Preload("Combos", func(db *gorm.DB) *gorm.DB {
			return db.Where("type = ?", 1).
				Preload("Childs", "type = ?", 2)
		})

	// 筛选已禁用的分类
	result = result.Joins("JOIN food_categories ON foods.food_category_id = food_categories.id").
		Where("food_categories.merchant_no = ? AND food_categories.state = ?", merchantNo, 1)

	// 分类
	if catId != 0 {
		result = result.Where("food_category_id = ?", catId)
	}
	// 关键字搜索
	if keyword != "" {
		result = result.Where("foods.name_ug LIKE ? OR foods.name_zh LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 如果 sellClearAll 参数存在，则仅筛选出清理状态为1的食品
	if sellClearAll != "" {
		result = result.Where("foods.cell_clear_state = ?", 1)
	}

	// 按照排序字段降序排列
	result = result.Order("sort asc")

	// 返回食品列表和可能的错误信息
	return foods, result.Find(&foods).Error
}

// GetFoodsList 获取美食列表（分页）
func (serv *FoodsService) GetFoodsList(ctx context.Context, req *request.FoodListRequest) ([]*model.FoodModel, error) {
	var list []*model.FoodModel

	db := util.GetDB(ctx, serv.DB).
		Joins("LEFT JOIN food_categories ON foods.food_category_id = food_categories.id").
		Where("food_categories.state = ? AND food_categories.deleted_at IS NULL AND foods.deleted_at IS NULL", 1).
		Preload("Combos", func(db *gorm.DB) *gorm.DB {
			return db.Where("type = ?", 1).
				Preload("Childs", "type = ?", 2)
		}).
		Model(&model.FoodModel{}).
		Order("foods.created_at asc").
		Order("foods.food_category_id asc")

	if req.MerchantNo != "" {
		db = db.Where("foods.merchant_no = ?", req.MerchantNo)
	}

	if req.FoodCategoryID > 0 {
		db = db.Where("foods.food_category_id = ?", req.FoodCategoryID)
	}

	if req.Keyword != "" {
		db = db.Where("foods.name_ug LIKE ? OR foods.name_zh LIKE ? OR foods.shortcut_code LIKE ?",
			"%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	if req.State != nil {
		db = db.Where("foods.state = ?", *req.State)
	}

	if req.SellClearAll == "1" {
		db = db.Where("foods.cell_clear_state = ?", 1)
	}

	if req.SupportScanOrder != nil {
		db = db.Where("foods.support_scan_order = ?", *req.SupportScanOrder)
	}

	err := db.Find(&list).Error
	if err != nil {
		return nil, err
	}

	return list, nil
}

// validateCombo 验证套餐信息
func (serv *FoodsService) validateCombo(ctx context.Context, req interface{}, merchantNo string) error {
	var comboFoods []request.ComboFoodRequest
	var price float64
	var isCombo bool

	// 根据请求类型获取套餐信息
	switch r := req.(type) {
	case *request.CreateFoodRequest:
		comboFoods = r.ComboFoods
		price = r.Price
		isCombo = r.IsCombo
	case *request.UpdateFoodRequest:
		comboFoods = r.ComboFoods
		price = r.Price
		isCombo = r.IsCombo
	default:
		return errors.BadRequest("", "InvalidRequestType")
	}

	if !isCombo {
		return nil
	}

	// 检查套餐菜品信息
	if len(comboFoods) == 0 {
		return errors.BadRequest("", "FoodComboInfoCantBeNull")
	}

	// 获取所有美食ID
	var foodIds []int64
	totalComboPrice := 0.0

	for _, combo := range comboFoods {
		totalComboPrice += combo.ComboPrice * float64(combo.ComboCount)

		// 解析逗号分隔的菜品ID
		ids := strings.Split(combo.ComboFoodID, ",")
		for _, idStr := range ids {
			id, err := strconv.ParseInt(strings.TrimSpace(idStr), 10, 64)
			if err != nil {
				return errors.BadRequest("", "InvalidFoodID")
			}
			foodIds = append(foodIds, id)
		}
	}

	// 检查所有菜品是否属于该商户
	for _, foodId := range foodIds {
		var food model.FoodModel
		err := util.GetDB(ctx, serv.DB).Where("id = ? AND merchant_no = ?", foodId, merchantNo).First(&food).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return errors.BadRequest("", "FoodNotFound")
			}
			return err
		}
	}

	// 验证总价
	if fmt.Sprintf("%.2f", price) != fmt.Sprintf("%.2f", totalComboPrice) {
		return errors.BadRequest("", fmt.Sprintf("FoodPriceAndComboPriceNotEqual: price=%.2f, combo_price=%.2f", price, totalComboPrice))
	}

	return nil
}

// updateComboFoods 更新套餐菜品
func (serv *FoodsService) updateComboFoods(ctx context.Context, foodId int64, merchantNo string, comboFoods []request.ComboFoodRequest) error {
	// 删除现有的套餐关联
	err := util.GetDB(ctx, serv.DB).Where("combo_id = ?", foodId).Delete(&model.FoodComboModel{}).Error
	if err != nil {
		return err
	}

	// 创建新的套餐关联
	for _, combo := range comboFoods {
		// 创建主套餐记录
		mainCombo := &model.FoodComboModel{
			MerchantNo:  merchantNo,
			ComboID:     foodId,
			Type:        "1", // 主套餐
			FoodID:      foodId,
			OriginPrice: 0, // 这里需要从food表获取原价
			ComboPrice:  combo.ComboPrice,
			Count:       combo.ComboCount,
			NameUg:      combo.NameUg,
			NameZh:      combo.NameZh,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		err = util.GetDB(ctx, serv.DB).Create(mainCombo).Error
		if err != nil {
			return err
		}

		// 解析逗号分隔的菜品ID
		ids := strings.Split(combo.ComboFoodID, ",")
		for _, idStr := range ids {
			foodId, err := strconv.ParseInt(strings.TrimSpace(idStr), 10, 64)
			if err != nil {
				continue
			}

			// 获取菜品信息
			var food model.FoodModel
			err = util.GetDB(ctx, serv.DB).Where("id = ?", foodId).First(&food).Error
			if err != nil {
				continue
			}

			// 创建子套餐记录
			childCombo := &model.FoodComboModel{
				MerchantNo:  merchantNo,
				ComboID:     mainCombo.ComboID,
				Type:        "2", // 子套餐
				ParentID:    mainCombo.ID,
				FoodID:      foodId,
				OriginPrice: 0,
				ComboPrice:  0,
				NameUg:      food.NameUg,
				NameZh:      food.NameZh,
				Count:       0,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			}

			err = util.GetDB(ctx, serv.DB).Create(childCombo).Error
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// CreateFood 创建美食
func (serv *FoodsService) CreateFood(ctx context.Context, req *request.CreateFoodRequest, merchantNo string) (*model.FoodModel, error) {
	// 验证套餐信息
	err := serv.validateCombo(ctx, req, merchantNo)
	if err != nil {
		return nil, err
	}

	// 检查分类是否存在且属于该商户
	var categoryCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodCategoryModel{}).
		Where("id = ? AND merchant_no = ? AND state = ?", req.FoodCategoryID, merchantNo, 1).
		Count(&categoryCount).Error
	if err != nil {
		return nil, err
	}
	if categoryCount == 0 {
		return nil, errors.BadRequest("", "FoodCategoryNotFound")
	}

	// 检查名称是否已存在
	var nameCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("merchant_no = ? AND (name_ug = ? OR name_zh = ?)", merchantNo, req.NameUg, req.NameZh).
		Count(&nameCount).Error
	if err != nil {
		return nil, err
	}
	if nameCount > 0 {
		return nil, errors.BadRequest("", "FoodNameExists")
	}

	// 检查快捷码是否已存在
	if req.ShortcutCode != "" {
		var codeCount int64
		err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
			Where("merchant_no = ? AND shortcut_code = ?", merchantNo, req.ShortcutCode).
			Count(&codeCount).Error
		if err != nil {
			return nil, err
		}
		if codeCount > 0 {
			return nil, errors.BadRequest("", "ShortcutCodeExists")
		}
	}

	// 创建美食记录
	food := &model.FoodModel{
		MerchantNo:       merchantNo,
		FoodCategoryID:   req.FoodCategoryID,
		Image:            req.Image,
		ShortcutCode:     req.ShortcutCode,
		NameUg:           req.NameUg,
		NameZh:           req.NameZh,
		CostPrice:        req.CostPrice,
		VipPrice:         req.VipPrice,
		Price:            req.Price,
		FormatID:         req.FormatID,
		IsSpecialFood:    req.IsSpecialFood,
		SupportScanOrder: req.SupportScanOrder,
		CellClearState:   req.CellClearState,
		SellClearCount:   req.SellClearCount,
		RemainingCount:   req.RemainingCount,
		IsCombo:          req.IsCombo,
		Sort:             req.Sort,
		State:            req.State,
		IsSync:           false,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = serv.Trans.Exec(ctx, func(ctx context.Context) error {
		err = util.GetDB(ctx, serv.DB).Create(food).Error
		if err != nil {
			return err
		}

		// 如果是套餐，创建套餐关联
		if req.IsCombo && len(req.ComboFoods) > 0 {
			err = serv.updateComboFoods(ctx, food.ID, merchantNo, req.ComboFoods)
			if err != nil {
				return err
			}
		}
		return nil
	})

	return food, nil
}

// UpdateFood 更新美食
func (serv *FoodsService) UpdateFood(ctx context.Context, foodId int64, req *request.UpdateFoodRequest, merchantNo string) (*model.FoodModel, error) {
	// 验证套餐信息
	err := serv.validateCombo(ctx, req, merchantNo)
	if err != nil {
		return nil, err
	}

	// 检查美食是否存在且属于该商户
	var food model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ?", foodId, merchantNo), &food)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "FoodNotFound")
	}

	// 检查分类是否存在且属于该商户
	var categoryCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodCategoryModel{}).
		Where("id = ? AND merchant_no = ? AND state = ?", req.FoodCategoryID, merchantNo, 1).
		Count(&categoryCount).Error
	if err != nil {
		return nil, err
	}
	if categoryCount == 0 {
		return nil, errors.BadRequest("", "FoodCategoryNotFound")
	}

	// 检查名称是否已存在（排除当前记录）
	var nameCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("merchant_no = ? AND id <> ? AND (name_ug = ? OR name_zh = ?)", merchantNo, foodId, req.NameUg, req.NameZh).
		Count(&nameCount).Error
	if err != nil {
		return nil, err
	}
	if nameCount > 0 {
		return nil, errors.BadRequest("", "FoodNameExists")
	}

	// 检查快捷码是否已存在（排除当前记录）
	if req.ShortcutCode != "" {
		var codeCount int64
		err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
			Where("merchant_no = ? AND id <> ? AND shortcut_code = ?", merchantNo, foodId, req.ShortcutCode).
			Count(&codeCount).Error
		if err != nil {
			return nil, err
		}
		if codeCount > 0 {
			return nil, errors.BadRequest("", "ShortcutCodeExists")
		}
	}

	// 更新美食记录
	food.FoodCategoryID = req.FoodCategoryID
	food.Image = req.Image
	food.ShortcutCode = req.ShortcutCode
	food.NameUg = req.NameUg
	food.NameZh = req.NameZh
	food.CostPrice = req.CostPrice
	food.VipPrice = req.VipPrice
	food.Price = req.Price
	food.FormatID = req.FormatID
	food.IsSpecialFood = req.IsSpecialFood
	food.SupportScanOrder = req.SupportScanOrder
	food.CellClearState = req.CellClearState
	food.SellClearCount = req.SellClearCount
	food.RemainingCount = req.RemainingCount
	food.IsCombo = req.IsCombo
	food.Sort = req.Sort
	food.State = req.State
	food.UpdatedAt = time.Now()

	err = serv.Trans.Exec(ctx, func(ctx context.Context) error {
		err = util.GetDB(ctx, serv.DB).Save(&food).Error
		if err != nil {
			return err
		}

		// 更新套餐关联
		if req.IsCombo {
			err = serv.updateComboFoods(ctx, foodId, merchantNo, req.ComboFoods)
			if err != nil {
				return err
			}
		} else {
			// 如果不是套餐，删除所有套餐关联
			err = util.GetDB(ctx, serv.DB).Where("combo_id = ?", foodId).Delete(&model.FoodComboModel{}).Error
			if err != nil {
				return err
			}
		}
		return nil
	})

	return &food, nil
}

// ChangeFoodState 更改食品状态
func (serv *FoodsService) ChangeFoodState(ctx context.Context, merchantNo string, foodId int64) (*model.FoodModel, error) {
	var food model.FoodModel

	// 查询食品记录
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("merchant_no = ? AND id = ?", merchantNo, foodId), &food)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "FoodNotFound")
	}

	// 更新状态（在0和1之间切换）
	if food.State == model.FoodStateNormal {
		food.State = model.FoodStateDisabled
	} else {
		food.State = model.FoodStateNormal
	}

	// 保存更改
	err = util.GetDB(ctx, serv.DB).Save(&food).Error
	if err != nil {
		return nil, err
	}

	return &food, nil
}

// ChangeFoodSupportScanOrder 更改食品是否支持扫码点单
func (serv *FoodsService) ChangeFoodSupportScanOrder(ctx context.Context, merchantNo string, foodId int64) (*model.FoodModel, error) {
	var food model.FoodModel

	// 查询食品记录
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("merchant_no = ? AND id = ?", merchantNo, foodId), &food)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "FoodNotFound")
	}

	// 更新支持扫码点单状态（取反）
	food.SupportScanOrder = !food.SupportScanOrder

	// 保存更改
	err = util.GetDB(ctx, serv.DB).Save(&food).Error
	if err != nil {
		return nil, err
	}

	return &food, nil
}

// DeleteFood 删除食品
func (serv *FoodsService) DeleteFood(ctx context.Context, merchantNo string, foodId int64) error {
	// 查询食品记录
	var food model.FoodModel
	// 查询食品记录
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("merchant_no = ? AND id = ?", merchantNo, foodId), &food)
	if err != nil {
		return err
	}
	if !one {
		return errors.NotFound("", "FoodNotFound")
	}

	// 检查是否有未支付的线上订单包含此食品
	var orderDetailCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.OrderDetailModel{}).
		Joins("JOIN cloud_orders ON order_detail_models.order_id = cloud_orders.id").
		Where("order_detail_models.food_id = ? AND cloud_orders.state < ?", foodId, consts.ORDER_STATE_PAID).
		Count(&orderDetailCount).Error
	if err != nil {
		return err
	}

	if orderDetailCount > 0 {
		return errors.BadRequest("", "ThisFoodHaveUnpaidOrder")
	}

	// 检查是否有套餐包含此食品
	var comboCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodComboModel{}).
		Where("food_id = ?", foodId).
		Count(&comboCount).Error
	if err != nil {
		return err
	}

	if comboCount > 0 {
		return errors.BadRequest("", "food_in_combo_foods")
	}

	// 软删除
	now := time.Now()
	return util.GetDB(ctx, serv.DB).
		Model(&model.FoodModel{}).
		Where("id = ? AND merchant_no = ?", foodId, merchantNo).
		Updates(map[string]interface{}{
			"state":      model.FoodStateDisabled,
			"updated_at": now,
			"deleted_at": &now,
		}).Error
}

// UpdateFoodSellClearData 更新菜品沽清数据业务逻辑
func (serv *FoodsService) UpdateFoodSellClearData(ctx context.Context, formItem request.FoodSellClearDataRequest) error {
	food := model.FoodModel{}
	err := util.GetDB(ctx, serv.DB).
		Where("merchant_no =? and id =?", formItem.MerchantNo, formItem.ID).
		First(&food).Error
	if err != nil {
		return err
	}
	food.CellClearState = formItem.CellClearState
	food.SellClearCount = formItem.SellClearCount
	food.RemainingCount = formItem.RemainingCount
	return util.GetDB(ctx, serv.DB).
		Select("cell_clear_state", "sell_clear_count", "remaining_count").
		Save(&food).Error
}

// GetFoodListForDualScreen 获取双屏显示食品列表
func (serv *FoodsService) GetFoodListForDualScreen(ctx context.Context, merchantNo string) ([]*model.FoodModel, error) {
	var foods []*model.FoodModel

	// 尝试从缓存获取数据
	cachedData, found, err := serv.Cache.Get(ctx, consts.CacheNSForDualScreen, merchantNo)
	if err != nil {
		return nil, err
	}
	if found {
		var cache []*model.FoodModel
		err := json.Unmarshal([]byte(cachedData), &cache)
		if err != nil {
			return nil, err
		}
		return cache, nil
	}

	// 如果缓存不存在或已过期，从数据库获取
	query := util.GetDB(ctx, serv.DB).
		Where("foods.merchant_no = ? AND foods.state = ?", merchantNo, model.FoodStateNormal).
		Select("foods.id, foods.name_ug, foods.name_zh, foods.image, foods.vip_price, foods.price").
		Limit(20).
		Order("foods.sort ASC, foods.created_at ASC")

	err = query.Find(&foods).Error
	if err != nil {
		return nil, err
	}

	// 将结果存入缓存（10分钟）
	cache := json.MarshalToString(foods)
	err = serv.Cache.Set(ctx, consts.CacheNSForDualScreen, merchantNo, cache, 10*time.Minute)
	if err != nil {
		return nil, err
	}

	return foods, nil
}

// GetDefaultFoodImages 获取系统默认提供的美食图片数据
func (serv *FoodsService) GetDefaultFoodImages(ctx context.Context) ([]*model.FoodsImageModel, error) {
	var images []*model.FoodsImageModel
	return images, util.GetDB(ctx, serv.DB).Find(&images).Error
}

// SaveSort 保存美食排序
func (serv *FoodsService) SaveSort(ctx context.Context, req *request.SaveFoodSortRequest, merchantNo string) error {
	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 批量验证美食是否属于当前商家
		foodIDs := make([]int64, 0, len(req.Items))
		for _, item := range req.Items {
			foodIDs = append(foodIDs, item.ID)
		}

		// 批量更新排序
		for _, item := range req.Items {
			err := util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
				Where("id = ? AND merchant_no = ?", item.ID, merchantNo).
				Update("sort", item.Sort).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

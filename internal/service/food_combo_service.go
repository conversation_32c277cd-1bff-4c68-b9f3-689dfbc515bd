package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// FoodComboService 菜品套餐业务逻辑
type FoodComboService struct {
	DB *gorm.DB
}

// GetMerchantFoodCombos 获取菜品套餐列表
func (serv *FoodComboService) GetMerchantFoodCombos(ctx context.Context, merchantNo string) ([]*model.FoodComboModel, error) {
	var list []*model.FoodComboModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

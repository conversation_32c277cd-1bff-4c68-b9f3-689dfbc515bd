package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
	"time"
)

type HandoverService struct {
	DB *gorm.DB
}

// HasActiveHandover 判断用户是否有未交班的记录
func (serv *HandoverService) HasActiveHandover(ctx context.Context, userID int64, merchantNo string) (bool, error) {
	ok, err := util.Exists(ctx, util.GetDB(ctx, serv.DB).
		Model(new(model.HandoverLogModel)).
		Where("user_id=?", userID).
		Where("merchant_no=?", merchantNo).
		Where("leave_at IS NULL"))
	return ok, err
}

// GetAvailableHandoverLog 获取可交班记录
func (serv *HandoverService) GetAvailableHandoverLog(ctx context.Context, userID int64, merchantNo string) (*model.HandoverLogModel, error) {
	var handoverLog model.HandoverLogModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Model(new(model.HandoverLogModel)).
		Where("user_id=? AND merchant_no=? AND leave_at IS NULL", userID, merchantNo).
		Where("leave_at IS NULL"), &handoverLog)
	if !ok {
		return nil, err
	}
	return &handoverLog, nil
}

// Open 开班
func (serv *HandoverService) Open(ctx context.Context, handoverForm request.HandoverOpenForm) (*model.HandoverLogModel, error) {
	handoverLog := &model.HandoverLogModel{
		MerchantNo:      handoverForm.MerchantNo,
		UserID:          handoverForm.UserID,
		Shift:           handoverForm.Shift,
		AlternateAmount: handoverForm.AlternateAmount,
		WorkingBalance:  handoverForm.WorkingBalance,
		StartAt:         time.Now(),
	}
	if err := util.GetDB(ctx, serv.DB).Model(handoverLog).Save(&handoverLog).Error; err != nil {
		return nil, err
	}
	return handoverLog, nil
}

// ClearHandoverDetail 清空交班详情
func (serv *HandoverService) ClearHandoverDetail(ctx context.Context, handoverLogID int64) error {
	return util.GetDB(ctx, serv.DB).
		Where("handover_log_id=?", handoverLogID).
		Delete(&model.HandoverDetailModel{HandoverLogID: handoverLogID}).Error
}

// CreateHandoverDetail 创建交班详情
func (serv *HandoverService) CreateHandoverDetail(ctx context.Context, detail []*model.HandoverDetailModel) error {
	return util.GetDB(ctx, serv.DB).Create(detail).Error
}

// GetHandoverDetails 获取交班详情
func (serv *HandoverService) GetHandoverDetails(ctx context.Context, handoverLogID int64) ([]*model.HandoverDetailModel, error) {
	var details []*model.HandoverDetailModel
	return details, util.GetDB(ctx, serv.DB).
		Where("handover_log_id=?", handoverLogID).
		Preload("PaymentType").
		Find(&details).Error
}

// Handover 交班
func (serv *HandoverService) Handover(ctx context.Context, handoverLog *model.HandoverLogModel, order *schema.HandoverOverviewFromOrder) error {
	updateData := map[string]interface{}{
		"leave_at":          time.Now(),
		"order_count":       order.OrderCount,
		"paid_amount":       order.PaidAmount,
		"receivable_amount": order.ReceivableAmount,
		"refund_amount":     order.RefundAmount,
	}
	return util.GetDB(ctx, serv.DB).Model(handoverLog).Updates(updateData).Error
}

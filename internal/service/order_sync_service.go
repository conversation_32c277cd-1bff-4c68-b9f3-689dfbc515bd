package service

import (
	"context"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request/order_request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"

	"gorm.io/gorm"
)

type OrderSyncService struct {
	Trans util.Trans
	DB    *gorm.DB
}

// SyncOrder 同步订单
func (serv *OrderSyncService) SyncOrder(ctx context.Context, merchantNo string, syncReq *order_request.OrderSyncRequest) error {

	// 订单部分
	orderData := syncReq.Order
	orderData.MerchantNo = merchantNo
	// 检查订单是否存在, 直接返回同步成功
	if err := serv.CheckOrderExists(ctx, orderData.MerchantNo, orderData.No); err != nil {
		return nil
	}

	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		orderModel, err := serv.syncOrderData(ctx, orderData)
		if err != nil {
			return err
		}

		// 更新退款记录的订单ID
		if err = serv.updateOrderRefundLog(ctx, orderData.MerchantNo, orderData.No, orderModel.ID); err != nil {
			return err
		}

		// 订单详情部分
		orderDetailData := syncReq.OrderDetails
		err = serv.syncOrderDetails(ctx, orderModel, orderDetailData)
		if err != nil {
			return err
		}

		// 订单支付部分
		orderPaymentData := syncReq.OrderPayments
		err = serv.syncOrderPayments(ctx, orderModel, orderPaymentData)
		if err != nil {
			return err
		}
		// 更新赊账记录的订单ID
		err = serv.updateDebtTransactionOrderID(ctx, merchantNo, orderModel.No, orderModel.ID)
		if err != nil {
			return err
		}

		return nil
	})
}

// syncOrderData 同步订单数据
func (serv *OrderSyncService) syncOrderData(ctx context.Context, orderData *order_request.SyncOrderForm) (*model.OrderModel, error) {

	orderModel := model.OrderModel{}

	err := orderData.FillTo(ctx, &orderModel)
	if err != nil {
		return nil, err
	}
	// 保存订单
	err = util.GetDB(ctx, serv.DB).Save(&orderModel).Error
	if err != nil {
		return nil, err
	}
	return &orderModel, nil
}

// 更新退款记录的订单ID
func (serv *OrderSyncService) updateOrderRefundLog(ctx context.Context, merchantNo, orderNo string, orderID int64) error {
	return util.GetDB(ctx, serv.DB).Model(model.MerchantRefundLogModel{}).
		Where("order_no=? and merchant_no=?", orderNo, merchantNo).
		Update("order_id", orderID).Error
}

// syncOrderDetails 同步订单详情数据
func (serv *OrderSyncService) syncOrderDetails(
	ctx context.Context,
	orderModel *model.OrderModel,
	orderDetailData []*order_request.SyncOrderDetailForm) error {
	orderDetailModels := make([]model.OrderDetailModel, len(orderDetailData))
	for _, detail := range orderDetailData {
		detailModel := model.OrderDetailModel{}
		detail.OrderID = orderModel.ID
		detail.OrderNo = orderModel.No
		detail.MerchantNo = orderModel.MerchantNo
		// 如果存在订单为已支付，详情为未支付，则更新订单状态为已支付
		if detail.State == consts.ORDER_DETAIL_STATE_UNPAID && orderModel.State == consts.ORDER_STATE_PAID {
			detail.State = consts.ORDER_STATE_PAID
		}
		err := detail.FillTo(ctx, &detailModel)
		if err != nil {
			return err
		}
		orderDetailModels = append(orderDetailModels, detailModel)
	}
	if len(orderDetailModels) == 0 {
		return nil
	}
	return util.GetDB(ctx, serv.DB).Save(&orderDetailModels).Error
}

// syncOrderPayments 同步订单详情数据
func (serv *OrderSyncService) syncOrderPayments(
	ctx context.Context,
	orderModel *model.OrderModel,
	orderPaymentData []*order_request.SyncOrderPaymentForm) error {
	orderPaymentModels := make([]model.MerchantPaymentModel, 0, len(orderPaymentData))
	for _, payment := range orderPaymentData {
		exists, err := serv.CheckPaymentExists(ctx, orderModel.MerchantNo, payment.PaymentNo)
		if err != nil {
			return err
		}
		if exists { // 如果已存在，则更新订单ID, 收银员ID
			util.GetDB(ctx, serv.DB).Model(model.MerchantPaymentModel{}).
				Where("payment_no=? and merchant_no=?", payment.PaymentNo, orderModel.MerchantNo).
				Updates(map[string]any{
					"cashier_id": payment.CashierID,
					"order_id":   orderModel.ID,
				})
			continue
		}
		paymentModel := model.MerchantPaymentModel{}
		payment.MerchantNo = orderModel.MerchantNo
		payment.OrderID = orderModel.ID
		payment.OrderNo = orderModel.No
		err = payment.FillTo(ctx, &paymentModel)
		if err != nil {
			return err
		}
		orderPaymentModels = append(orderPaymentModels, paymentModel)
	}
	if len(orderPaymentModels) == 0 {
		return nil
	}
	return util.GetDB(ctx, serv.DB).Save(&orderPaymentModels).Error
}

// updateDebtTransactionOrderID 更新赊账记录的订单ID
func (serv *OrderSyncService) updateDebtTransactionOrderID(ctx context.Context, merchantNo string, orderNo string, orderID int64) error {
	return util.GetDB(ctx, serv.DB).Model(model.DebtTransactionModel{}).
		Where("merchant_no=? and order_no=? and type=?", merchantNo, orderNo, model.DebtTransactionTypeDebt).
		Update("order_id", orderID).Error
}

// CheckOrderExists 检查订单是否存在
func (serv *OrderSyncService) CheckOrderExists(ctx context.Context, merchantNo string, orderNo string) error {
	exists, err := util.Exists(ctx, util.GetDB(ctx, serv.DB).
		Model(model.OrderModel{}).
		Where("merchant_no=?", merchantNo).
		Where("no=?", orderNo))
	if err != nil {
		return err
	}
	if exists {
		return errors.BadRequest("", "OrderExists")
	}
	return nil
}

// CheckPaymentExists 检查付款记录是否存在
func (serv *OrderSyncService) CheckPaymentExists(ctx context.Context, merchantNo string, paymentNo string) (bool, error) {
	exists, err := util.Exists(ctx, util.GetDB(ctx, serv.DB).
		Model(model.MerchantPaymentModel{}).
		Where("merchant_no=?", merchantNo).
		Where("payment_no=?", paymentNo))
	if err != nil {
		return false, err
	}
	return exists, nil
}

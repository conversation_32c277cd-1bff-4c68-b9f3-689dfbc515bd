package scan_service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type MerchantServiceService struct {
	DB *gorm.DB
}

// GetMerchantServicesForBroadcast 获取服务
func (service *MerchantServiceService) GetMerchantServicesForBroadcast(ctx context.Context, merchantNo string, serviceIds []int64) ([]*model.MerchantServiceModel, error) {
	db := util.GetDB(ctx, service.DB)

	var serviceList []*model.MerchantServiceModel

	err := db.Model(model.MerchantServiceModel{}).
		Select("id,service_name_ug, service_name_zh").
		Where("merchant_no = ? and id in (?)", merchantNo, serviceIds).Find(&serviceList).Error
	if err != nil {
		return nil, err
	}

	return serviceList, nil
}

// CreateServiceHistory 创建服务历史
func (service *MerchantServiceService) CreateServiceHistory(ctx context.Context, merchantNo string, tableId int64, serviceNameUg string, serviceNameZh string, status int) error {
	history := &model.ServiceHistoryModel{
		MerchantNo:    merchantNo,
		TableID:       tableId,
		ServiceNameUg: serviceNameUg,
		ServiceNameZh: serviceNameZh,
		Status:        status,
	}
	db := util.GetDB(ctx, service.DB)
	return db.Create(history).Error
}

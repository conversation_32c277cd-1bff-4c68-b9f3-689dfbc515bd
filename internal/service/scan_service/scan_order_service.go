package scan_service

import (
	"context"
	"ros-api-go/internal/http/request/scan_request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"

	"gorm.io/gorm"
)

type ScanOrderService struct {
	DB *gorm.DB
}

// OrderList 订单列表
func (serv *ScanOrderService) OrderList(ctx context.Context, req scan_request.ScanOrderListRequest) ([]*model.OrderModel, *util.PaginationResult, error) {
	var orders []*model.OrderModel

	// 基础查询条件：微信用户ID必须匹配
	query := util.GetDB(ctx, serv.DB).
		Model(&model.OrderModel{}).
		Preload("Merchant", func(db *gorm.DB) *gorm.DB {
			return db.Select("id, no, name_zh, name_ug, logo, address_ug,address_zh")
		}).
		Preload("Table", func(db *gorm.DB) *gorm.DB {
			return db.Select("id, name_zh,name_ug")
		}).
		Where("wechat_user_id = ?", req.WechatUserId).
		Order("created_at desc")

	// 添加状态过滤条件（当state不为0时）
	if req.State > 0 {
		query = query.Where("state = ?", req.State)
	}

	// 执行查询并处理错误
	result, err := util.WrapPageQuery(ctx, query, req.PaginationParam, &orders)
	if err != nil {
		return nil, nil, err
	}

	return orders, result, nil
}

// OrderDetails 订单详情
func (serv *ScanOrderService) OrderDetails(ctx context.Context, orderID, wechatUserID int64) ([]*model.OrderDetailModel, error) {
	var details []*model.OrderDetailModel
	return details, util.GetDB(ctx, serv.DB).
		Model(&model.OrderDetailModel{}).
		Select("order_details.*").
		Joins("left join orders on orders.id = order_details.order_id").
		Where("order_id = ? AND orders.wechat_user_id = ?", orderID, wechatUserID).
		Preload("Food").
		Find(&details).Error
}

package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// RefundBatchService 退款批次服务
type RefundBatchService struct {
	DB *gorm.DB
}

// GetRefundBatch 根据ID获取退款批次
func (serv *RefundBatchService) GetRefundBatch(ctx context.Context, merchantNo string, batchID int64) (*model.RefundBatchModel, error) {
	batch := model.RefundBatchModel{}
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("merchant_no =? AND id =?", merchantNo, batchID), &batch)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &batch, nil
}

// CreateRefundBatch 创建退款批次
func (serv *RefundBatchService) CreateRefundBatch(ctx context.Context,
	orderId int64, merchantNo string, totalAmount float64, cashierId int64,
) (*model.RefundBatchModel, error) {
	batchNo := util.SerialNumber(consts.RefundBatchPrefix)
	refundBatch := model.RefundBatchModel{
		BatchNo:      batchNo,
		MerchantNo:   merchantNo,
		OrderID:      orderId,
		TotalRefunds: 0,
		TotalAmount:  int64(util.MultiplyFloat(totalAmount, 100)), // 单位：分
		Status:       0,
		CashierID:    cashierId,
	}
	return &refundBatch, util.GetDB(ctx, serv.DB).Create(&refundBatch).Error
}

// UpdateRefundCount 更新退款数量
func (serv *RefundBatchService) UpdateRefundCount(ctx context.Context, merchantNo string, batchID int64, count int) error {
	return util.GetDB(ctx, serv.DB).
		Model(&model.RefundBatchModel{}).
		Where("merchant_no =? AND id =?", merchantNo, batchID).
		Update("total_refunds", count).Error
}

// UpdateRefundStatus 更新退款状态
func (serv *RefundBatchService) UpdateRefundStatus(ctx context.Context, merchantNo string, batchID int64, refundResult *schema.RefundResult) error {

	batch, err := serv.GetRefundBatch(ctx, merchantNo, batchID)
	if err != nil {
		return err
	}

	if batch.Status == model.RefundBatchStatusDone {
		return nil
	}

	if refundResult.Status == consts.REFUND_STATUS_SUCCESS {
		finishCount := int64(0)
		err = util.GetDB(ctx, serv.DB).
			Model(&model.MerchantRefundLogModel{}).
			Where("merchant_no =? AND batch_id =?", merchantNo, batchID).
			Where("refund_status =?", consts.REFUND_STATUS_SUCCESS).
			Count(&finishCount).Error
		if err != nil {
			return err
		}
		if finishCount == batch.TotalRefunds {
			return util.GetDB(ctx, serv.DB).
				Model(&model.RefundBatchModel{}).
				Where("merchant_no =? AND id =?", merchantNo, batchID).
				Update("status", model.RefundBatchStatusDone).Error
		} else {
			if finishCount > 0 {
				return util.GetDB(ctx, serv.DB).
					Model(&model.RefundBatchModel{}).
					Where("merchant_no =? AND id =?", merchantNo, batchID).
					Update("status", model.RefundBatchStatusProcessing).Error
			}
		}

	}
	return nil

}

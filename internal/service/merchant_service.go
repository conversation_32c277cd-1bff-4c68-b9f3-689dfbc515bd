package service

import (
	"context"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"

	"gorm.io/gorm"
)

type MerchantService struct {
	DB *gorm.DB
}

// GetUserActiveMerchants 获取用户激活的商户列表
func (serv *MerchantService) GetUserActiveMerchants(ctx context.Context, userID int64) ([]*model.MerchantModel, error) {
	merchants := make([]*model.MerchantModel, 0)
	// 获取符合条件的 merchant_no
	err := util.GetDB(ctx, serv.DB).
		Select("merchants.*").
		Joins("INNER JOIN merchant_employees me ON merchants.no = me.merchant_no").
		Where("merchants.state = ?", model.MerchantStateActive).
		Where("me.user_id = ? AND me.state = ?", userID, model.EmployeeStateActive).
		Preload("LocalServer", "status =?", model.ServerActivated).
		Find(&merchants).Error

	if err != nil {
		return nil, err
	}

	return merchants, nil

}

// GetUserActiveMerchant 根据用户ID和商户号获取激活的商户信息
func (serv *MerchantService) GetUserActiveMerchant(
	ctx context.Context,
	userID int64,
	merchantNo string) (*model.MerchantModel, error) {
	var merchant model.MerchantModel
	db := util.GetDB(ctx, serv.DB).
		Select("merchants.*").
		Joins("INNER JOIN merchant_employees me ON merchants.no = me.merchant_no").
		Where("merchants.state = ?", model.MerchantStateActive).
		Where("merchants.no = ?", merchantNo).
		Where("me.user_id = ? AND me.state = ?", userID, model.EmployeeStateActive)

	ok, err := util.FindOne(ctx, db, &merchant)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}

	return &merchant, nil

}

func (serv *MerchantService) MustGetUserActiveMerchant(ctx context.Context, userID int64, merchantNo string) (*model.MerchantModel, error) {
	merchant, err := serv.GetUserActiveMerchant(ctx, userID, merchantNo)
	if err != nil {
		return nil, err
	}
	if merchant == nil {
		return nil, errors.BadRequest("", "UserNotBindMerchantOrMerchantNotActive")
	}
	return merchant, nil
}

// GetByMerchantNo 根据商户号获取商户信息
func (serv *MerchantService) GetByMerchantNo(ctx context.Context, merchantNo string) (*model.MerchantModel, error) {
	var item model.MerchantModel
	err := util.GetDB(ctx, serv.DB).Where("no =?", merchantNo).First(&item).Error
	if err != nil {
		return nil, err
	}
	return &item, nil
}

// DecreaseSMSCount 减少商户短信次数
func (serv *MerchantService) DecreaseSMSCount(ctx context.Context, merchantNo string) error {
	return util.GetDB(ctx, serv.DB).Model(&model.MerchantModel{}).Where("no = ?", merchantNo).Update("sms_count", gorm.Expr("sms_count - 1")).Error
}

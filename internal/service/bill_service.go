package service

import (
	"context"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request/bill_request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"

	"gorm.io/gorm"
)

type BillService struct {
	DB *gorm.DB
}
type BillTotals struct {
	TotalAmount float64 `json:"total_amount"`
	RealAmount  float64 `json:"real_amount"`
}

func (serv *BillService) GetTotalAmounts(ctx context.Context, req *bill_request.BillListRequest) (*BillTotals, error) {
	var err error
	var total BillTotals
	db := util.GetDB(ctx, serv.DB).
		Model(model.OrderModel{}).
		Select("sum(original_price) as total_amount, sum(price) as real_amount").
		Where("state = ?", consts.ORDER_STATE_PAID)
	if req.TableName != "" {
		var tableIds []int64
		tableLike := req.TableName + "%"
		err = util.GetDB(ctx, serv.DB).Model(model.TableModel{}).
			Where("merchant_no = ?", req.MerchantNo).
			Where("name_zh LIKE ? or name_ug LIKE ?", tableLike, tableLike).
			Pluck("id", &tableIds).Error
		if err != nil {
			return nil, err
		}
		if len(tableIds) == 0 {
			return nil, nil
		}
		db = db.Where("table_id IN (?)", tableIds)
	}
	if req.MerchantNo != "" {
		db = db.Where("merchant_no = ?", req.MerchantNo)
	}
	if req.BeginAt != nil {
		db = db.Where("paid_at >= ?", req.BeginAt)
	}
	if req.EndAt != nil {
		db = db.Where("paid_at <= ?", req.EndAt)
	}
	if req.CashierID > 0 {
		db = db.Where("cashier_id = ?", req.CashierID)
	}

	db.First(&total)
	return &total, nil
}

func (serv *BillService) GetList(ctx context.Context, req *bill_request.BillListRequest) ([]*model.OrderModel, *util.PaginationResult, error) {
	var err error
	db := util.GetDB(ctx, serv.DB).
		Select("id,no,original_price,price,ignore_price,tax_ticket,refund_price,cashier_id,"+
			"table_id,merchant_no,paid_at,created_at,updated_at, "+
			"(SELECT COALESCE(SUM(total_price), 0) FROM order_details WHERE order_id = orders.id AND state = ?) as canceled_amount", consts.ORDER_DETAIL_STATE_CANCEL).
		Model(model.OrderModel{}).
		Preload("Cashier").
		Preload("Table").
		Where("state in (?)", []int64{consts.ORDER_STATE_PAID, consts.ORDER_STATE_CANCEL}).
		Order("paid_at desc")
	var orders []*model.OrderModel
	if req.TableName != "" {
		var tableIds []int64
		tableLike := req.TableName + "%"
		err = util.GetDB(ctx, serv.DB).Model(model.TableModel{}).
			Where("merchant_no = ?", req.MerchantNo).
			Where("name_zh LIKE ? or name_ug LIKE ?", tableLike, tableLike).
			Pluck("id", &tableIds).Error
		if err != nil {
			return nil, nil, err
		}
		if len(tableIds) == 0 {
			return nil, nil, nil
		}
		db = db.Where("table_id IN (?)", tableIds)
	}
	if req.MerchantNo != "" {
		db = db.Where("merchant_no = ?", req.MerchantNo)
	}
	if req.BeginAt != nil {
		db = db.Where("paid_at >= ?", req.BeginAt)
	}
	if req.EndAt != nil {
		db = db.Where("paid_at <= ?", req.EndAt)
	}
	if req.CashierID > 0 {
		db = db.Where("cashier_id = ?", req.CashierID)
	}
	if req.PaymentTypeID > 0 {
		db = db.Where("exists (select 1 from merchant_payments mp where mp.order_id = orders.id and mp.payment_type_id = ?)", req.PaymentTypeID)
	}

	pageResult, err := util.WrapPageQuery(ctx, db, req.PaginationParam, &orders)
	if err != nil {
		return nil, nil, err
	}

	return orders, pageResult, nil
}

func (serv *BillService) GetDetail(ctx context.Context, orderId int64, merchantNo string) (*model.OrderModel, error) {
	var orderModel model.OrderModel
	db := util.GetDB(ctx, serv.DB).
		Model(model.OrderModel{}).
		Preload("Cashier").
		Preload("Creator").
		Preload("Table").
		Preload("Details.Food").
		Preload("Details.Creator").
		Preload("RefundBatches.OrderRefundLogs.Food").
		Preload("RefundBatches.RefundLogs.PaymentType").
		Preload("Payments", func(db *gorm.DB) *gorm.DB {
			return db. // 只获取支付成功的
					Select("p.*, SUM(r.amount) AS refund_amount").
					Table("merchant_payments p").
					Joins("LEFT JOIN merchant_refund_logs r ON p.payment_no = r.payment_no").
					Where("status = ?", consts.PAY_STATUS_PAID).
					Group("p.id")
		}).
		Preload("Payments.PaymentType").
		Order("paid_at desc").
		Where("id = ? and merchant_no = ?", orderId, merchantNo)
	ok, err := util.FindOne(ctx, db, &orderModel)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.NotFound("", "OrderNotFound")
	}
	return &orderModel, nil
}

package printer

var uyMap map[rune]byte

func init() {
	uyMap = make(map[rune]byte)

	uyMap['('] = 122
	uyMap[')'] = 121
	uyMap['ﻼ'] = 133
	uyMap['ﺌ'] = 134
	uyMap['ﯥ'] = 135
	uyMap['ﯧ'] = 136
	uyMap['ﯦ'] = 137
	uyMap['ﻰ'] = 123 // 138
	uyMap['ﯩ'] = 139
	uyMap['ﯨ'] = 140
	uyMap['ﻲ'] = 124 // 141
	uyMap['ﻴ'] = 142
	uyMap['ﻳ'] = 143
	uyMap['ﺎ'] = 144
	uyMap['ﻪ'] = 145
	uyMap['ﺐ'] = 146
	uyMap['ﺒ'] = 147
	uyMap['ﺑ'] = 148
	uyMap['ﭗ'] = 149
	uyMap['ﭙ'] = 150
	uyMap['ﭘ'] = 151
	uyMap['ﺖ'] = 152
	uyMap['ﺘ'] = 153
	uyMap['ﺗ'] = 154
	uyMap['ﺞ'] = 155
	uyMap['ﺠ'] = 156
	uyMap['ﺟ'] = 157
	uyMap['ﭻ'] = 158
	uyMap['ﭽ'] = 159
	uyMap['ﭼ'] = 160
	uyMap['ﺦ'] = 161
	uyMap['ﺨ'] = 162
	uyMap['ﺧ'] = 163
	uyMap['ﺪ'] = 164
	uyMap['ﺮ'] = 165
	uyMap['ﺰ'] = 166
	uyMap['ﮋ'] = 167
	uyMap['ﺲ'] = 168
	uyMap['ﺴ'] = 169
	uyMap['ﺳ'] = 170
	uyMap['ﺶ'] = 171
	uyMap['ﺸ'] = 172
	uyMap['ﺷ'] = 173
	uyMap['ﻎ'] = 174
	uyMap['ﻐ'] = 175
	uyMap['ﻏ'] = 176
	uyMap['ﻒ'] = 177
	uyMap['ﻔ'] = 178
	uyMap['ﻓ'] = 179
	uyMap['ﻖ'] = 180
	uyMap['ﻘ'] = 181
	uyMap['ﻗ'] = 182
	uyMap['ﻚ'] = 183
	uyMap['ﻜ'] = 184
	uyMap['ﻛ'] = 185
	uyMap['ﮓ'] = 186
	uyMap['ﮕ'] = 187
	uyMap['ﮔ'] = 188
	uyMap['ﯔ'] = 189
	uyMap['ﯖ'] = 190
	uyMap['ﯕ'] = 191
	uyMap['ﻞ'] = 192
	uyMap['ﻠ'] = 193
	uyMap['ﻟ'] = 194
	uyMap['ﻢ'] = 195
	uyMap['ﻤ'] = 196
	uyMap['ﻣ'] = 197
	uyMap['ﻦ'] = 198
	uyMap['ﻨ'] = 199
	uyMap['ﻧ'] = 200
	uyMap['ﮭ'] = 201
	uyMap['ﻮ'] = 202
	uyMap['ﯘ'] = 203
	uyMap['ﯚ'] = 204
	uyMap['ﯜ'] = 205
	uyMap['ﯟ'] = 206
	uyMap['ﯤ'] = 207
	uyMap['ﺍ'] = 208
	uyMap['ﻩ'] = 209
	uyMap['ﺏ'] = 210
	uyMap['ﭖ'] = 211
	uyMap['ﺕ'] = 212
	uyMap['ﺝ'] = 213
	uyMap['ﭺ'] = 214
	uyMap['ﺥ'] = 215
	uyMap['ﺩ'] = 216
	uyMap['ﺭ'] = 217
	uyMap['ﺯ'] = 218
	uyMap['ﮊ'] = 219
	uyMap['ﺱ'] = 220
	uyMap['ﺵ'] = 221
	uyMap['ﻍ'] = 222
	uyMap['ﻑ'] = 223
	uyMap['ﻕ'] = 224
	uyMap['ﻙ'] = 225
	uyMap['ﮒ'] = 226
	uyMap['ﯓ'] = 227
	uyMap['ﻝ'] = 228
	uyMap['ﻡ'] = 229
	uyMap['ﻥ'] = 126 // 230
	uyMap['ﮬ'] = 231
	uyMap['ﻭ'] = 232
	uyMap['ﯗ'] = 233
	uyMap['ﯙ'] = 234
	uyMap['ﯛ'] = 235
	uyMap['ﯞ'] = 125 // 236
	uyMap['ﯤ'] = 237
	uyMap['ﻯ'] = 238
	uyMap['ﻱ'] = 239
	uyMap['ﻻ'] = 240
	uyMap['؟'] = 241
	uyMap['ﺋ'] = 242
	uyMap['،'] = 127
	uyMap[','] = 127
	uyMap['，'] = 127

	// Additional mappings from the bottom
	uyMap['ا'] = 208
	uyMap['ى'] = 238
	uyMap['ە'] = 209
	uyMap['ۇ'] = 233
	uyMap['ن'] = 126
	uyMap['ۈ'] = 235
	uyMap['ر'] = 217
	uyMap['ت'] = 152
	uyMap['خ'] = 215
	uyMap['م'] = 195
	uyMap['ش'] = 171
	uyMap['پ'] = 211
	uyMap['ك'] = 225
}

func Get(c rune, isCloud bool) byte {
	val := uyMap[c]

	if isCloud {
		switch val {
		case 123:
			val = 138
		case 124:
			val = 141
		case 125:
			val = 236
		case 126:
			val = 230
		}
	}
	return val
}

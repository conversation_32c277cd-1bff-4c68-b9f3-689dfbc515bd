package printer

import (
	"bytes"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
	"strings"
)

// PrintText 用于处理维吾尔文打印文本的类
type PrintText struct {
	maxCharInLine int
	align         int
	textType      int
	isZh          bool
	buffer        []byte
	isCloud       bool
}

// Model 常量定义
const (
	// 对齐方式
	ALIGN_LEFT   = 48
	ALIGN_CENTER = 49
	ALIGN_RIGHT  = 50

	// 文本类型
	TEXT_TYPE_TITLE    = 0
	TEXT_TYPE_BODY     = 1
	TEXT_TYPE_BODY_BIG = 2

	// 每行最大字符数
	MAX_CHAR_IN_LINE_80 = 46
	MAX_CHAR_IN_LINE_56 = 32
)

// NewPrintText 创建一个新的PrintText实例
func NewPrintText(isCloud bool) *PrintText {
	p := &PrintText{
		maxCharInLine: MAX_CHAR_IN_LINE_56,
		align:         ALIGN_RIGHT,
		textType:      TEXT_TYPE_BODY,
		isZh:          true,
		buffer:        make([]byte, 0),
		isCloud:       isCloud,
	}

	// 初始化打印机
	p.write([]byte{27, 64}) // 初始化打印机
	//p.write([]byte{27, 100, 3})  // 设置打印浓度
	p.write([]byte{27, 116, 30}) // 设置字符集
	p.beautify()                 // 美化打印效果

	return p
}

// SetLang 设置语言
func (p *PrintText) SetLang(lang string) *PrintText {
	p.isZh = lang == "zh"
	return p
}

// SetPrinterSize 设置打印机尺寸
func (p *PrintText) SetPrinterSize(isPrint80 bool) *PrintText {
	if isPrint80 {
		p.maxCharInLine = MAX_CHAR_IN_LINE_80
	} else {
		p.maxCharInLine = MAX_CHAR_IN_LINE_56
	}
	return p
}

// SetAlign 设置对齐方式
func (p *PrintText) SetAlign(align int) *PrintText {
	p.align = align
	return p
}

// SetTextType 设置文本类型
func (p *PrintText) SetTextType(textType int) *PrintText {
	p.textType = textType
	return p
}

// Line 打印一条线
func (p *PrintText) Line() *PrintText {
	tempTextType := p.textType
	p.SetTextType(TEXT_TYPE_BODY)
	p.printTextType()

	lineChars := make([]byte, p.maxCharInLine)
	for i := range lineChars {
		lineChars[i] = '-'
	}

	p.write(lineChars)
	p.write([]byte{'\n'})
	p.SetTextType(tempTextType)
	return p
}

// GetSpaceLength 获取字符串占用的空间长度
func (p *PrintText) GetSpaceLength(str string) float64 {
	count := 0.0
	if str == "" {
		return count
	}

	for _, c := range str {
		isChinese := c >= '\u4e00' && c <= '\u9fa5'
		if c == '￥' || c == '（' || c == '）' {
			isChinese = true
		}
		if isChinese {
			count += 2
		} else {
			count += 1
		}
	}
	return count
}

// getSpace 获取指定数量的空格
func (p *PrintText) getSpace(count int) string {
	if count <= 0 {
		return ""
	}
	return strings.Repeat(" ", count)
}

// Cut 切纸
func (p *PrintText) Cut() *PrintText {
	p.write([]byte{29, 86, 1})
	return p
}

// Feed 进纸
func (p *PrintText) Feed(count int) *PrintText {
	if count > 0 {
		for i := 0; i < count; i++ {
			p.write([]byte{10})
		}
	} else {
		p.write([]byte{10})
	}
	return p
}

// printTextType 打印文本类型
func (p *PrintText) printTextType() {
	if p.textType == TEXT_TYPE_TITLE {
		p.write([]byte{27, 33, 48})
	} else if p.textType == TEXT_TYPE_BODY {
		p.write([]byte{27, 33, 0})
	} else {
		p.write([]byte{27, 33, 16})
	}

	p.write([]byte{27, 97, byte(p.align)})
}

// specialFormat 特殊格式化
func (p *PrintText) specialFormat(text string) string {
	if text != "" && (strings.Contains(text, "-") || strings.Contains(text, "—")) {
		var c string
		if strings.Contains(text, "-") {
			c = "-"
		} else {
			c = "—"
		}

		parts := strings.Split(text, c)
		if len(parts) == 2 {
			before := parts[0]
			after := parts[1]
			if before != "" && after != "" && !isUyghur([]rune(before)[len([]rune(before))-1]) && isUyghur([]rune(after)[0]) {
				text = "-" + before + after
			}
		}
	}
	if text != "" {
		text = strings.ReplaceAll(text, "¥", "￥")
	}
	return text
}

// Text 打印文本
func (p *PrintText) Text(arguments ...string) *PrintText {
	if len(arguments) == 0 {
		return p
	}

	// 格式化参数
	for i := range arguments {
		arguments[i] = p.formatStr(arguments[i])
	}

	args := make([]string, len(arguments))

	if !p.isZh {
		for i := 0; i < len(arguments); i++ {
			args[len(arguments)-1-i] = arguments[i]
		}
	} else {
		args = arguments
	}

	var text strings.Builder
	utils := UyghurCharUtils()

	for _, arg := range args {
		content := utils.Basic2Extend(arg)
		content = p.reversal(content)
		text.WriteString(content)
	}

	p.textWrite(text.String())
	return p
}

// TextAlign 对齐打印文本
func (p *PrintText) TextAlign(content, content2, content3, content4 string) *PrintText {
	content = p.formatStr(content)
	content2 = p.formatStr(content2)
	content3 = p.formatStr(content3)
	content4 = p.formatStr(content4)

	if !p.isZh {
		if content4 != "" {
			temp := content
			temp2 := content2
			content = content4
			content2 = content3
			content4 = temp
			content3 = temp2
		} else if content3 != "" {
			temp := content
			content = content3
			content3 = temp
		} else if content2 != "" {
			temp := content
			content = content2
			content2 = temp
		}
	}

	utils := UyghurCharUtils()
	content = utils.Basic2Extend(content)
	content2 = utils.Basic2Extend(content2)
	content3 = utils.Basic2Extend(content3)
	content4 = utils.Basic2Extend(content4)

	content = p.reversal(content)
	content2 = p.reversal(content2)
	content3 = p.reversal(content3)
	content4 = p.reversal(content4)

	text := ""

	if content2 != "" {
		if content4 != "" {
			var leftSpace, centerSpace, rightSpace string

			if p.isZh {
				leftSpace = p.getSpace(int(float64(p.maxCharInLine)/10.0*5.0 - p.GetSpaceLength(content) - 2))
				centerSpace = p.getSpace(int(float64(p.maxCharInLine)/10.0*2.0 - p.GetSpaceLength(content2)))
				rightSpace = p.getSpace(int(float64(p.maxCharInLine)/10.0*3.0 - p.GetSpaceLength(content3) - p.GetSpaceLength(content4)))
			} else {
				rightSpace = p.getSpace(int(float64(p.maxCharInLine)/10.0*5.0 - p.GetSpaceLength(content4) - 2))
				centerSpace = p.getSpace(int(float64(p.maxCharInLine)/10.0*2.0 - p.GetSpaceLength(content3)))
				leftSpace = p.getSpace(int(float64(p.maxCharInLine)/10.0*3.0 - p.GetSpaceLength(content2) - p.GetSpaceLength(content)))
			}

			length := len(leftSpace+centerSpace+rightSpace) + int(p.GetSpaceLength(content)) + int(p.GetSpaceLength(content2)) + int(p.GetSpaceLength(content3)) + int(p.GetSpaceLength(content4))
			for i := p.maxCharInLine - length; i > 0; i-- {
				centerSpace += " "
			}

			text = content + leftSpace + content2 + centerSpace + content3 + rightSpace + content4
		} else if content3 != "" {
			var rightSpace, leftSpace string

			if p.isZh {
				leftSpace = p.getSpace(int(float64(p.maxCharInLine)/10.0*5.0 - p.GetSpaceLength(content)))
				rightSpace = p.getSpace(int(float64(p.maxCharInLine)/10.0*5.0 - p.GetSpaceLength(content2) - p.GetSpaceLength(content3)))
			} else {
				rightSpace = p.getSpace(int(float64(p.maxCharInLine)/10.0*5.0 - p.GetSpaceLength(content3)))
				leftSpace = p.getSpace(int(float64(p.maxCharInLine)/10.0*5.0 - p.GetSpaceLength(content) - p.GetSpaceLength(content2)))
			}

			text = content + leftSpace + content2 + rightSpace + content3
		} else {
			maxCharInLine := p.maxCharInLine
			if p.textType == TEXT_TYPE_TITLE {
				maxCharInLine = p.maxCharInLine / 2
			}
			text = content + p.getSpace(int(maxCharInLine-int(p.GetSpaceLength(content))-int(p.GetSpaceLength(content2)))) + content2
		}
	}

	p.textWrite(text)
	return p
}

// TextBody 打印正文
func (p *PrintText) TextBody(str string) *PrintText {
	str = p.formatStr(str)
	utils := UyghurCharUtils()
	str = utils.Basic2Extend(str)

	index := 0
	startIndex := 0

	var text strings.Builder
	for startIndex < len([]rune(str)) {
		var nowIndex int
		if p.textType == TEXT_TYPE_TITLE {
			nowIndex = startIndex + p.maxCharInLine/2
		} else {
			nowIndex = startIndex + p.maxCharInLine
		}

		if nowIndex >= len([]rune(str)) {
			text.WriteString(p.reversal(string([]rune(str)[startIndex:])))
			text.WriteByte('\n')
			break
		}

		for j := nowIndex; j >= 0; j-- {
			index = j
			if []rune(str)[j] == ' ' || !isUyghur([]rune(str)[j]) {
				//text.WriteString(p.reversal(str[startIndex:j]))
				text.WriteString(p.reversal(string([]rune(str)[startIndex:j])))
				text.WriteByte('\n')
				break
			}
		}

		if nowIndex == len([]rune(str)) {
			break
		}
		startIndex = index
	}

	p.textWrite(text.String())
	return p
}

// textWrite 写入文本
func (p *PrintText) textWrite(text string) {
	addCancelKanjiMode := []byte{28, 46}
	addSelectKanjiMode := []byte{28, 38}

	p.useFont()
	p.write(addCancelKanjiMode)
	p.addSetCharacterSize(true)
	p.printTextType()
	isKanjiMode := false

	for _, c := range text {
		if isUyghur(c) {
			if isKanjiMode {
				p.useFont()
				p.write(addCancelKanjiMode)
				isKanjiMode = false
				p.addSetCharacterSize(true)
				p.printTextType()
			}
			p.write([]byte{Get(c, p.isCloud)})
		} else {
			if !isKanjiMode {
				p.cancelFont()
				p.write(addSelectKanjiMode)
				isKanjiMode = true
				p.addSetCharacterSize(false)
				p.printTextType()
			}
			// 将字符转换为GB18030编码
			gbBytes, err := p.encodeGB18030(string(c))
			if err == nil {
				p.write(gbBytes)
			} else {
				// 如果编码失败，则使用原始字节
				p.write([]byte(string(c)))
			}
		}
	}
	p.write([]byte{10})
}

// encodeGB18030 将字符串编码为GB18030
func (p *PrintText) encodeGB18030(s string) ([]byte, error) {
	reader := transform.NewReader(strings.NewReader(s), simplifiedchinese.GB18030.NewEncoder())
	buf := new(bytes.Buffer)
	_, err := buf.ReadFrom(reader)
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

// isUyghur 判断是否是维吾尔文字符
func isUyghur(c rune) bool {
	_, ok := uyMap[c]
	return ok
}

// addSetCharacterSize 设置字符大小
func (p *PrintText) addSetCharacterSize(isUg bool) {
	if p.textType == TEXT_TYPE_TITLE && !isUg {
		p.write([]byte{29, 33, 17})
	} else {
		p.write([]byte{29, 33, 0})
	}
}

// reversal 反转字符串
func (p *PrintText) reversal(str string) string {
	if str == "" {
		return str
	}
	str = p.specialFormat(str)

	var cache, rtl strings.Builder
	runes := []rune(str)
	for i := len(runes) - 1; i >= 0; i-- {
		ugChar := true
		if (runes[i] == '，' || runes[i] == ',') && (i-1 >= 0) && !isUyghur(runes[i-1]) {
			ugChar = false
		}
		if isUyghur(runes[i]) && ugChar {
			if rtl.Len() > 0 {
				cache.WriteString(rtl.String())
				rtl.Reset()
			}
			cache.WriteString(string(runes[i]))
		} else {
			// Insert non-Uyghur characters at the beginning of rtl
			rtlStr := rtl.String()
			rtl.Reset()
			rtl.WriteString(string(runes[i]))
			rtl.WriteString(rtlStr)
		}
	}
	if rtl.Len() > 0 {
		cache.WriteString(rtl.String())
	}
	return cache.String()
}

// write 写入字节
func (p *PrintText) write(data []byte) {
	p.buffer = append(p.buffer, data...)
}

// beautify 美化打印效果
func (p *PrintText) beautify() {
	p.write([]byte{27, 37, 1})
	p.write([]byte{27, 38, 3, 121, 127,
		12,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x07, 0xFC, 0x00, 0x0E, 0x0E, 0x00, 0x18,
		0x03, 0x00, 0x30, 0x01, 0x80, 0x60, 0x00, 0xC0, 0x40, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00,
		12,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x60, 0x00, 0xC0, 0x30, 0x01, 0x80, 0x18,
		0x03, 0x00, 0x0E, 0x0E, 0x00, 0x07, 0xFC, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00,
		12,
		0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00,
		0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x01, 0xD8, 0x00, 0x03, 0xD0, 0x00, 0x03, 0x70, 0x00, 0x03,
		0x00, 0x00, 0x03, 0x00,
		12,
		0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x1B, 0x00,
		0x00, 0x18, 0x00, 0x00, 0x1B, 0x00, 0x01, 0xDB, 0x00, 0x03, 0xD0, 0x00, 0x03, 0x70, 0x00, 0x03,
		0x00, 0x00, 0x03, 0x00,
		12,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x01, 0x00, 0x04, 0x03,
		0x8E, 0x0C, 0x05, 0x1F, 0x1C, 0x0E, 0x13, 0x38, 0x05, 0x1B, 0x70, 0x03, 0x8F, 0xC0, 0x01, 0x07,
		0x80, 0x00, 0x00, 0x00,
		12,
		0x00, 0x07, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x1D, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0xC0, 0xC0, 0x01,
		0xC0, 0xC0, 0x00, 0xC0, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x19, 0x80, 0x00, 0x1F, 0x80, 0x00, 0x0E,
		0x00, 0x00, 0x00, 0x00,
		12,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00,
		0x1F, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00})
	p.write([]byte{27, 37, 1})
}

// GetBytes 获取字节数组
func (p *PrintText) GetBytes() []byte {
	return p.buffer
}

// useFont 使用字体
func (p *PrintText) useFont() {
	p.write([]byte{27, 37, 1})
}

// cancelFont 取消字体
func (p *PrintText) cancelFont() {
	p.write([]byte{27, 37, 0})
}

// formatStr 格式化字符串
func (p *PrintText) formatStr(str string) string {
	if p.isCloud {
		if str == "" {
			return str
		}
		str = strings.ReplaceAll(str, "(", "）")
		str = strings.ReplaceAll(str, ")", "（")
	}
	return str
}

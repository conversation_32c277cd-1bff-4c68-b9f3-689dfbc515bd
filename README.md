# ros-api-go

> 智慧餐厅GO接口

## Quick Start

```bash
make start
```

## Build

```bash
make build
```

## Generate wire inject files

```bash
make wire
```

## Generate swagger documents

```bash
make swagger
```

## 商家模式字段mode：0 本地服务模式 1 线上模式
```sql
alter table merchants
    add mode tinyint default 1 not null comment '商家模式：1 本地服务模式(默认) 2 线上模式';

# 服务模式
alter table service_history
drop column service_id;

alter table service_history
    add service_name_ug varchar(255) not null comment '服务名称' after table_id;

alter table service_history
    add service_name_zh varchar(255) not null comment '服务名称' after service_name_ug;


```

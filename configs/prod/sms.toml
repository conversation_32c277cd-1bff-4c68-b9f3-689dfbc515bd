[SMS]
DefaultProvider = "${SMS_DEFAULT_PROVIDER}"  # 默认短信服务提供商: aliyun/zhutong

[SMS.Providers.Aliyun]
AccessKeyId = "${ALIYUN_SMS_ACCESSKEY_ID}"
AccessKeySecret = "${ALIYUN_SMS_ACCESS_KEY_SECRET}"
SignName = "${ALIYUN_SMS_SIGN_NAME}"  # 短信签名
Endpoint = "dysmsapi.aliyuncs.com"  # 接入点

# 短信模板配置
[SMS.Providers.Aliyun.Templates]
ForgotPasswordSMS="SMS_101255079"
UserPasswordSMS="SMS_478550104"
OperationPasswordSMS="SMS_101255078"
CustomerRechargeSMS="SMS_238980755"
CustomerPaySMS="SMS_238975670"

[SMS.Providers.ZhuTong]
AccessKeyId = "${ZHUTONG_SMS_USERNAME}"
AccessKeySecret = "${ZHUTONG_SMS_PASSWORD}"
SignName = "${ZHUTONG_SMS_SIGN_NAME}"
Endpoint = "https://api-shss.zthysms.com/v2/sendSmsTp"

[SMS.Providers.ZhuTong.Templates]
ForgotPasswordSMS = "193301"
UserPasswordSMS = "193324"
OperationPasswordSMS = "193304"
CustomerRechargeSMS = "193321"
CustomerPaySMS = "193322"

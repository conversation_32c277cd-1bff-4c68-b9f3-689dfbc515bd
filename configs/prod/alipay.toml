[Alipay]
RootCert="${ALIPAY_ROOT_CERT}"
PublicCert="${ALIPAY_PUBLIC_CERT}"

## 微信小程序配置
[Alipay.Mini]
AppID = "${ALIPAY_MINI_APP_ID}"  # 微信小程序 AppID
AppPrivateKey = "${ALIPAY_MINI_APP_PRIVATE_KEY}" # 小程序私钥
EncryptKey = "${ALIPAY_MINI_ENCRYPT_KEY}" # 加密密钥
AppCert = "${ALIPAY_MINI_APP_CERT}" # 小程序

## 微信支付配置
[Alipay.Payment]
AppID = "${ALIPAY_PAYMENT_APP_ID}"  # 支付宝开放平台 AppID
AppPrivateKey = "${ALIPAY_PAYMENT_APP_PRIVATE_KEY}" # 应用私钥
EncryptKey = "${ALIPAY_PAYMENT_ENCRYPT_KEY}" # 加密密钥
AppCert = "${ALIPAY_PAYMENT_APP_CERT}" # 应用公钥

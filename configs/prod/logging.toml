[Logger]
Debug = true
Level = "debug" # debug/info/warn/error/dpanic/panic/fatal
CallerSkip = 1

[Logger.File]
Enable = true
Path = "${WORK_DIR}/data/log/rosapigo.log"
MaxBackups = 20 # Files
MaxSize = 64 # MB

[[Logger.Hooks]]
Enable = false
Level = "info"
Type = "gorm" # gorm
MaxBuffer = 1024
MaxThread = 2

[Logger.Hooks.Options]
Debug = "false"
DBType = "sqlite3" # sqlite3/mysql/postgres
DSN = "data/rosapigo.db"
MaxOpenConns = "16"
MaxIdleConns = "4"
MaxLifetime = "86400"
MaxIdleTime = "7200"

## 微信小程序配置
[WechatMini]
AppID = "${WECHAT_APP_ID}"  # 微信小程序 AppID
AppSecret = "${WECHAT_APP_SECRET}" # 微信
MessageToken = ""  # 消息校验 Token
MessageAesKey = ""  # 消息加密密钥

## 微信支付配置
[WechatPayment]
AppID="${WECHAT_APP_ID}"  # 微信支付 AppID
MchID="${WECHAT_MCH_ID}"  # 微信支付商户号
MchApiKey="${WECHAT_MCH_API_KEY}"  # 微信支付 API 密钥
MchApiV3Key="${WECHAT_MCH_API_V3_KEY}"  # 微信支付 API v3 密钥
WechatPaySerial="${WECHAT_MCH_SERIAL}"  # 商户证书序列号
ApiClientCert="${WECHAT_MCH_API_CLIENT_CERT}"
ApiClientKey="${WECHAT_MCH_API_CLIENT_KEY}"
CertificateKeyPath=""  # 平台证书私钥路径
RSAPublicKeyPath=""  # 平台公钥路径
NotifyURL=""

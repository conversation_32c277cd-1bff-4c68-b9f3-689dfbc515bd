[SMS]
DefaultProvider = "aliyun"  # 默认短信服务提供商: aliyun/zhutong

[SMS.Providers.Aliyun]
AccessKeyId = "LTAI5tNPBcygs3d2QLYuRgJH"
AccessKeySecret = "******************************"
SignName = "Mulazim智慧餐厅"  # 短信签名
Endpoint = "dysmsapi.aliyuncs.com"  # 接入点

# 短信模板配置
[SMS.Providers.Aliyun.Templates]
ForgotPasswordSMS="SMS_101255079"
UserPasswordSMS="SMS_478550104"
OperationPasswordSMS="SMS_101255078"
CustomerRechargeSMS="SMS_238980755"
CustomerPaySMS="SMS_238975670"

[SMS.Providers.ZhuTong]
AccessKeyId = "xjjz888hy"
AccessKeySecret = "yH!498fK"
SignName = "Mulazim收银端"
Endpoint = "https://api-shss.zthysms.com/v2/sendSmsTp"

[SMS.Providers.ZhuTong.Templates]
ForgotPasswordSMS = "193301"
UserPasswordSMS = "193324"
OperationPasswordSMS = "193304"
CustomerRechargeSMS = "193321"
CustomerPaySMS = "193322"

[Middleware]

[Middleware.Recovery]
Skip = 3

[Middleware.CORS]
Enable = false
AllowOrigins = ["*"]
AllowMethods = ["GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"]
AllowHeaders = ["*"]
MaxAge = 86400
AllowWildcard = true
AllowWebSockets = true
AllowFiles = true

[Middleware.Trace]
RequestHeaderKey = "X-Request-Id"
ResponseTraceKey = "X-Trace-Id"

[Middleware.Logger]
MaxOutputRequestBodyLen = 10240 #  bytes = 10KB
MaxOutputResponseBodyLen = 10240 # bytes = 10KB

[Middleware.CopyBody]
MaxContentLen = 134217728 # 128MB

[Middleware.Auth]
Disable = false
SigningMethod = "HS256" # HS256/HS384/HS512
SigningKey = "zS4shmnleTXV8Gq6bNQwcx2RFL5yuO1doWCja9PEvg7rfHKBk0JptD3iIZUAMYZz" # Secret key
OldSigningKey = "lnAFCgkudv0sHh6b" # Old secret key (For change secret key)
Expired = 1296000 # seconds  1296000 seconds = 15 days
RefreshExpired = 2592000 # seconds 2592000 seconds = 30 days

[Middleware.ServerAuth]
Disable = false

[Middleware.Auth.Store]
Type = "redis" # memory/badger/redis
Delimiter = ":"

[Middleware.Auth.Store.Memory]
CleanupInterval = 60 # seconds

[Middleware.Auth.Store.Badger]
Path = "data/auth"

[Middleware.Auth.Store.Redis]
Addr = "" # If empty, then use the same configuration as Storage.Cache.Redis
Username = ""
Password = ""
DB = 0

[Middleware.RateLimiter]
Enable = false
Period = 10 # seconds
MaxRequestsPerIP = 1000
MaxRequestsPerUser = 500

[Middleware.RateLimiter.Store]
Type = "memory" # memory/redis

[Middleware.RateLimiter.Store.Memory]
Expiration = 3600
CleanupInterval = 60

[Middleware.RateLimiter.Store.Redis]
Addr = "" # If empty, then use the same configuration as Storage.Cache.Redis
Username = ""
Password = ""
DB = 10

[Middleware.Casbin]
Disable = false
LoadThread = 2
AutoLoadInterval = 3 # seconds
ModelFile = "rbac_model.conf"
GenPolicyFile = "gen_rbac_policy.csv"

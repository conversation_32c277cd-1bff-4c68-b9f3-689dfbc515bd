[{"code": "home", "name": "Home", "sequence": 9, "type": "page", "path": "/home", "status": "enabled"}, {"code": "system", "name": "System", "sequence": 8, "type": "page", "path": "/system", "status": "enabled", "children": [{"code": "menu", "name": "<PERSON><PERSON>", "sequence": 9, "type": "page", "path": "/system/menu", "status": "enabled", "children": [{"code": "add", "name": "Add", "sequence": 9, "type": "button", "status": "enabled", "resources": [{"method": "POST", "path": "/api/v1/menus"}]}, {"code": "edit", "name": "Edit", "sequence": 8, "type": "button", "status": "enabled", "resources": [{"method": "PUT", "path": "/api/v1/menus/{id}"}]}, {"code": "delete", "name": "Delete", "sequence": 7, "type": "button", "status": "enabled", "resources": [{"method": "DELETE", "path": "/api/v1/menus/{id}"}]}, {"code": "search", "name": "Search", "sequence": 6, "type": "button", "status": "enabled"}], "resources": [{"method": "GET", "path": "/api/v1/menus"}, {"method": "GET", "path": "/api/v1/menus/{id}"}]}, {"code": "role", "name": "Role", "sequence": 8, "type": "page", "path": "/system/role", "status": "enabled", "children": [{"code": "add", "name": "Add", "sequence": 9, "type": "button", "status": "enabled", "resources": [{"method": "POST", "path": "/api/v1/roles"}]}, {"code": "edit", "name": "Edit", "sequence": 8, "type": "button", "status": "enabled", "resources": [{"method": "PUT", "path": "/api/v1/roles/{id}"}]}, {"code": "delete", "name": "Delete", "sequence": 7, "type": "button", "status": "enabled", "resources": [{"method": "DELETE", "path": "/api/v1/roles/{id}"}]}, {"code": "search", "name": "Search", "sequence": 6, "type": "button", "status": "enabled"}], "resources": [{"method": "GET", "path": "/api/v1/menus"}, {"method": "GET", "path": "/api/v1/roles"}, {"method": "GET", "path": "/api/v1/roles/{id}"}]}, {"code": "user", "name": "User", "sequence": 7, "type": "page", "path": "/system/user", "status": "enabled", "children": [{"code": "add", "name": "Add", "sequence": 9, "type": "button", "status": "enabled", "resources": [{"method": "POST", "path": "/api/v1/users"}]}, {"code": "edit", "name": "Edit", "sequence": 8, "type": "button", "status": "enabled", "resources": [{"method": "PUT", "path": "/api/v1/users/{id}"}]}, {"code": "delete", "name": "Delete", "sequence": 7, "type": "button", "status": "enabled", "resources": [{"method": "DELETE", "path": "/api/v1/users/{id}"}]}, {"code": "search", "name": "Search", "sequence": 6, "type": "button", "status": "enabled"}], "resources": [{"method": "GET", "path": "/api/v1/roles"}, {"method": "GET", "path": "/api/v1/users"}, {"method": "GET", "path": "/api/v1/users/{id}"}]}, {"code": "logger", "name": "<PERSON><PERSON>", "sequence": 1, "type": "page", "path": "/system/logger", "status": "enabled", "resources": [{"method": "GET", "path": "/api/v1/loggers"}]}]}]